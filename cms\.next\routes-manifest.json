{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/blog/slug/[slug]", "regex": "^/api/blog/slug/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/blog/slug/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/blog/[id]", "regex": "^/api/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/blog/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/clients/[id]", "regex": "^/api/clients/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/clients/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/public/blog/[slug]", "regex": "^/api/public/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/public/blog/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/reels/[id]", "regex": "^/api/reels/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/reels/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/testimonials/[id]", "regex": "^/api/testimonials/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/testimonials/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/videos/[id]", "regex": "^/api/videos/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/videos/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/blog/[id]", "regex": "^/dashboard/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/blog/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/clients/[id]", "regex": "^/dashboard/clients/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/clients/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/reels/[id]", "regex": "^/dashboard/reels/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/reels/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/testimonials/[id]", "regex": "^/dashboard/testimonials/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/testimonials/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/videos/[id]", "regex": "^/dashboard/videos/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/videos/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/blog", "regex": "^/dashboard/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/blog(?:/)?$"}, {"page": "/dashboard/blog/new", "regex": "^/dashboard/blog/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/blog/new(?:/)?$"}, {"page": "/dashboard/clients", "regex": "^/dashboard/clients(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/clients(?:/)?$"}, {"page": "/dashboard/clients/new", "regex": "^/dashboard/clients/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/clients/new(?:/)?$"}, {"page": "/dashboard/reels", "regex": "^/dashboard/reels(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reels(?:/)?$"}, {"page": "/dashboard/reels/new", "regex": "^/dashboard/reels/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reels/new(?:/)?$"}, {"page": "/dashboard/testimonials", "regex": "^/dashboard/testimonials(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/testimonials(?:/)?$"}, {"page": "/dashboard/testimonials/new", "regex": "^/dashboard/testimonials/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/testimonials/new(?:/)?$"}, {"page": "/dashboard/videos", "regex": "^/dashboard/videos(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/videos(?:/)?$"}, {"page": "/dashboard/videos/new", "regex": "^/dashboard/videos/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/videos/new(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/setup", "regex": "^/setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}