"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5812],{1275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2115),o=n(2712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},1414:(e,t,n)=>{e.exports=n(2436)},1497:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2432:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},2436:(e,t,n)=>{var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),a(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},2657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let r=n(9991),o=n(7102);function i(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let r=n(6966)._(n(8859)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:n}=e,i=e.protocol||"",a=e.pathname||"",l=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),l&&"#"!==l[0]&&(l="#"+l),s&&"?"!==s[0]&&(s="?"+s),""+i+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return i(e)}},3052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>l});var r=n(2115),o=n(7650),i=n(9708),a=n(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},3795:(e,t,n)=>{n.d(t,{A:()=>K});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),m=function(){},v=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],g=p[1],y=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,R=e.sideCar,A=e.noRelative,T=e.noIsolation,k=e.inert,S=e.allowPinchZoom,N=e.as,P=e.gapMode,L=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),M=i(i({},L),v);return l.createElement(l.Fragment,null,E&&l.createElement(R,{sideCar:h,removeScrollBar:x,shards:C,noRelative:A,noIsolation:T,inert:k,setCallbacks:g,allowPinchZoom:!!S,lockRef:c,gapMode:P}),y?l.cloneElement(l.Children.only(b),i(i({},M),{ref:O})):l.createElement(void 0===N?"div":N,i({},M,{className:w,ref:O}),b))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=w(),T="data-scroll-locked",k=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(T,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(T,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},S=function(){var e=parseInt(document.body.getAttribute(T)||"0",10);return isFinite(e)?e:0},N=function(){l.useEffect(function(){return document.body.setAttribute(T,(S()+1).toString()),function(){var e=S()-1;e<=0?document.body.removeAttribute(T):document.body.setAttribute(T,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var i=l.useMemo(function(){return R(o)},[o]);return l.createElement(A,{styles:k(i,!t,o,n?"":"!important")})},L=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return L=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){L=!1}var M=!!L&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{var h=_(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&I(e,u)&&(f+=v,p+=m),u=u.parentNode.host||u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},H=function(e){return e&&"current"in e?e.current:e},U=0,z=[];let q=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(U++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(H),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(z.length&&z[z.length-1]===i){var n="deltaY"in e?B(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(H).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,M),document.addEventListener("touchmove",c,M),document.addEventListener("touchstart",d,M),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,M),document.removeEventListener("touchmove",c,M),document.removeEventListener("touchstart",d,M)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var G=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:q}))});G.classNames=v.classNames;let K=G},3921:(e,t,n)=>{n.d(t,{i3:()=>$,UC:()=>Y,ZL:()=>X,Kq:()=>G,bL:()=>K,l9:()=>V});var r=n(2115),o=n(5185),i=n(6101),a=n(6081),l=n(9178),u=n(1285),c=n(5152),s=n(4378),d=n(8905),f=n(3655),p=n(9708),h=n(5845),m=n(5155),v=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),g=r.forwardRef((e,t)=>(0,m.jsx)(f.sG.span,{...e,ref:t,style:{...v,...e.style}}));g.displayName="VisuallyHidden";var[y,b]=(0,a.A)("Tooltip",[c.Bk]),w=(0,c.Bk)(),x="TooltipProvider",E="tooltip.open",[C,R]=y(x),A=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,l=r.useRef(!0),u=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(C,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),l.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:a})};A.displayName=x;var T="Tooltip",[k,S]=y(T),N=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:a,disableHoverableContent:l,delayDuration:s}=e,d=R(T,e.__scopeTooltip),f=w(t),[p,v]=r.useState(null),g=(0,u.B)(),y=r.useRef(0),b=null!=l?l:d.disableHoverableContent,x=null!=s?s:d.delayDuration,C=r.useRef(!1),[A,S]=(0,h.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(E))):d.onClose(),null==a||a(e)},caller:T}),N=r.useMemo(()=>A?C.current?"delayed-open":"instant-open":"closed",[A]),P=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,C.current=!1,S(!0)},[S]),L=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,S(!1)},[S]),O=r.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{C.current=!0,S(!0),y.current=0},x)},[x,S]);return r.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,m.jsx)(c.bL,{...f,children:(0,m.jsx)(k,{scope:t,contentId:g,open:A,stateAttribute:N,trigger:p,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?O():P()},[d.isOpenDelayedRef,O,P]),onTriggerLeave:r.useCallback(()=>{b?L():(window.clearTimeout(y.current),y.current=0)},[L,b]),onOpen:P,onClose:L,disableHoverableContent:b,children:n})})};N.displayName=T;var P="TooltipTrigger",L=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,l=S(P,n),u=R(P,n),s=w(n),d=r.useRef(null),p=(0,i.s)(t,d,l.onTriggerChange),h=r.useRef(!1),v=r.useRef(!1),g=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,m.jsx)(c.Mz,{asChild:!0,...s,children:(0,m.jsx)(f.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||u.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});L.displayName=P;var O="TooltipPortal",[M,j]=y(O,{forceMount:void 0}),D=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=S(O,t);return(0,m.jsx)(M,{scope:t,forceMount:n,children:(0,m.jsx)(d.C,{present:n||i.open,children:(0,m.jsx)(s.Z,{asChild:!0,container:o,children:r})})})};D.displayName=O;var I="TooltipContent",_=r.forwardRef((e,t)=>{let n=j(I,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,a=S(I,e.__scopeTooltip);return(0,m.jsx)(d.C,{present:r||a.open,children:a.disableHoverableContent?(0,m.jsx)(U,{side:o,...i,ref:t}):(0,m.jsx)(F,{side:o,...i,ref:t})})}),F=r.forwardRef((e,t)=>{let n=S(I,e.__scopeTooltip),o=R(I,e.__scopeTooltip),a=r.useRef(null),l=(0,i.s)(t,a),[u,c]=r.useState(null),{trigger:s,onClose:d}=n,f=a.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{c(null),p(!1)},[p]),v=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(s&&f){let e=e=>v(e,f),t=e=>v(e,s);return s.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{s.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[s,f,v,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==s?void 0:s.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}(n,u);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[s,f,u,d,h]),(0,m.jsx)(U,{...e,ref:l})}),[W,B]=y(T,{isInside:!1}),H=(0,p.Dc)("TooltipContent"),U=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:u,...s}=e,d=S(I,n),f=w(n),{onClose:p}=d;return r.useEffect(()=>(document.addEventListener(E,p),()=>document.removeEventListener(E,p)),[p]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,m.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,m.jsxs)(c.UC,{"data-state":d.stateAttribute,...f,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(H,{children:o}),(0,m.jsx)(W,{scope:n,isInside:!0,children:(0,m.jsx)(g,{id:d.contentId,role:"tooltip",children:i||o})})]})})});_.displayName=I;var z="TooltipArrow",q=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=w(n);return B(z,n).isInside?null:(0,m.jsx)(c.i3,{...o,...r,ref:t})});q.displayName=z;var G=A,K=N,V=L,X=D,Y=_,$=q},4011:(e,t,n)=>{n.d(t,{H4:()=>E,bL:()=>x});var r=n(2115),o=n(6081),i=n(9033),a=n(2712),l=n(3655),u=n(1414);function c(){return()=>{}}var s=n(5155),d="Avatar",[f,p]=(0,o.A)(d),[h,m]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.sG.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage";r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(g,n),h=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,i=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),l=r.useRef(null),s=i?(l.current||(l.current=new window.Image),l.current):null,[d,f]=r.useState(()=>w(s,e));return(0,a.N)(()=>{f(w(s,e))},[s,e]),(0,a.N)(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),r=e("error");return s.addEventListener("load",t),s.addEventListener("error",r),n&&(s.referrerPolicy=n),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",t),s.removeEventListener("error",r)}},[s,o,n]),d}(o,f),v=(0,i.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(l.sG.img,{...f,ref:t,src:o}):null}).displayName=g;var y="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=m(y,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.sG.span,{...i,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=y;var x=v,E=b},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),i=n(3655),a=n(2712),l=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,a.N)(()=>f(!0),[]);let p=c||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,l.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4738:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},4835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5152:(e,t,n)=>{n.d(t,{Mz:()=>e$,i3:()=>eQ,UC:()=>eZ,bL:()=>eY,Bk:()=>eM});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=m(g(t)),u=v(l),c=p(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=E(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:b}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(c=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:s,y:d}=E(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=w(h),v=l[p?"floating"===d?"reference":"floating":d],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},C=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:b,strategy:u}):y);return{top:(g.top-C.top+m.top)/E.y,bottom:(C.bottom-g.bottom+m.bottom)/E.y,left:(g.left-C.left+m.left)/E.x,right:(C.right-g.right+m.right)/E.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return o.some(t=>e[t]>=0)}async function k(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),u="y"===g(n),c=["left","top"].includes(a)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),u?{x:v*s,y:m*c}:{x:m*c,y:v*s}}function S(){return"undefined"!=typeof window}function N(e){return O(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(O(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function O(e){return!!S()&&(e instanceof Node||e instanceof P(e).Node)}function M(e){return!!S()&&(e instanceof Element||e instanceof P(e).Element)}function j(e){return!!S()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function D(e){return!!S()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=H(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function _(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=W(),n=M(e)?H(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(N(e))}function H(e){return P(e).getComputedStyle(e)}function U(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||L(e);return D(t)?t.host:t}function q(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=z(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:j(n)&&I(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=P(o);if(i){let e=G(a);return t.concat(a,a.visualViewport||[],I(o)?o:[],e&&n?q(e):[])}return t.concat(o,q(o,[],n))}function G(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=H(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=j(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function V(e){return M(e)?e:e.contextElement}function X(e){let t=V(e);if(!j(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=K(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let Y=c(0);function $(e){let t=P(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=V(e),l=c(1);t&&(r?M(r)&&(l=X(r)):l=X(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(a))&&o)?$(a):c(0),s=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=P(a),t=r&&M(r)?P(r):r,n=e,o=G(n);for(;o&&r&&t!==n;){let e=X(o),t=o.getBoundingClientRect(),r=H(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=a,o=G(n=P(o))}}return x({width:f,height:p,x:s,y:d})}function Q(e,t){let n=U(e).scrollLeft;return t?t.left+n:Z(L(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=L(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=W();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=L(e),n=U(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+Q(e),u=-n.scrollTop;return"rtl"===H(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(L(e));else if(M(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=j(e)?X(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=$(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===H(e).position}function en(e,t){if(!j(e)||"fixed"===H(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(_(e))return n;if(!j(e)){let t=z(e);for(;t&&!B(t);){if(M(t)&&!et(t))return t;t=z(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(N(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!F(r)?n:r||function(e){let t=z(e);for(;j(t)&&!B(t);){if(F(t))return t;if(_(t))break;t=z(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=j(t),o=L(t),i="fixed"===n,a=Z(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==N(t)||I(o))&&(l=U(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=Q(o));i&&!r&&o&&(u.x=Q(o));let s=!o||r||i?c(0):J(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=L(r),l=!!t&&_(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=j(r);if((f||!f&&!i)&&(("body"!==N(r)||I(a))&&(u=U(r)),j(r))){let e=Z(r);s=X(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?c(0):J(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?_(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=q(e,[],!1).filter(e=>M(e)&&"body"!==N(e)),o=null,i="fixed"===H(e).position,a=i?z(e):e;for(;M(a)&&!B(a);){let t=H(a),n=F(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(a)&&!n&&function e(t,n){let r=z(t);return!(r===n||!M(r)||B(r))&&("fixed"===H(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=z(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:X,isElement:M,isRTL:function(e){return"rtl"===H(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=w(p),b={x:n,y:r},x=m(g(o)),E=v(x),C=await u.getDimensions(d),R="y"===x,A=R?"clientHeight":"clientWidth",T=l.reference[E]+l.reference[x]-b[x]-l.floating[E],k=b[x]-l.reference[x],S=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),N=S?S[A]:0;N&&await (null==u.isElement?void 0:u.isElement(S))||(N=c.floating[A]||l.floating[E]);let P=N/2-C[E]/2-1,L=i(y[R?"top":"left"],P),O=i(y[R?"bottom":"right"],P),M=N-C[E]-O,j=N/2-C[E]/2+(T/2-k/2),D=a(L,i(j,M)),I=!s.arrow&&null!=h(o)&&j!==D&&l.reference[E]/2-(j<L?L:O)-C[E]/2<0,_=I?j<L?j-L:j-M:0;return{[x]:b[x]+_,data:{[x]:D,centerOffset:j-D-_,...I&&{alignmentOffset:_}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var ec=n(7650),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await k(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},h=await R(t,s),v=g(p(o)),y=m(v),b=d[y],w=d[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}let x=c.fn({...t,[y]:b,[v]:w});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:l,[v]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=g(o),h=m(d),v=s[h],y=s[d],b=f(l,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+w.mainAxis,n=i.reference[h]+i.reference[e]-w.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?w.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[d]:y}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a,l;let{placement:u,middlewareData:c,rects:s,initialPlacement:d,platform:w,elements:x}=t,{mainAxis:E=!0,crossAxis:C=!0,fallbackPlacements:A,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:S=!0,...N}=f(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let P=p(u),L=g(d),O=p(d)===d,M=await (null==w.isRTL?void 0:w.isRTL(x.floating)),j=A||(O||!S?[b(d)]:function(e){let t=b(e);return[y(e),t,y(t)]}(d)),D="none"!==k;!A&&D&&j.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(d,S,k,M));let I=[d,...j],_=await R(t,N),F=[],W=(null==(r=c.flip)?void 0:r.overflows)||[];if(E&&F.push(_[P]),C){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=b(a)),[a,b(a)]}(u,s,M);F.push(_[e[0]],_[e[1]])}if(W=[...W,{placement:u,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=I[e];if(t){let n="alignment"===C&&L!==g(t),r=(null==(a=W[0])?void 0:a.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:W},reset:{placement:t}}}let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(T){case"bestFit":{let e=null==(l=W.filter(e=>{if(D){let t=g(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=d}if(u!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:c,platform:s,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),y=await R(t,v),b=p(u),w=h(u),x="y"===g(u),{width:E,height:C}=c.floating;"top"===b||"bottom"===b?(o=b,l=w===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=b,o="end"===w?"top":"bottom");let A=C-y.top-y.bottom,T=E-y.left-y.right,k=i(C-y[o],A),S=i(E-y[l],T),N=!t.middlewareData.shift,P=k,L=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=A),N&&!w){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?L=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):P=C-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...t,availableWidth:L,availableHeight:P});let O=await s.getDimensions(d.floating);return E!==O.width||C!==O.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=A(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=A(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eC=n(3655),eR=n(5155),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eT=n(6101),ek=n(6081),eS=n(9033),eN=n(2712),eP=n(1275),eL="Popper",[eO,eM]=(0,ek.A)(eL),[ej,eD]=eO(eL),eI=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(ej,{scope:t,anchor:o,onAnchorChange:i,children:n})};eI.displayName=eL;var e_="PopperAnchor",eF=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eD(e_,n),l=r.useRef(null),u=(0,eT.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,eR.jsx)(eC.sG.div,{...i,ref:u})});eF.displayName=e_;var eW="PopperContent",[eB,eH]=eO(eW),eU=r.forwardRef((e,t)=>{var n,o,l,c,s,d,f,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:b=0,avoidCollisions:w=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:C="partial",hideWhenDetached:R=!1,updatePositionStrategy:A="optimized",onPlaced:T,...k}=e,S=eD(eW,h),[N,P]=r.useState(null),O=(0,eT.s)(t,e=>P(e)),[M,j]=r.useState(null),D=(0,eP.X)(M),I=null!=(f=null==D?void 0:D.width)?f:0,_=null!=(p=null==D?void 0:D.height)?p:0,F="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},W=Array.isArray(x)?x:[x],B=W.length>0,H={padding:F,boundary:W.filter(eK),altBoundary:B},{refs:U,floatingStyles:z,placement:G,isPositioned:K,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),b=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),w=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=a||m,E=l||g,C=r.useRef(null),R=r.useRef(null),A=r.useRef(d),T=null!=c,k=eh(c),S=eh(i),N=eh(s),P=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};S.current&&(e.platform=S.current),eu(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==N.current};L.current&&!ed(A.current,t)&&(A.current=t,ec.flushSync(()=>{f(t)}))})},[p,t,n,S,N]);es(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let L=r.useRef(!1);es(()=>(L.current=!0,()=>{L.current=!1}),[]),es(()=>{if(x&&(C.current=x),E&&(R.current=E),x&&E){if(k.current)return k.current(x,E,P);P()}},[x,E,P,k,T]);let O=r.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:w}),[b,w]),M=r.useMemo(()=>({reference:x,floating:E}),[x,E]),j=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ep(M.floating,d.x),r=ep(M.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,M.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:O,elements:M,floatingStyles:j}),[d,P,O,M,j])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=V(e),h=l||c?[...p?q(p):[],...q(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=L(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(s||t(),!m||!v)return;let g=u(h),y=u(o.clientWidth-(p+m)),b={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+v))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!w)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ea(f,e.getBoundingClientRect())||c(),w=!1}try{r=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,b)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let r=Z(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:S.anchor},middleware:[ev({mainAxis:v+_,alignmentAxis:y}),w&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?ey():void 0,...H}),w&&eb({...H}),ew({...H,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),M&&eE({element:M,padding:b}),eV({arrowWidth:I,arrowHeight:_}),R&&ex({strategy:"referenceHidden",...H})]}),[Y,$]=eX(G),Q=(0,eS.c)(T);(0,eN.N)(()=>{K&&(null==Q||Q())},[K,Q]);let J=null==(n=X.arrow)?void 0:n.x,ee=null==(o=X.arrow)?void 0:o.y,et=(null==(l=X.arrow)?void 0:l.centerOffset)!==0,[en,er]=r.useState();return(0,eN.N)(()=>{N&&er(window.getComputedStyle(N).zIndex)},[N]),(0,eR.jsx)("div",{ref:U.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:K?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(c=X.transformOrigin)?void 0:c.x,null==(s=X.transformOrigin)?void 0:s.y].join(" "),...(null==(d=X.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eB,{scope:h,placedSide:Y,onArrowChange:j,arrowX:J,arrowY:ee,shouldHideArrow:et,children:(0,eR.jsx)(eC.sG.div,{"data-side":Y,"data-align":$,...k,ref:O,style:{...k.style,animation:K?void 0:"none"}})})})});eU.displayName=eW;var ez="PopperArrow",eq={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eH(ez,n),i=eq[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eK(e){return null!==e}eG.displayName=ez;var eV=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=eX(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(a=null==(o=c.arrow)?void 0:o.y)?a:0)+f/2,y="",b="";return"bottom"===p?(y=s?m:"".concat(v,"px"),b="".concat(-f,"px")):"top"===p?(y=s?m:"".concat(v,"px"),b="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),b=s?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),b=s?m:"".concat(g,"px")),{data:{x:y,y:b}}}});function eX(e){let[t,n="center"]=e.split("-");return[t,n]}var eY=eI,e$=eF,eZ=eU,eQ=eG},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5452:(e,t,n)=>{n.d(t,{UC:()=>ee,VY:()=>en,ZL:()=>Q,bL:()=>Z,bm:()=>er,hE:()=>et,hJ:()=>J});var r=n(2115),o=n(5185),i=n(6101),a=n(6081),l=n(1285),u=n(5845),c=n(9178),s=n(7900),d=n(4378),f=n(8905),p=n(3655),h=n(2293),m=n(3795),v=n(8168),g=n(9708),y=n(5155),b="Dialog",[w,x]=(0,a.A)(b),[E,C]=w(b),R=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=i&&i,onChange:a,caller:b});return(0,y.jsx)(E,{scope:t,triggerRef:s,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};R.displayName=b;var A="DialogTrigger";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(A,n),l=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":G(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})}).displayName=A;var T="DialogPortal",[k,S]=w(T,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=C(T,t);return(0,y.jsx)(k,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};N.displayName=T;var P="DialogOverlay",L=r.forwardRef((e,t)=>{let n=S(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=C(P,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(M,{...o,ref:t})}):null});L.displayName=P;var O=(0,g.TL)("DialogOverlay.RemoveScroll"),M=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(P,n);return(0,y.jsx)(m.A,{as:O,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":G(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),j="DialogContent",D=r.forwardRef((e,t)=>{let n=S(j,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=C(j,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(I,{...o,ref:t}):(0,y.jsx)(_,{...o,ref:t})})});D.displayName=j;var I=r.forwardRef((e,t)=>{let n=C(j,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(F,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),_=r.forwardRef((e,t)=>{let n=C(j,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let l=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...u}=e,d=C(j,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,y.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":G(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:d.titleId}),(0,y.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(W,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});B.displayName=W;var H="DialogDescription",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(H,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});U.displayName=H;var z="DialogClose",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=C(z,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function G(e){return e?"open":"closed"}q.displayName=z;var K="DialogTitleWarning",[V,X]=(0,a.q)(K,{contentName:j,titleName:W,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=X(K),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},Z=R,Q=N,J=L,ee=D,et=B,en=U,er=q},5690:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5695:(e,t,n)=>{var r=n(8999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},5845:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[c,e,l,u])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[l]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(2115);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=i(e,r)),t&&(o.current=i(t,r))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},useLinkStatus:function(){return y}});let r=n(6966),o=n(5155),i=r._(n(2115)),a=n(2757),l=n(5227),u=n(9818),c=n(6654),s=n(9991),d=n(5929);n(3230);let f=n(4930),p=n(2664),h=n(6634);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function v(e){let t,n,r,[a,v]=(0,i.useOptimistic)(f.IDLE_LINK_STATUS),y=(0,i.useRef)(null),{href:b,as:w,children:x,prefetch:E=null,passHref:C,replace:R,shallow:A,scroll:T,onClick:k,onMouseEnter:S,onTouchStart:N,legacyBehavior:P=!1,onNavigate:L,ref:O,unstable_dynamicOnHover:M,...j}=e;t=x,P&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let D=i.default.useContext(l.AppRouterContext),I=!1!==E,_=null===E?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:F,as:W}=i.default.useMemo(()=>{let e=m(b);return{href:e,as:w?m(w):e}},[b,w]);P&&(n=i.default.Children.only(t));let B=P?n&&"object"==typeof n&&n.ref:O,H=i.default.useCallback(e=>(null!==D&&(y.current=(0,f.mountLinkInstance)(e,F,D,_,I,v)),()=>{y.current&&((0,f.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,f.unmountPrefetchableInstance)(e)}),[I,F,D,_,v]),U={ref:(0,c.useMergedRef)(H,B),onClick(e){P||"function"!=typeof k||k(e),P&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),D&&(e.defaultPrevented||function(e,t,n,r,o,a,l){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==a||a,r.current)})}}(e,F,W,y,R,T,L))},onMouseEnter(e){P||"function"!=typeof S||S(e),P&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),D&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){P||"function"!=typeof N||N(e),P&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),D&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,s.isAbsoluteUrl)(W)?U.href=W:P&&!C&&("a"!==n.type||"href"in n.props)||(U.href=(0,d.addBasePath)(W)),r=P?i.default.cloneElement(n,U):(0,o.jsx)("a",{...j,...U,children:t}),(0,o.jsx)(g.Provider,{value:a,children:r})}n(3180);let g=(0,i.createContext)(f.IDLE_LINK_STATUS),y=()=>(0,i.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7434:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7489:(e,t,n)=>{n.d(t,{b:()=>c});var r=n(2115),o=n(3655),i=n(5155),a="horizontal",l=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=a,...c}=e,s=(n=u,l.includes(n))?u:a;return(0,i.jsx)(o.sG.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...c,ref:t})});u.displayName="Separator";var c=u},7580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),i=n(3655),a=n(9033),l=n(5155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[b,w]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),C=r.useRef(null),R=(0,o.s)(t,e=>w(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!b)return;let t=e.target;b.contains(t)?C.current=t:h(C.current,{select:!0})},t=function(e){if(A.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||h(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,A.paused]),r.useEffect(()=>{if(b){m.add(A);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,s);b.addEventListener(u,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);b.addEventListener(c,E),b.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),b.removeEventListener(c,E),m.remove(A)},0)}}},[b,x,E,A]);let T=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:T})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8106:(e,t,n)=>{n.d(t,{Ke:()=>E,R6:()=>w,bL:()=>A});var r=n(2115),o=n(5185),i=n(6081),a=n(5845),l=n(2712),u=n(6101),c=n(3655),s=n(8905),d=n(1285),f=n(5155),p="Collapsible",[h,m]=(0,i.A)(p),[v,g]=h(p),y=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:i,disabled:l,onOpenChange:u,...s}=e,[h,m]=(0,a.i)({prop:o,defaultProp:null!=i&&i,onChange:u,caller:p});return(0,f.jsx)(v,{scope:n,disabled:l,contentId:(0,d.B)(),open:h,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),children:(0,f.jsx)(c.sG.div,{"data-state":R(h),"data-disabled":l?"":void 0,...s,ref:t})})});y.displayName=p;var b="CollapsibleTrigger",w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,i=g(b,n);return(0,f.jsx)(c.sG.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":R(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:t,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});w.displayName=b;var x="CollapsibleContent",E=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=g(x,e.__scopeCollapsible);return(0,f.jsx)(s.C,{present:n||o.open,children:e=>{let{present:n}=e;return(0,f.jsx)(C,{...r,ref:t,present:n})}})});E.displayName=x;var C=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:i,...a}=e,s=g(x,n),[d,p]=r.useState(o),h=r.useRef(null),m=(0,u.s)(t,h),v=r.useRef(0),y=v.current,b=r.useRef(0),w=b.current,E=s.open||d,C=r.useRef(E),A=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=h.current;if(e){A.current=A.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();v.current=t.height,b.current=t.width,C.current||(e.style.transitionDuration=A.current.transitionDuration,e.style.animationName=A.current.animationName),p(o)}},[s.open,o]),(0,f.jsx)(c.sG.div,{"data-state":R(s.open),"data-disabled":s.disabled?"":void 0,id:s.contentId,hidden:!E,...a,ref:m,style:{"--radix-collapsible-content-height":y?"".concat(y,"px"):void 0,"--radix-collapsible-content-width":w?"".concat(w,"px"):void 0,...e.style},children:E&&i})});function R(e){return e?"open":"closed"}var A=y},8136:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},8564:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function i(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},8905:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(2115),o=n(6101),i=n(2712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=l(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),c=(0,o.s)(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:c}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(2115),i=n(5185),a=n(3655),l=n(6101),u=n(9033),c=n(5155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,...x}=e,E=o.useContext(d),[C,R]=o.useState(null),A=null!=(f=null==C?void 0:C.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,T]=o.useState({}),k=(0,l.s)(t,e=>R(e)),S=Array.from(E.layers),[N]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),P=S.indexOf(N),L=C?S.indexOf(C):-1,O=E.layersWithOutsidePointerEventsDisabled.size>0,M=L>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));M&&!n&&(null==g||g(e),null==b||b(e),e.defaultPrevented||null==w||w())},A),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},A);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},A),o.useEffect(()=>{if(C)return m&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),p(),()=>{m&&1===E.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[C,A,m,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),p())},[C,E]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...x,ref:k,style:{pointerEvents:O?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.hO)(i,l):i.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9803:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return l},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);