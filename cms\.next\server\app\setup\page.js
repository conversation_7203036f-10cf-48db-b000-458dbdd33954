(()=>{var e={};e.id=620,e.ids=[620],e.modules={1724:(e,t,r)=>{Promise.resolve().then(r.bind(r,29131))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(49384),n=r(82348);function a(...e){return(0,n.QP)((0,s.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\contexts\\AuthContext.tsx","useAuth")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var n=r(8730),a=r(24224),i=r(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:a=!1,...d}){let l=a?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...d})}},32070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\setup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\setup\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>a,aR:()=>i});var s=r(60687);r(43210);var n=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},50130:(e,t,r)=>{Promise.resolve().then(r.bind(r,32070))},51921:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},55365:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>l});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32070)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\setup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\setup\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/setup/page",pathname:"/setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,AuthProvider:()=>i});var s=r(60687),n=r(43210);let a=(0,n.createContext)(void 0);function i({children:e}){let[t,r]=(0,n.useState)(null),[i,o]=(0,n.useState)(!0),d=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();r(t.user)}else r(null)}catch(e){console.error("Auth check failed:",e),r(null)}finally{o(!1)}},l=async(e,t)=>{try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),n=await s.json();if(s.ok)return r(n.user),{success:!0};return{success:!1,error:n.error||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error"}}},c=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null)}};return(0,s.jsx)(a.Provider,{value:{user:t,loading:i,login:l,logout:c,checkAuth:d},children:e})}function o(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},86769:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},88156:(e,t,r)=>{Promise.resolve().then(r.bind(r,63213))},89797:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),n=r(43210),a=r(29523),i=r(44493),o=r(62688);let d=(0,o.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var l=r(58869);let c=(0,o.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var u=r(41862);let h=(0,o.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),p=(0,o.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);function m(){let[e,t]=(0,n.useState)([{id:"database",title:"Database Connection",description:"Connect to MongoDB database",status:"pending",icon:d},{id:"admin",title:"Admin User",description:"Create admin user account",status:"pending",icon:l.A},{id:"auth",title:"Authentication",description:"Verify authentication system",status:"pending",icon:c}]),[r,o]=(0,n.useState)(!1),[m,x]=(0,n.useState)(!1),v=(e,r,s)=>{t(t=>t.map(t=>t.id===e?{...t,status:r,error:s}:t))},f=async()=>{o(!0),x(!1);try{v("database","running"),v("admin","running");let e=await fetch("/api/init",{method:"POST"}),t=await e.json();e.ok?(v("database","success"),v("admin","success"),v("auth","running"),(await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t.credentials.email,password:t.credentials.password})})).ok?(v("auth","success"),x(!0)):v("auth","error","Authentication test failed")):(v("database","error",t.error||"Database connection failed"),v("admin","error","Failed to create admin user"),v("auth","error","Cannot test authentication"))}catch(e){console.error("Setup error:",e),v("database","error","Network error or MongoDB not running"),v("admin","error","Setup failed"),v("auth","error","Setup failed")}finally{o(!1)}},g=e=>{switch(e){case"running":return(0,s.jsx)(u.A,{className:"h-5 w-5 animate-spin text-blue-500"});case"success":return(0,s.jsx)(h,{className:"h-5 w-5 text-green-500"});case"error":return(0,s.jsx)(p,{className:"h-5 w-5 text-red-500"});default:return(0,s.jsx)("div",{className:"h-5 w-5 rounded-full border-2 border-gray-300"})}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsxs)(i.Zp,{className:"w-full max-w-2xl",children:[(0,s.jsxs)(i.aR,{className:"text-center",children:[(0,s.jsx)(i.ZB,{className:"text-3xl font-bold",children:"Portfolio CMS Setup"}),(0,s.jsx)(i.BT,{children:"Initialize your content management system"})]}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Prerequisites"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,s.jsx)("li",{children:"• MongoDB should be running on localhost:27017"}),(0,s.jsx)("li",{children:"• Make sure your .env.local file is configured"}),(0,s.jsx)("li",{children:"• Default admin credentials: <EMAIL> / admin123"})]})]}),(0,s.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:g(e.status)}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),e.error&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:e.error})]}),(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(e.icon,{className:"h-6 w-6 text-gray-400"})})]},e.id))}),(0,s.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,s.jsx)(a.$,{onClick:f,disabled:r,className:"px-8",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Setting up..."]}):"Start Setup"}),m&&(0,s.jsx)(a.$,{variant:"outline",onClick:()=>window.location.href="/login",children:"Go to Login"})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"MongoDB Setup"}),(0,s.jsxs)("div",{className:"text-sm text-yellow-800 space-y-2",children:[(0,s.jsx)("p",{children:"If you don't have MongoDB running, here are quick setup options:"}),(0,s.jsxs)("div",{className:"bg-yellow-100 p-3 rounded font-mono text-xs",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"Option 1 - Local MongoDB:"})}),(0,s.jsx)("p",{children:"1. Download from: https://www.mongodb.com/try/download/community"}),(0,s.jsx)("p",{children:"2. Install and start the service"}),(0,s.jsx)("p",{children:"3. Default connection: mongodb://localhost:27017"})]}),(0,s.jsxs)("div",{className:"bg-yellow-100 p-3 rounded font-mono text-xs",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"Option 2 - MongoDB Atlas (Cloud):"})}),(0,s.jsx)("p",{children:"1. Create free account at: https://cloud.mongodb.com"}),(0,s.jsx)("p",{children:"2. Create cluster and get connection string"}),(0,s.jsx)("p",{children:"3. Update MONGODB_URI in .env.local"})]})]})]}),m&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 text-center",children:[(0,s.jsx)(h,{className:"h-8 w-8 text-green-500 mx-auto mb-2"}),(0,s.jsx)("h3",{className:"font-semibold text-green-900",children:"Setup Complete!"}),(0,s.jsx)("p",{className:"text-sm text-green-800 mt-1",children:"Your CMS is ready to use. You can now login with the admin credentials."})]})]})]})})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>o});var s=r(37413),n=r(25091),a=r.n(n);r(61135);var i=r(29131);let o={title:"Portfolio CMS - Content Management System",description:"Manage your portfolio content with ease"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:(0,s.jsx)(i.AuthProvider,{children:e})})})}},96986:(e,t,r)=>{Promise.resolve().then(r.bind(r,89797))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,1771,1658,9365],()=>r(55365));module.exports=s})();