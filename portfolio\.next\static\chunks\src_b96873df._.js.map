{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { ChevronDown, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function Hero() {\n  const typedTextRef = useRef<HTMLSpanElement>(null);\n\n  useEffect(() => {\n    // Simple typing animation\n    const texts = ['Video Editor', 'Storyteller', 'Motion Graphics Artist', 'Visual Craftsman'];\n    let textIndex = 0;\n    let charIndex = 0;\n    let isDeleting = false;\n    let currentText = '';\n\n    const typeText = () => {\n      const fullText = texts[textIndex];\n\n      if (isDeleting) {\n        currentText = fullText.substring(0, charIndex - 1);\n        charIndex--;\n      } else {\n        currentText = fullText.substring(0, charIndex + 1);\n        charIndex++;\n      }\n\n      if (typedTextRef.current) {\n        typedTextRef.current.textContent = currentText;\n      }\n\n      let typeSpeed = isDeleting ? 40 : 60;\n\n      if (!isDeleting && charIndex === fullText.length) {\n        typeSpeed = 1500; // Pause at end\n        isDeleting = true;\n      } else if (isDeleting && charIndex === 0) {\n        isDeleting = false;\n        textIndex = (textIndex + 1) % texts.length;\n        typeSpeed = 500; // Pause before next word\n      }\n\n      setTimeout(typeText, typeSpeed);\n    };\n\n    typeText();\n  }, []);\n\n  const handleScrollToWork = () => {\n    const element = document.getElementById('featured-work');\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  const handleScrollToContact = () => {\n    const element = document.getElementById('contact');\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  return (\n    <section\n      id=\"home\"\n      className=\"relative min-h-screen flex items-center bg-background overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-accent/20 to-transparent transform skew-x-12\"></div>\n      </div>\n\n      <div className=\"container mx-auto container-padding section-padding relative z-10\">\n        <div className=\"flex flex-col lg:flex-row items-center justify-between gap-12\">\n          {/* Text Content */}\n          <div className=\"flex-1 max-w-2xl text-center lg:text-left\">\n            <h1 className=\"heading-primary mb-4 animate-in slide-in-from-left duration-1000\">\n              Uttam Rimal\n            </h1>\n            <h2 className=\"heading-tertiary text-accent mb-6 animate-in slide-in-from-left duration-1000 delay-200\">\n              Creative Video Editor & Storyteller\n            </h2>\n            <p className=\"text-body-large mb-8 animate-in slide-in-from-left duration-1000 delay-400\">\n              Transforming footage into compelling narratives. I&apos;m a{' '}\n              <span ref={typedTextRef} className=\"text-accent font-semibold\"></span>\n              <span className=\"animate-pulse\">_</span>\n              {' '}with over a year of experience crafting visually stunning videos that engage and inspire.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 mb-8 animate-in slide-in-from-left duration-1000 delay-600\">\n              <Button\n                onClick={handleScrollToWork}\n                variant=\"secondary\"\n                className=\"btn-secondary\"\n              >\n                View My Work\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleScrollToContact}\n                className=\"hover-lift\"\n              >\n                Get a Quote\n              </Button>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex justify-center lg:justify-start gap-4 animate-in slide-in-from-left duration-1000 delay-800\">\n              {[\n                { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn', color: 'hover:text-[#0077B5]' },\n                { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram', color: 'hover:text-[#E4405F]' },\n                { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube', color: 'hover:text-[#FF0000]' },\n                { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook', color: 'hover:text-[#1877F2]' },\n              ].map(({ icon: Icon, href, label, color }) => (\n                <Link\n                  key={label}\n                  href={href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className={`w-12 h-12 bg-foreground/10 hover:bg-accent text-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg ${color}`}\n                  aria-label={label}\n                >\n                  <Icon size={20} />\n                </Link>\n              ))}\n            </div>\n\n          </div>\n\n          {/* Profile Image */}\n          <div className=\"flex-1 max-w-md animate-in slide-in-from-right duration-1000 delay-300\">\n            <div className=\"relative\">\n              <div className=\"w-80 h-80 mx-auto relative\">\n                <Image\n                  src=\"/images/uttam_rimal.jpg\"\n                  alt=\"Uttam Rimal - Professional Video Editor\"\n                  fill\n                  className=\"rounded-full object-cover border-4 border-foreground/20 shadow-2xl animate-float\"\n                  priority\n                />\n              </div>\n              {/* Decorative elements */}\n              <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-accent/20 rounded-full blur-xl animate-pulse-slow\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-accent/30 rounded-full blur-xl animate-glow\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Down Arrow */}\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        onClick={handleScrollToWork}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-foreground/80 hover:text-foreground transition-colors duration-300 animate-bounce-slow w-auto h-auto p-2\"\n        aria-label=\"Scroll down\"\n      >\n        <ChevronDown size={32} />\n      </Button>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,0BAA0B;YAC1B,MAAM,QAAQ;gBAAC;gBAAgB;gBAAe;gBAA0B;aAAmB;YAC3F,IAAI,YAAY;YAChB,IAAI,YAAY;YAChB,IAAI,aAAa;YACjB,IAAI,cAAc;YAElB,MAAM;2CAAW;oBACf,MAAM,WAAW,KAAK,CAAC,UAAU;oBAEjC,IAAI,YAAY;wBACd,cAAc,SAAS,SAAS,CAAC,GAAG,YAAY;wBAChD;oBACF,OAAO;wBACL,cAAc,SAAS,SAAS,CAAC,GAAG,YAAY;wBAChD;oBACF;oBAEA,IAAI,aAAa,OAAO,EAAE;wBACxB,aAAa,OAAO,CAAC,WAAW,GAAG;oBACrC;oBAEA,IAAI,YAAY,aAAa,KAAK;oBAElC,IAAI,CAAC,cAAc,cAAc,SAAS,MAAM,EAAE;wBAChD,YAAY,MAAM,eAAe;wBACjC,aAAa;oBACf,OAAO,IAAI,cAAc,cAAc,GAAG;wBACxC,aAAa;wBACb,YAAY,CAAC,YAAY,CAAC,IAAI,MAAM,MAAM;wBAC1C,YAAY,KAAK,yBAAyB;oBAC5C;oBAEA,WAAW,UAAU;gBACvB;;YAEA;QACF;yBAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,6LAAC;oCAAG,WAAU;8CAA0F;;;;;;8CAGxG,6LAAC;oCAAE,WAAU;;wCAA6E;wCAC5B;sDAC5D,6LAAC;4CAAK,KAAK;4CAAc,WAAU;;;;;;sDACnC,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAC/B;wCAAI;;;;;;;8CAIP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM,6MAAA,CAAA,WAAQ;4CAAE,MAAM;4CAA0C,OAAO;4CAAY,OAAO;wCAAuB;wCACnH;4CAAE,MAAM,+MAAA,CAAA,YAAS;4CAAE,MAAM;4CAAwC,OAAO;4CAAa,OAAO;wCAAuB;wCACnH;4CAAE,MAAM,2MAAA,CAAA,UAAO;4CAAE,MAAM;4CAAwC,OAAO;4CAAW,OAAO;wCAAuB;wCAC/G;4CAAE,MAAM,6MAAA,CAAA,WAAQ;4CAAE,MAAM;4CAAuC,OAAO;4CAAY,OAAO;wCAAuB;qCACjH,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACvC,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM;4CACN,QAAO;4CACP,KAAI;4CACJ,WAAW,CAAC,qKAAqK,EAAE,OAAO;4CAC1L,cAAY;sDAEZ,cAAA,6LAAC;gDAAK,MAAM;;;;;;2CAPP;;;;;;;;;;;;;;;;sCAeb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,MAAM;;;;;;;;;;;;;;;;;AAI3B;GA3KwB;KAAA", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40\",\n        info:\n          \"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/video-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport { X } from 'lucide-react';\nimport { Dialog, DialogContent, DialogClose } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\n\ninterface VideoDialogProps {\n  isOpen: boolean;\n  onClose: () => void;\n  videoId: string | null;\n  videoTitle?: string;\n  variant?: 'default' | 'compact';\n}\n\nexport function VideoDialog({ \n  isOpen, \n  onClose, \n  videoId, \n  videoTitle = 'Video',\n  variant = 'default' \n}: VideoDialogProps) {\n  const getYouTubeEmbedUrl = (id: string) => {\n    return `https://www.youtube.com/embed/${id}?autoplay=1&rel=0&modestbranding=1`;\n  };\n\n  const dialogStyles = variant === 'compact' \n    ? \"max-w-4xl w-full p-0\"\n    : \"w-[95vw] sm:max-w-2xl md:max-w-4xl lg:max-w-6xl p-0 bg-black border-0\";\n\n  const closeButtonStyles = variant === 'compact'\n    ? \"absolute top-4 right-4 z-10 bg-primary/50 hover:bg-primary/70 text-primary-foreground rounded-full p-2 transition-colors\"\n    : \"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2\";\n\n  return (\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\n      <DialogContent className={dialogStyles}>\n        <div className=\"relative w-full aspect-video\">\n          {variant === 'default' ? (\n            <DialogClose asChild>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className={closeButtonStyles}\n              >\n                <X size={24} />\n              </Button>\n            </DialogClose>\n          ) : (\n            <button\n              onClick={onClose}\n              className={closeButtonStyles}\n            >\n              <X size={20} />\n            </button>\n          )}\n          \n          {videoId && (\n            <iframe\n              src={getYouTubeEmbedUrl(videoId)}\n              title={videoTitle}\n              className={`w-full h-full ${variant === 'compact' ? 'rounded-lg' : ''}`}\n              allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n              allowFullScreen\n            />\n          )}\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcO,SAAS,YAAY,EAC1B,MAAM,EACN,OAAO,EACP,OAAO,EACP,aAAa,OAAO,EACpB,UAAU,SAAS,EACF;IACjB,MAAM,qBAAqB,CAAC;QAC1B,OAAO,CAAC,8BAA8B,EAAE,GAAG,kCAAkC,CAAC;IAChF;IAEA,MAAM,eAAe,YAAY,YAC7B,yBACA;IAEJ,MAAM,oBAAoB,YAAY,YAClC,6HACA;IAEJ,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAW;sBACxB,cAAA,6LAAC;gBAAI,WAAU;;oBACZ,YAAY,0BACX,6LAAC,qIAAA,CAAA,cAAW;wBAAC,OAAO;kCAClB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAW;sCAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;6CAIb,6LAAC;wBACC,SAAS;wBACT,WAAW;kCAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;oBAIZ,yBACC,6LAAC;wBACC,KAAK,mBAAmB;wBACxB,OAAO;wBACP,WAAW,CAAC,cAAc,EAAE,YAAY,YAAY,eAAe,IAAI;wBACvE,OAAM;wBACN,eAAe;;;;;;;;;;;;;;;;;;;;;;AAO7B;KAvDgB", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/api.ts"], "sourcesContent": ["// API integration for fetching content from CMS\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || \"http://localhost:3002\";\n\n// Types for API responses\nexport interface BlogPost {\n  _id: string;\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  thumbnail: string;\n  category: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  readTime: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Video {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  description?: string;\n  category?: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reel {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  thumbnail?: string; // Generated dynamically from YouTube API\n  description?: string;\n  platform: \"youtube\";\n  embedUrl?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Testimonial {\n  _id: string;\n  name: string;\n  slug: string;\n  avatar?: string;\n  role: string;\n  company?: string;\n  email?: string;\n  linkedinUrl?: string;\n  content: string;\n  rating: number;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Client {\n  _id: string;\n  name: string;\n  slug: string;\n  logo: string;\n  description: string;\n  website?: string;\n  industry?: string;\n  projectType?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n}\n\n// Generic fetch function with error handling\nasync function fetchFromCMS<T>(endpoint: string): Promise<T[]> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api${endpoint}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      // Add cache control for better performance\n      next: { revalidate: 300 }, // Revalidate every 5 minutes\n    });\n\n    if (!response.ok) {\n      console.error(\n        `Failed to fetch ${endpoint}:`,\n        response.status,\n        response.statusText\n      );\n      return [];\n    }\n\n    const result: ApiResponse<T[]> = await response.json();\n\n    if (!result.success) {\n      console.error(`API error for ${endpoint}:`, result.message);\n      return [];\n    }\n\n    return result.data || [];\n  } catch (error) {\n    console.error(`Network error fetching ${endpoint}:`, error);\n    return [];\n  }\n}\n\n// Blog API functions\nexport async function getBlogPosts(): Promise<BlogPost[]> {\n  const posts = await fetchFromCMS<BlogPost>(\"/blog\");\n  // Only return published posts, sorted by creation date\n  return posts\n    .filter((post) => post.status === \"published\")\n    .sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n}\n\nexport async function getFeaturedBlogPosts(): Promise<BlogPost[]> {\n  const posts = await getBlogPosts();\n  return posts.filter((post) => post.featured);\n}\n\nexport async function getBlogPostBySlug(\n  slug: string\n): Promise<BlogPost | null> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/slug/${slug}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      next: { revalidate: 300 },\n    });\n\n    if (!response.ok) {\n      return null;\n    }\n\n    const result: ApiResponse<BlogPost> = await response.json();\n    return result.success ? result.data : null;\n  } catch (error) {\n    console.error(\"Error fetching blog post by slug:\", error);\n    return null;\n  }\n}\n\n// Videos API functions\nexport async function getVideos(): Promise<Video[]> {\n  const videos = await fetchFromCMS<Video>(\"/videos\");\n  return videos\n    .filter((video) => video.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedVideos(): Promise<Video[]> {\n  const videos = await getVideos();\n  return videos.filter((video) => video.featured);\n}\n\n// Reels API functions\nexport async function getReels(): Promise<Reel[]> {\n  const reels = await fetchFromCMS<Reel>(\"/reels\");\n  return reels\n    .filter((reel) => reel.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedReels(): Promise<Reel[]> {\n  const reels = await getReels();\n  return reels.filter((reel) => reel.featured);\n}\n\n// Testimonials API functions\nexport async function getTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await fetchFromCMS<Testimonial>(\"/testimonials\");\n\n  // If CMS returns empty data, use mock data as fallback\n  if (testimonials.length === 0) {\n    const { testimonials: mockTestimonials } = await import('../data/testimonials');\n    return mockTestimonials.map((testimonial, index) => ({\n      ...testimonial,\n      _id: testimonial.id.toString(),\n      slug: testimonial.name.toLowerCase().replace(/\\s+/g, '-'),\n      rating: testimonial.rating || 5,\n      status: 'published' as const,\n      order: index + 1,\n      featured: index < 3, // First 3 are featured\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }));\n  }\n\n  return testimonials\n    .filter((testimonial) => testimonial.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await getTestimonials();\n  return testimonials.filter((testimonial) => testimonial.featured);\n}\n\n// Clients API functions\nexport async function getClients(): Promise<Client[]> {\n  const clients = await fetchFromCMS<Client>(\"/clients\");\n  return clients\n    .filter((client) => client.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedClients(): Promise<Client[]> {\n  const clients = await getClients();\n  return clients.filter((client) => client.featured);\n}\n\n// Utility functions\nexport function getYouTubeThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\nexport function generateEmbedUrl(id: string): string {\n  // Only YouTube Shorts are supported now\n  return `https://www.youtube.com/embed/${id}`;\n}\n\nexport function getYouTubeShortsThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\n// Helper function to ensure reels have thumbnails\nexport function ensureReelThumbnail(reel: Reel): Reel {\n  return {\n    ...reel,\n    thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id),\n  };\n}\n\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  });\n}\n\nexport function calculateReadTime(content: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = content.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;;;;;;;;;;;;;;;AAC3B;AAArB,MAAM,eAAe,wEAAmC;AA2FxD,6CAA6C;AAC7C,eAAe,aAAgB,QAAgB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,2CAA2C;YAC3C,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CACX,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAC9B,SAAS,MAAM,EACf,SAAS,UAAU;YAErB,OAAO,EAAE;QACX;QAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;QAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,OAAO;YAC1D,OAAO,EAAE;QACX;QAEA,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAuB;IAC3C,uDAAuD;IACvD,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CACH,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;AAEvE;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAEO,eAAe,kBACpB,IAAY;IAEZ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,EAAE,MAAM,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,SAAgC,MAAM,SAAS,IAAI;QACzD,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,GAAG;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM,aAAoB;IACzC,OAAO,OACJ,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK,aACnC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,QAAQ;AAChD;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAmB;IACvC,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAGO,eAAe;IACpB,MAAM,eAAe,MAAM,aAA0B;IAErD,uDAAuD;IACvD,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,MAAM,EAAE,cAAc,gBAAgB,EAAE,GAAG;QAC3C,OAAO,iBAAiB,GAAG,CAAC,CAAC,aAAa,QAAU,CAAC;gBACnD,GAAG,WAAW;gBACd,KAAK,YAAY,EAAE,CAAC,QAAQ;gBAC5B,MAAM,YAAY,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;gBACrD,QAAQ,YAAY,MAAM,IAAI;gBAC9B,QAAQ;gBACR,OAAO,QAAQ;gBACf,UAAU,QAAQ;gBAClB,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEA,OAAO,aACJ,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK,aAC/C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,eAAe,MAAM;IAC3B,OAAO,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,QAAQ;AAClE;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM,aAAqB;IAC3C,OAAO,QACJ,MAAM,CAAC,CAAC,SAAW,OAAO,MAAM,KAAK,aACrC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,OAAO,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,QAAQ;AACnD;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAEO,SAAS,iBAAiB,EAAU;IACzC,wCAAwC;IACxC,OAAO,CAAC,8BAA8B,EAAE,IAAI;AAC9C;AAEO,SAAS,0BAA0B,OAAe;IACvD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAGO,SAAS,oBAAoB,IAAU;IAC5C,OAAO;QACL,GAAG,IAAI;QACP,WAAW,KAAK,SAAS,IAAI,0BAA0B,KAAK,EAAE;IAChE;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,iBAAiB;IACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/hooks/useScrollAnimation.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nexport function useScrollAnimation(threshold = 0.1) {\n  const elementRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const element = elementRef.current;\n    if (!element) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            entry.target.classList.add('animate');\n          }\n        });\n      },\n      {\n        threshold,\n        rootMargin: '0px 0px -50px 0px',\n      }\n    );\n\n    observer.observe(element);\n\n    return () => {\n      observer.unobserve(element);\n    };\n  }, [threshold]);\n\n  return elementRef;\n}\n\nexport function useStaggeredScrollAnimation(threshold = 0.1) {\n  const containerRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            const children = entry.target.querySelectorAll('.animate-on-scroll');\n            children.forEach((child, index) => {\n              setTimeout(() => {\n                child.classList.add('animate');\n              }, index * 100);\n            });\n          }\n        });\n      },\n      {\n        threshold,\n        rootMargin: '0px 0px -50px 0px',\n      }\n    );\n\n    observer.observe(container);\n\n    return () => {\n      observer.unobserve(container);\n    };\n  }, [threshold]);\n\n  return containerRef;\n}\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;AAIO,SAAS,mBAAmB,YAAY,GAAG;;IAChD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,CAAC,SAAS;YAEd,MAAM,WAAW,IAAI;gDACnB,CAAC;oBACC,QAAQ,OAAO;wDAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;4BAC7B;wBACF;;gBACF;+CACA;gBACE;gBACA,YAAY;YACd;YAGF,SAAS,OAAO,CAAC;YAEjB;gDAAO;oBACL,SAAS,SAAS,CAAC;gBACrB;;QACF;uCAAG;QAAC;KAAU;IAEd,OAAO;AACT;GA7BgB;AA+BT,SAAS,4BAA4B,YAAY,GAAG;;IACzD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iDAAE;YACR,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,CAAC,WAAW;YAEhB,MAAM,WAAW,IAAI;yDACnB,CAAC;oBACC,QAAQ,OAAO;iEAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,CAAC;gCAC/C,SAAS,OAAO;6EAAC,CAAC,OAAO;wCACvB;qFAAW;gDACT,MAAM,SAAS,CAAC,GAAG,CAAC;4CACtB;oFAAG,QAAQ;oCACb;;4BACF;wBACF;;gBACF;wDACA;gBACE;gBACA,YAAY;YACd;YAGF,SAAS,OAAO,CAAC;YAEjB;yDAAO;oBACL,SAAS,SAAS,CAAC;gBACrB;;QACF;gDAAG;QAAC;KAAU;IAEd,OAAO;AACT;IAlCgB", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Play } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { VideoDialog } from '@/components/ui/video-dialog';\nimport { getFeaturedVideos, getYouTubeThumbnail, type Video } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function FeaturedWork() {\n  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);\n  const [videos, setVideos] = useState<Video[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchVideos = async () => {\n      try {\n        const featuredVideos = await getFeaturedVideos();\n        // Show only first 6 videos for featured section\n        setVideos(featuredVideos.slice(0, 6));\n      } catch (error) {\n        console.error('Error fetching featured videos:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchVideos();\n  }, []);\n\n\n\n  return (\n    <section id=\"featured-work\" className=\"section-padding bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto container-padding\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"heading-secondary mb-4\">\n            Featured Work\n          </h2>\n          <p className=\"text-body-large max-w-2xl mx-auto\">\n            A selection of projects showcasing diverse editing styles and creative storytelling techniques.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <div key={index} className=\"bg-card rounded-xl overflow-hidden shadow-lg\">\n                <div className=\"aspect-video bg-gray-200 animate-pulse\"></div>\n                <div className=\"p-6\">\n                  <div className=\"h-6 bg-gray-200 rounded animate-pulse mb-2\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : videos.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No featured videos available at the moment.</p>\n          </div>\n        ) : (\n          <>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {videos.map((video, index) => (\n                <div\n                  key={video._id}\n                  className={`group card-interactive hover-lift h-[380px] flex flex-col animate-on-scroll stagger-${Math.min(index + 1, 6)}`}\n                >\n                  <div className=\"relative aspect-video overflow-hidden flex-shrink-0\">\n                    <Image\n                      src={getYouTubeThumbnail(video.id)}\n                      alt={video.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Play Button Overlay */}\n                    <Button\n                      variant=\"ghost\"\n                      className=\"absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 h-full w-full rounded-none hover:bg-black/40 hover:cursor-pointer\"\n                      onClick={() => setSelectedVideo(video.id)}\n                    >\n                      <div className=\"w-16 h-16 bg-secondary hover:bg-secondary/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg\">\n                        <Play size={24} className=\"text-secondary-foreground ml-1\" fill=\"currentColor\" />\n                      </div>\n                    </Button>\n\n                    {/* Category Badge */}\n                    {video.category && (\n                      <div className=\"absolute top-4 left-4\">\n                        <Badge variant=\"secondary\" className=\"bg-accent/90 text-accent-foreground\">\n                          {video.category}\n                        </Badge>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"p-6 flex-1 flex flex-col\">\n                    <h3 className=\"text-xl font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 line-clamp-2\">\n                      {video.title}\n                    </h3>\n                    {video.description && (\n                      <p className=\"text-muted-foreground text-sm leading-relaxed line-clamp-3 flex-1\">\n                        {video.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Video Dialog */}\n            <VideoDialog\n              isOpen={selectedVideo !== null}\n              onClose={() => setSelectedVideo(null)}\n              videoId={selectedVideo}\n              videoTitle={videos.find(v => v.id === selectedVideo)?.title}\n              variant=\"default\"\n            />\n          </>\n        )}\n\n        {/* View More Button */}\n        <div className=\"text-center mt-12\">\n          <Button asChild className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n            <Link href=\"/videos\">\n              View All Videos\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;sDAAc;oBAClB,IAAI;wBACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD;wBAC7C,gDAAgD;wBAChD,UAAU,eAAe,KAAK,CAAC,GAAG;oBACpC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG,EAAE;IAIL,qBACE,6LAAC;QAAQ,IAAG;QAAgB,WAAU;QAAgC,KAAK;kBACzE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCAGvC,6LAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;gBAKlD,wBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJT;;;;;;;;;2BASZ,OAAO,MAAM,KAAK,kBACpB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C;;sCACE,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;oCAEC,WAAW,CAAC,oFAAoF,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI;;sDAE1H,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,EAAE;oDACjC,KAAK,MAAM,KAAK;oDAChB,IAAI;oDACJ,WAAU;;;;;;8DAIZ,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,iBAAiB,MAAM,EAAE;8DAExC,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;4DAAiC,MAAK;;;;;;;;;;;;;;;;gDAKnE,MAAM,QAAQ,kBACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,MAAM,QAAQ;;;;;;;;;;;;;;;;;sDAMvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,MAAM,KAAK;;;;;;gDAEb,MAAM,WAAW,kBAChB,6LAAC;oDAAE,WAAU;8DACV,MAAM,WAAW;;;;;;;;;;;;;mCAtCnB,MAAM,GAAG;;;;;;;;;;sCA+CpB,6LAAC,8IAAA,CAAA,cAAW;4BACV,QAAQ,kBAAkB;4BAC1B,SAAS,IAAM,iBAAiB;4BAChC,SAAS;4BACT,YAAY,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB;4BACtD,SAAQ;;;;;;;;8BAMd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC;GA7HwB;;QAID,qIAAA,CAAA,8BAA2B;;;KAJ1B", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientCard.tsx"], "sourcesContent": ["import Image from 'next/image';\nimport { ExternalLink } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { type Client } from '@/lib/api';\nimport clsx from 'clsx';\n\ninterface ClientCardProps {\n  client: Client;\n  variant?: 'logo' | 'detailed' | 'compact';\n  onClick?: () => void;\n  showWebsite?: boolean;\n  className?: string;\n}\n\nconst cardStyles = {\n  logo: 'group relative bg-white p-4 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-full h-full flex items-center justify-center',\n  detailed: 'bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',\n  compact: 'bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'\n};\n\nconst imageSizes = {\n  logo: { className: 'relative h-16 w-full max-w-[120px] mx-auto', size: '16vw' },\n  detailed: { className: 'relative h-24 w-full max-w-[200px] mx-auto mb-6', size: '33vw' },\n  compact: { className: 'relative h-12 w-full max-w-[100px] mx-auto mb-3', size: '33vw' }\n};\n\nconst contentPadding = {\n  detailed: 'p-6 text-center',\n  compact: 'p-4'\n};\n\nfunction ClientImage({ client, variant }: { client: Client; variant: keyof typeof imageSizes }) {\n  return (\n    <div className={imageSizes[variant].className}>\n      <Image\n        src={client.logo}\n        alt={client.name}\n        fill\n        className={clsx('object-contain', variant === 'detailed' && 'rounded-md')}\n        sizes={imageSizes[variant].size}\n      />\n    </div>\n  );\n}\n\nfunction ClientBadges({ client }: { client: Client }) {\n  return (\n    <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n      {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n      {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n    </div>\n  );\n}\n\nexport default function ClientCard({\n  client,\n  variant = 'logo',\n  onClick,\n  showWebsite = false,\n  className = ''\n}: ClientCardProps) {\n  if (variant === 'logo') {\n    return (\n      <button\n        onClick={onClick}\n        className={clsx(\n          'group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-40 h-40 flex flex-col items-center justify-center text-center',\n          className\n        )}\n        title={client.name}\n        aria-label={`View ${client.name}`}\n      >\n        <div className=\"relative w-24 h-24 mb-4\">\n          <Image\n            src={client.logo}\n            alt={client.name}\n            fill\n            className=\"object-cover rounded-full border border-gray-200 shadow-sm\"\n            sizes=\"96px\"\n          />\n        </div>\n        <p className=\"text-sm font-medium text-gray-700\">{client.name}</p>\n      </button>\n    );\n  }\n  if (variant === 'detailed') {\n    return (\n      <article\n        className={clsx(\n          ' transition-all duration-300 w-full max-w-md overflow-hidden',\n          className\n        )}\n      >\n        <div className=\"p-6 flex flex-col items-center text-center\">\n          <div className=\"relative w-28 h-28 mb-4\">\n            <Image\n              src={client.logo}\n              alt={client.name}\n              fill\n              className=\"object-cover rounded-xl border border-gray-200 shadow-sm\"\n              sizes=\"112px\"\n            />\n          </div>\n\n          <h3 className=\"text-2xl font-heading font-bold text-portfolio-primary mb-2\">\n            {client.name}\n          </h3>\n\n          <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n            {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n            {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n          </div>\n\n          <p className=\"text-muted-foreground leading-relaxed mb-6\">\n            {client.description}\n          </p>\n\n          {showWebsite && client.website && (\n            <Button variant=\"outline\" asChild>\n              <a\n                href={client.website}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center gap-2\"\n              >\n                Visit Website <ExternalLink size={16} />\n              </a>\n            </Button>\n          )}\n        </div>\n      </article>\n    );\n  }\n\n\n  // Compact\n  return (\n    <article className={clsx(cardStyles.compact, className)}>\n      <div className={contentPadding.compact}>\n        <ClientImage client={client} variant=\"compact\" />\n        <h4 className=\"text-lg font-heading font-semibold text-foreground mb-2\">{client.name}</h4>\n        <ClientBadges client={client} />\n        <p className=\"text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3\">\n          {client.description}\n        </p>\n        <div className=\"flex items-center justify-between\">\n          {onClick && (\n            <button\n              onClick={onClick}\n              className=\"text-foreground font-semibold text-sm hover:text-accent transition-colors duration-300\"\n              aria-label={`Learn more about ${client.name}`}\n            >\n              Learn More →\n            </button>\n          )}\n          {showWebsite && client.website && (\n            <a\n              href={client.website}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-portfolio-secondary hover:text-portfolio-primary transition-colors duration-300\"\n              aria-label={`Visit ${client.name} website`}\n            >\n              <ExternalLink size={16} />\n            </a>\n          )}\n        </div>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAUA,MAAM,aAAa;IACjB,MAAM;IACN,UAAU;IACV,SAAS;AACX;AAEA,MAAM,aAAa;IACjB,MAAM;QAAE,WAAW;QAA8C,MAAM;IAAO;IAC9E,UAAU;QAAE,WAAW;QAAmD,MAAM;IAAO;IACvF,SAAS;QAAE,WAAW;QAAmD,MAAM;IAAO;AACxF;AAEA,MAAM,iBAAiB;IACrB,UAAU;IACV,SAAS;AACX;AAEA,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAwD;IAC5F,qBACE,6LAAC;QAAI,WAAW,UAAU,CAAC,QAAQ,CAAC,SAAS;kBAC3C,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAK,OAAO,IAAI;YAChB,KAAK,OAAO,IAAI;YAChB,IAAI;YACJ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,kBAAkB,YAAY,cAAc;YAC5D,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI;;;;;;;;;;;AAIvC;KAZS;AAcT,SAAS,aAAa,EAAE,MAAM,EAAsB;IAClD,qBACE,6LAAC;QAAI,WAAU;;YACZ,OAAO,QAAQ,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAW,OAAO,QAAQ;;;;;;YAC5D,OAAO,WAAW,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAa,OAAO,WAAW;;;;;;;;;;;;AAG3E;MAPS;AASM,SAAS,WAAW,EACjC,MAAM,EACN,UAAU,MAAM,EAChB,OAAO,EACP,cAAc,KAAK,EACnB,YAAY,EAAE,EACE;IAChB,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YACC,SAAS;YACT,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,kLACA;YAEF,OAAO,OAAO,IAAI;YAClB,cAAY,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE;;8BAEjC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,OAAO,IAAI;wBAChB,KAAK,OAAO,IAAI;wBAChB,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;;;;;;8BAGV,6LAAC;oBAAE,WAAU;8BAAqC,OAAO,IAAI;;;;;;;;;;;;IAGnE;IACA,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,gEACA;sBAGF,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,OAAO,IAAI;4BAChB,KAAK,OAAO,IAAI;4BAChB,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;;;;;;;kCAIV,6LAAC;wBAAG,WAAU;kCACX,OAAO,IAAI;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,QAAQ,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW,OAAO,QAAQ;;;;;;4BAC5D,OAAO,WAAW,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAa,OAAO,WAAW;;;;;;;;;;;;kCAGvE,6LAAC;wBAAE,WAAU;kCACV,OAAO,WAAW;;;;;;oBAGpB,eAAe,OAAO,OAAO,kBAC5B,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,OAAO;kCAC/B,cAAA,6LAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;;gCACX;8CACe,6LAAC,yNAAA,CAAA,eAAY;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhD;IAGA,UAAU;IACV,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,OAAO,EAAE;kBAC3C,cAAA,6LAAC;YAAI,WAAW,eAAe,OAAO;;8BACpC,6LAAC;oBAAY,QAAQ;oBAAQ,SAAQ;;;;;;8BACrC,6LAAC;oBAAG,WAAU;8BAA2D,OAAO,IAAI;;;;;;8BACpF,6LAAC;oBAAa,QAAQ;;;;;;8BACtB,6LAAC;oBAAE,WAAU;8BACV,OAAO,WAAW;;;;;;8BAErB,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAY,CAAC,iBAAiB,EAAE,OAAO,IAAI,EAAE;sCAC9C;;;;;;wBAIF,eAAe,OAAO,OAAO,kBAC5B,6LAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;4BACV,cAAY,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC;sCAE1C,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;MApHwB", "debugId": null}}, {"offset": {"line": 1605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientsList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport ClientCard from './ClientCard';\nimport { getFeaturedClients, type Client } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function Clients() {\n  const [selectedClient, setSelectedClient] = useState<string | null>(null);\n  const [clients, setClients] = useState<Client[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchClients = async () => {\n      try {\n        const featuredClients = await getFeaturedClients();\n        setClients(featuredClients);\n      } catch (error) {\n        console.error('Error fetching clients:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchClients();\n  }, []);\n\n  const openClientModal = (clientId: string) => {\n    setSelectedClient(clientId);\n  };\n\n  const navigateClient = (direction: 'prev' | 'next') => {\n    if (selectedClient === null) return;\n\n    const currentIndex = clients.findIndex(client => client._id === selectedClient);\n    let newIndex;\n\n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : clients.length - 1;\n    } else {\n      newIndex = currentIndex < clients.length - 1 ? currentIndex + 1 : 0;\n    }\n\n    setSelectedClient(clients[newIndex]._id);\n  };\n\n  const selectedClientData = clients.find(client => client._id === selectedClient);\n\n  return (\n    <section id=\"clients\" className=\"py-20 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n            Trusted By\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Proud to have collaborated with diverse clients across various industries.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch\">\n            {[...Array(12)].map((_, index) => (\n              <div key={index} className=\"h-24 flex items-center justify-center\">\n                <div className=\"bg-card p-4 rounded-xl shadow-md w-full h-full flex items-center justify-center min-h-[80px]\">\n                  <div className=\"h-12 w-20 bg-gray-200 rounded animate-pulse\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : clients.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No clients available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch\">\n            {clients.map((client, index) => (\n              <Dialog key={client._id}>\n                <DialogTrigger asChild>\n                  <div className={`h-24 flex items-center justify-center animate-on-scroll stagger-${Math.min(index % 6 + 1, 6)}`}>\n                    <ClientCard\n                      client={client}\n                      variant=\"logo\"\n                      onClick={() => openClientModal(client._id)}\n                      className=\"min-h-[80px] hover:shadow-lg transition-all duration-300 hover:scale-105\"\n                    />\n                  </div>\n                </DialogTrigger>\n              <DialogContent className=\"max-w-2xl\">\n                <div className=\"relative\">\n                  {/* Navigation Arrows */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -left-12 top-1/2 -translate-y-1/2 z-10\"\n                    onClick={() => navigateClient('prev')}\n                  >\n                    <ChevronLeft size={20} />\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -right-12 top-1/2 -translate-y-1/2 z-10\"\n                    onClick={() => navigateClient('next')}\n                  >\n                    <ChevronRight size={20} />\n                  </Button>\n\n                  {selectedClientData && (\n                    <ClientCard\n                      client={selectedClientData}\n                      variant=\"detailed\"\n                      showWebsite={true}\n                    />\n                  )}\n                </div>\n              </DialogContent>\n            </Dialog>\n            ))}\n          </div>\n        )}\n\n        {/* View All Clients Button */}\n        <div className=\"text-center mt-12\">\n          <Link href=\"/clients\">\n            <Button className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n              View All Clients\n            </Button>\n          </Link>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8\">\n          {[\n            { number: '50+', label: 'Projects Completed' },\n            { number: '25+', label: 'Happy Clients' },\n            { number: '1M+', label: 'Views Generated' },\n            { number: '98%', label: 'Client Satisfaction' },\n          ].map((stat, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-heading font-bold text-secondary mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-muted-foreground font-medium\">\n                {stat.label}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;kDAAe;oBACnB,IAAI;wBACF,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD;wBAC/C,WAAW;oBACb,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;4BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,mBAAmB,MAAM;QAE7B,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;QAChE,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,QAAQ,MAAM,GAAG;QACpE,OAAO;YACL,WAAW,eAAe,QAAQ,MAAM,GAAG,IAAI,eAAe,IAAI;QACpE;QAEA,kBAAkB,OAAO,CAAC,SAAS,CAAC,GAAG;IACzC;IAEA,MAAM,qBAAqB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;IAEjE,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;QAAsB,KAAK;kBACzD,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;gBAKhE,wBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,6LAAC;4BAAgB,WAAU;sCACzB,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;2BAFT;;;;;;;;;2BAOZ,QAAQ,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,OAAO;8CACpB,cAAA,6LAAC;wCAAI,WAAW,CAAC,gEAAgE,EAAE,KAAK,GAAG,CAAC,QAAQ,IAAI,GAAG,IAAI;kDAC7G,cAAA,6LAAC,0JAAA,CAAA,UAAU;4CACT,QAAQ;4CACR,SAAQ;4CACR,SAAS,IAAM,gBAAgB,OAAO,GAAG;4CACzC,WAAU;;;;;;;;;;;;;;;;8CAIlB,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;;;;;;0DAErB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,MAAM;;;;;;;;;;;4CAGrB,oCACC,6LAAC,0JAAA,CAAA,UAAU;gDACT,QAAQ;gDACR,SAAQ;gDACR,aAAa;;;;;;;;;;;;;;;;;;2BAnCR,OAAO,GAAG;;;;;;;;;;8BA8C7B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;sCAA0J;;;;;;;;;;;;;;;;8BAOhL,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,QAAQ;4BAAO,OAAO;wBAAqB;wBAC7C;4BAAE,QAAQ;4BAAO,OAAO;wBAAgB;wBACxC;4BAAE,QAAQ;4BAAO,OAAO;wBAAkB;wBAC1C;4BAAE,QAAQ;4BAAO,OAAO;wBAAsB;qBAC/C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,6LAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BALL;;;;;;;;;;;;;;;;;;;;;AAatB;GAlJwB;;QAID,qIAAA,CAAA,8BAA2B;;;KAJ1B", "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/reels/ReelsList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Play, X, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Dialog, DialogContent, DialogClose } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { getFeaturedReels, type Reel, getYouTubeShortsThumbnail } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function Reels() {\n  const [selectedReel, setSelectedReel] = useState<string | null>(null);\n  const [reels, setReels] = useState<Reel[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchReels = async () => {\n      try {\n        const featuredReels = await getFeaturedReels();\n        console.log(featuredReels);\n        // Show only first 12 shorts for featured section\n        const shortsWithThumbnails = featuredReels.slice(0, 12).map(reel => ({\n          ...reel,\n          thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id)\n        }));\n        setReels(shortsWithThumbnails);\n      } catch (error) {\n        console.error('Error fetching featured shorts:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchReels();\n  }, []);\n\n  const openReelModal = (reelId: string) => {\n    setSelectedReel(reelId);\n  };\n\n  const navigateReel = (direction: 'prev' | 'next') => {\n    if (!selectedReel) return;\n\n    const currentIndex = reels.findIndex(reel => reel.id === selectedReel);\n    let newIndex;\n\n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : reels.length - 1;\n    } else {\n      newIndex = currentIndex < reels.length - 1 ? currentIndex + 1 : 0;\n    }\n\n    setSelectedReel(reels[newIndex].id);\n  };\n\n  const selectedReelData = reels.find(reel => reel.id === selectedReel);\n\n  // if(!loading && reels.length !== 0) {\n  //   return console.log(reels);\n  // }\n\n  return (\n    <section id=\"shorts\" className=\"py-20 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-foreground mb-4\">\n            YouTube Shorts\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto font-body\">\n            Creative short-form content showcasing dynamic editing and storytelling.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {[...Array(12)].map((_, index) => (\n              <div key={index} className=\"bg-card rounded-xl overflow-hidden shadow-lg\">\n                <div className=\"aspect-[9/16] bg-gray-200 animate-pulse\"></div>\n                <div className=\"p-3\">\n                  <div className=\"h-4 bg-gray-200 rounded animate-pulse mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : reels.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground font-body\">No YouTube Shorts available at the moment.</p>\n          </div>\n        ) : (\n          <>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n              {reels.map((reel, index) => (\n                <div\n                  key={reel._id}\n                  className={`group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 animate-on-scroll animate-scale-in stagger-${Math.min(index % 6 + 1, 6)}`}\n                >\n                  <div className=\"relative aspect-[9/16] overflow-hidden\">\n                    <Image\n                      src={reel.thumbnail || ''}\n                      alt={reel.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Play Button Overlay */}\n                    <Button\n                      variant=\"ghost\"\n                      className=\"absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 h-full w-full rounded-none hover:bg-black/40\"\n                      onClick={() => openReelModal(reel.id)}\n                    >\n                      <div className=\"w-12 h-12 bg-accent hover:bg-accent/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg\">\n                        <Play size={16} className=\"text-accent-foreground ml-0.5\" fill=\"currentColor\" />\n                      </div>\n                    </Button>\n\n                    {/* Platform Badge */}\n                    <div className=\"absolute top-2 left-2\">\n                      <Badge variant=\"secondary\" className=\"bg-accent/90 text-accent-foreground text-xs font-accent\">\n                        YouTube Shorts\n                      </Badge>\n                    </div>\n                  </div>\n\n                  <div className=\"p-3\">\n                    <h3 className=\"text-sm font-heading font-semibold text-foreground mb-1 group-hover:text-accent transition-colors duration-300 line-clamp-2\">\n                      {reel.title}\n                    </h3>\n                    {reel.description && (\n                      <p className=\"text-muted-foreground text-xs leading-relaxed line-clamp-2 font-body\">\n                        {reel.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Single Dialog for all reels */}\n            <Dialog\n              open={selectedReel !== null}\n              onOpenChange={(open) => {\n                if (!open) {\n                  setSelectedReel(null);\n                }\n              }}\n            >\n              <DialogContent className=\"max-w-lg w-[95vw] p-0 bg-black border-0\">\n                <div className=\"relative\">\n                  {/* Navigation Arrows */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -left-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent\"\n                    onClick={() => navigateReel('prev')}\n                  >\n                    <ChevronLeft size={20} />\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -right-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent\"\n                    onClick={() => navigateReel('next')}\n                  >\n                    <ChevronRight size={20} />\n                  </Button>\n\n                  <DialogClose asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"icon\"\n                      className=\"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2\"\n                    >\n                      <X size={24} />\n                    </Button>\n                  </DialogClose>\n\n                  <div className=\"aspect-[9/16] max-h-[85vh] w-full\">\n                    {selectedReel && selectedReelData?.embedUrl && (\n                      <iframe\n                        src={selectedReelData.embedUrl}\n                        title={selectedReelData.title}\n                        className=\"w-full h-full rounded-lg\"\n                        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                        allowFullScreen\n                      />\n                    )}\n                  </div>\n                </div>\n              </DialogContent>\n            </Dialog>\n          </>\n        )}\n\n        {/* View More Button */}\n        <div className=\"text-center mt-12\">\n          <Button asChild className=\"px-8 py-3 rounded-full font-semibold font-accent transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n            <Link href=\"/reels\">\n              View All YouTube Shorts\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;8CAAa;oBACjB,IAAI;wBACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD;wBAC3C,QAAQ,GAAG,CAAC;wBACZ,iDAAiD;wBACjD,MAAM,uBAAuB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG;+EAAC,CAAA,OAAQ,CAAC;oCACnE,GAAG,IAAI;oCACP,WAAW,KAAK,SAAS,IAAI,CAAA,GAAA,oHAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,EAAE;gCAChE,CAAC;;wBACD,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;0BAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;IAClB;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,cAAc;QAEnB,MAAM,eAAe,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACzD,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,MAAM,MAAM,GAAG;QAClE,OAAO;YACL,WAAW,eAAe,MAAM,MAAM,GAAG,IAAI,eAAe,IAAI;QAClE;QAEA,gBAAgB,KAAK,CAAC,SAAS,CAAC,EAAE;IACpC;IAEA,MAAM,mBAAmB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAExD,uCAAuC;IACvC,+BAA+B;IAC/B,IAAI;IAEJ,qBACE,6LAAC;QAAQ,IAAG;QAAS,WAAU;QAAsB,KAAK;kBACxD,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,6LAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;gBAK1E,wBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJT;;;;;;;;;2BASZ,MAAM,MAAM,KAAK,kBACnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;yCAGzD;;sCACE,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAEC,WAAW,CAAC,2JAA2J,EAAE,KAAK,GAAG,CAAC,QAAQ,IAAI,GAAG,IAAI;;sDAErM,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,SAAS,IAAI;oDACvB,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,cAAc,KAAK,EAAE;8DAEpC,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;4DAAgC,MAAK;;;;;;;;;;;;;;;;8DAKnE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAA0D;;;;;;;;;;;;;;;;;sDAMnG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;gDAEZ,KAAK,WAAW,kBACf,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;;mCApClB,KAAK,GAAG;;;;;;;;;;sCA6CnB,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAM,iBAAiB;4BACvB,cAAc,CAAC;gCACb,IAAI,CAAC,MAAM;oCACT,gBAAgB;gCAClB;4BACF;sCAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,6LAAC,uNAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;;;;;;sDAErB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;sDAGtB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,OAAO;sDAClB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,MAAM;;;;;;;;;;;;;;;;sDAIb,6LAAC;4CAAI,WAAU;sDACZ,gBAAgB,kBAAkB,0BACjC,6LAAC;gDACC,KAAK,iBAAiB,QAAQ;gDAC9B,OAAO,iBAAiB,KAAK;gDAC7B,WAAU;gDACV,OAAM;gDACN,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAW/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GApMwB;;QAID,qIAAA,CAAA,8BAA2B;;;KAJ1B", "debugId": null}}, {"offset": {"line": 2370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogCard.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\nimport { Calendar, Clock, User } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { formatDate, type BlogPost } from '@/lib/api';\n\ninterface PostCardProps {\n  post: BlogPost;\n  variant?: 'default' | 'featured' | 'compact';\n  showExcerpt?: boolean;\n  showAuthor?: boolean;\n  className?: string;\n}\n\nexport default function PostCard({\n  post,\n  variant = 'default',\n  showExcerpt = true,\n  showAuthor = true,\n  className = ''\n}: PostCardProps) {\n  const cardClasses = {\n    default: 'bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',\n    featured: 'bg-card rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 border-2 border-accent',\n    compact: 'bg-card rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'\n  };\n\n  const imageClasses = {\n    default: 'aspect-video',\n    featured: 'aspect-video',\n    compact: 'aspect-[4/3]'\n  };\n\n  const contentClasses = {\n    default: 'p-6',\n    featured: 'p-8',\n    compact: 'p-4'\n  };\n\n  const titleClasses = {\n    default: 'text-xl font-heading font-semibold text-primary mb-3',\n    featured: 'text-2xl font-heading font-bold text-primary mb-4',\n    compact: 'text-lg font-heading font-semibold text-primary mb-2'\n  };\n\n  return (\n    <article className={`group ${cardClasses[variant]} ${className}`}>\n      <Link href={`/blog/${post.slug}`}>\n        {/* Featured Image */}\n        <div className={`relative ${imageClasses[variant]} overflow-hidden`}>\n          {post.thumbnail ? (\n            <Image\n              src={post.thumbnail}\n              alt={post.title}\n              fill\n              className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n            />\n          ) : (\n            <div className=\"w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center\">\n              <div className=\"text-primary-foreground text-6xl font-heading font-bold opacity-20\">\n                {post.title.charAt(0)}\n              </div>\n            </div>\n          )}\n\n          {/* Overlay with badges */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n          {/* Category Badge */}\n          <div className=\"absolute top-4 left-4\">\n            <Badge variant=\"secondary\" className=\"bg-secondary/90 text-secondary-foreground backdrop-blur-sm\">\n              {post.category}\n            </Badge>\n          </div>\n\n          {/* Featured Badge */}\n          {post.featured && (\n            <div className=\"absolute top-4 right-4\">\n              <Badge variant=\"default\" className=\"bg-accent text-accent-foreground backdrop-blur-sm\">\n                Featured\n              </Badge>\n            </div>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className={contentClasses[variant]}>\n          {/* Title */}\n          <h3 className={`${titleClasses[variant]} group-hover:text-secondary transition-colors duration-300 line-clamp-2`}>\n            {post.title}\n          </h3>\n\n          {/* Meta Information */}\n          <div className=\"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3\">\n            <div className=\"flex items-center gap-1\">\n              <Calendar size={14} />\n              <span>{formatDate(post.createdAt)}</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Clock size={14} />\n              <span>{post.readTime} min read</span>\n            </div>\n            {showAuthor && (\n              <div className=\"flex items-center gap-1\">\n                <User size={14} />\n                <span>Uttam Rimal</span>\n              </div>\n            )}\n          </div>\n\n          {/* Excerpt */}\n          {showExcerpt && post.excerpt && (\n            <p className={`text-muted-foreground leading-relaxed mb-4 ${variant === 'compact' ? 'text-sm line-clamp-2' : 'line-clamp-3'\n              }`}>\n              {post.excerpt}\n            </p>\n          )}\n\n          {/* Tags */}\n          {post.tags && post.tags.length > 0 && (\n            <div className=\"flex flex-wrap gap-2 mb-4\">\n              {post.tags.slice(0, variant === 'compact' ? 2 : 3).map((tag) => (\n                <Badge key={tag} variant=\"outline\" className=\"text-xs hover:bg-primary hover:text-primary-foreground transition-colors\">\n                  #{tag}\n                </Badge>\n              ))}\n              {post.tags.length > (variant === 'compact' ? 2 : 3) && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{post.tags.length - (variant === 'compact' ? 2 : 3)}\n                </Badge>\n              )}\n            </div>\n          )}\n\n          {/* Read More Link */}\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-primary font-semibold text-sm group-hover:text-secondary transition-colors duration-300\">\n              Read More →\n            </span>\n\n            {/* Status indicator for featured variant */}\n            {variant === 'featured' && (\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-2 h-2 rounded-full ${post.status === 'published' ? 'bg-success' : 'bg-warning'\n                  }`} />\n                <span className=\"text-xs text-muted-foreground capitalize\">\n                  {post.status}\n                </span>\n              </div>\n            )}\n          </div>\n        </div>\n      </Link>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AAUe,SAAS,SAAS,EAC/B,IAAI,EACJ,UAAU,SAAS,EACnB,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,YAAY,EAAE,EACA;IACd,MAAM,cAAc;QAClB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;kBAC9D,cAAA,6LAAC,+JAA<PERSON>,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;8BAE9B,6LAAC;oBAAI,WAAW,CAAC,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC;;wBAChE,KAAK,SAAS,iBACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAK,KAAK,KAAK;4BACf,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;iDAGR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,MAAM,CAAC;;;;;;;;;;;sCAMzB,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,KAAK,QAAQ;;;;;;;;;;;wBAKjB,KAAK,QAAQ,kBACZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAoD;;;;;;;;;;;;;;;;;8BAQ7F,6LAAC;oBAAI,WAAW,cAAc,CAAC,QAAQ;;sCAErC,6LAAC;4BAAG,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,uEAAuE,CAAC;sCAC7G,KAAK,KAAK;;;;;;sCAIb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,6LAAC;sDAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,6LAAC;;gDAAM,KAAK,QAAQ;gDAAC;;;;;;;;;;;;;gCAEtB,4BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;wBAMX,eAAe,KAAK,OAAO,kBAC1B,6LAAC;4BAAE,WAAW,CAAC,2CAA2C,EAAE,YAAY,YAAY,yBAAyB,gBACzG;sCACD,KAAK,OAAO;;;;;;wBAKhB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC,oBACtD,6LAAC,oIAAA,CAAA,QAAK;wCAAW,SAAQ;wCAAU,WAAU;;4CAA2E;4CACpH;;uCADQ;;;;;gCAIb,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC,mBAChD,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC;;;;;;;;;;;;;sCAO3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA+F;;;;;;gCAK9G,YAAY,4BACX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,MAAM,KAAK,cAAc,eAAe,cACjF;;;;;;sDACJ,6LAAC;4CAAK,WAAU;sDACb,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B;KA9IwB", "debugId": null}}, {"offset": {"line": 2690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2805, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2839, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 3041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 3322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 3353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/index.ts"], "sourcesContent": ["// UI components barrel exports\nexport * from './badge';\nexport * from './button';\nexport * from './card';\nexport * from './dialog';\nexport * from './form';\nexport * from './input';\nexport * from './label';\nexport * from './select';\nexport * from './textarea';\nexport * from './video-dialog';\n"], "names": [], "mappings": "AAAA,+BAA+B;;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport PostCard from './BlogCard';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { getBlogPosts, type BlogPost } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\nimport { Button } from '@/components/ui';\n\nexport default function Blog() {\n  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchBlogPosts = async () => {\n      try {\n        const posts = await getBlogPosts();\n        // Show only first 6 posts for homepage\n        setBlogPosts(posts.slice(0, 6));\n      } catch (error) {\n        console.error('Error fetching blog posts:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchBlogPosts();\n  }, []);\n\n  return (\n    <section id=\"blog\" className=\"py-20 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4 animate-slide-up\">\n            Insights & Tips\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto animate-fade-in stagger-2\">\n            Sharing knowledge on video editing, storytelling, and creative techniques.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <Card key={index} className=\"bg-white overflow-hidden\">\n                <CardHeader className=\"p-0\">\n                  <div className=\"aspect-video bg-gray-200 animate-pulse\"></div>\n                </CardHeader>\n                <CardContent className=\"p-6\">\n                  <div className=\"space-y-3\">\n                    <div className=\"h-4 bg-gray-200 rounded animate-pulse\"></div>\n                    <div className=\"h-6 bg-gray-200 rounded animate-pulse\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : blogPosts.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No blog posts available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {blogPosts.map((post, index) => (\n              <div\n                key={post._id}\n                className={`animate-on-scroll animate-scale-in stagger-${Math.min(index % 6 + 1, 6)}`}\n              >\n                <PostCard\n                  post={post}\n                  variant=\"default\"\n                  showExcerpt={true}\n                  showAuthor={false}\n                />\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* View All Posts Button */}\n        <div className=\"text-center mt-12\">\n          <Button asChild className=\"px-8 py-3 rounded-full font-semibold font-accent transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n            <Link href=\"/blog\">View All Posts</Link>\n          </Button>\n          {/* <Link\n            href=\"/blog\"\n            className=\"inline-block bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\"\n          >\n            View All Posts\n          </Link> */}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;iDAAiB;oBACrB,IAAI;wBACF,MAAM,QAAQ,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;wBAC/B,uCAAuC;wBACvC,aAAa,MAAM,KAAK,CAAC,GAAG;oBAC9B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;QAAsB,KAAK;kBACtD,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiF;;;;;;sCAG/F,6LAAC;4BAAE,WAAU;sCAA4E;;;;;;;;;;;;gBAK1F,wBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC,mIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;2BARV;;;;;;;;;2BAcb,UAAU,MAAM,KAAK,kBACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;4BAEC,WAAW,CAAC,2CAA2C,EAAE,KAAK,GAAG,CAAC,QAAQ,IAAI,GAAG,IAAI;sCAErF,cAAA,6LAAC,qJAAA,CAAA,UAAQ;gCACP,MAAM;gCACN,SAAQ;gCACR,aAAa;gCACb,YAAY;;;;;;2BAPT,KAAK,GAAG;;;;;;;;;;8BAerB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/B;GAvFwB;;QAGD,qIAAA,CAAA,8BAA2B;;;KAH1B", "debugId": null}}, {"offset": {"line": 3637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport { Quote, Star, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { getFeaturedTestimonials, type Testimonial } from '@/lib/api';\n\n\nexport default function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchTestimonials = async () => {\n      try {\n        const featuredTestimonials = await getFeaturedTestimonials();\n\n        // Check if we got testimonials, if not use fallback data\n        if (featuredTestimonials && featuredTestimonials.length > 0) {\n          setTestimonials(featuredTestimonials);\n        } else {\n          // Fallback testimonials data\n          const fallbackTestimonials = [\n            {\n              _id: '1',\n              name: '<PERSON><PERSON>',\n              slug: 'shahid-kathariya',\n              role: 'Vlogger/YouTuber',\n              content: \"Uttam's editing skills brought our project to life. His attention to detail and creative flair exceeded our expectations.\",\n              rating: 5,\n              featured: true,\n              status: 'published' as const,\n              order: 1,\n              createdAt: new Date().toISOString(),\n              updatedAt: new Date().toISOString()\n            },\n            {\n              _id: '2',\n              name: 'Utsab Pandey',\n              slug: 'utsab-pandey',\n              role: 'Professional Designer',\n              content: \"Working with Uttam was a pleasure. He understood our vision from the start and delivered a final product that was both professional and highly engaging.\",\n              rating: 5,\n              featured: true,\n              status: 'published' as const,\n              order: 2,\n              createdAt: new Date().toISOString(),\n              updatedAt: new Date().toISOString()\n            },\n            {\n              _id: '3',\n              name: 'Sarah Johnson',\n              slug: 'sarah-johnson',\n              role: 'Marketing Director',\n              company: 'Creative Solutions Inc.',\n              content: \"Uttam's creativity and technical skills are truly impressive. He transformed raw footage into a compelling narrative that resonated perfectly with our target audience.\",\n              rating: 5,\n              featured: true,\n              status: 'published' as const,\n              order: 3,\n              createdAt: new Date().toISOString(),\n              updatedAt: new Date().toISOString()\n            }\n          ];\n          setTestimonials(fallbackTestimonials);\n        }\n      } catch (error) {\n        console.error('Error fetching testimonials:', error);\n\n        // Use fallback data on error\n        const fallbackTestimonials = [\n          {\n            _id: '1',\n            name: 'Shahid Kathariya',\n            slug: 'shahid-kathariya',\n            role: 'Vlogger/YouTuber',\n            content: \"Uttam's editing skills brought our project to life. His attention to detail and creative flair exceeded our expectations.\",\n            rating: 5,\n            featured: true,\n            status: 'published' as const,\n            order: 1,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          }\n        ];\n        setTestimonials(fallbackTestimonials);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTestimonials();\n  }, []);\n\n  useEffect(() => {\n    if (!isAutoPlaying || testimonials.length === 0) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) =>\n        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n      );\n    }, 6000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, testimonials.length]);\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10 seconds\n  };\n\n  const goToPrevious = () => {\n    const newIndex = currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1;\n    goToSlide(newIndex);\n  };\n\n  const goToNext = () => {\n    const newIndex = currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1;\n    goToSlide(newIndex);\n  };\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  // Add safety check for currentTestimonial\n  if (!currentTestimonial && !loading) {\n    console.log('No current testimonial found. Testimonials:', testimonials, 'Current Index:', currentIndex);\n  }\n\n  if (loading) {\n    return (\n      <section id=\"testimonials\" className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n              What Clients Say\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Feedback from collaborators and clients who trusted me with their vision.\n            </p>\n          </div>\n          <div className=\"max-w-3xl mx-auto\">\n            <Card className=\"bg-card border-border shadow-lg h-[280px] md:h-[240px]\">\n              <CardContent className=\"p-6 md:p-8 h-full\">\n                <div className=\"flex flex-col md:flex-row items-center md:items-start gap-6 h-full\">\n                  <div className=\"flex flex-col items-center md:items-start flex-shrink-0 md:w-32\">\n                    <div className=\"w-16 h-16 bg-muted/20 rounded-full animate-pulse mb-3\"></div>\n                    <div className=\"h-4 bg-muted/20 rounded animate-pulse w-24 mb-1\"></div>\n                    <div className=\"h-3 bg-muted/20 rounded animate-pulse w-20\"></div>\n                  </div>\n                  <div className=\"flex-1 flex flex-col h-full space-y-3\">\n                    <div className=\"h-8 bg-muted/20 rounded animate-pulse w-8 flex-shrink-0\"></div>\n                    <div className=\"flex-1 space-y-3\">\n                      <div className=\"h-4 bg-muted/20 rounded animate-pulse w-full\"></div>\n                      <div className=\"h-4 bg-muted/20 rounded animate-pulse w-4/5\"></div>\n                      <div className=\"h-4 bg-muted/20 rounded animate-pulse w-3/4\"></div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (testimonials.length === 0) {\n    return (\n      <section id=\"testimonials\" className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n              What Clients Say\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Feedback from collaborators and clients who trusted me with their vision.\n            </p>\n          </div>\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No testimonials available at the moment.</p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-background\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n            What Clients Say\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Feedback from collaborators and clients who trusted me with their vision.\n          </p>\n        </div>\n\n        <div className=\"max-w-3xl mx-auto\">\n          <div className=\"relative\">\n            {/* Navigation Arrows */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute -left-12 top-1/2 -translate-y-1/2 z-10 text-foreground hover:text-accent hover:bg-accent/10 w-10 h-10\"\n              onClick={goToPrevious}\n            >\n              <ChevronLeft size={20} />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute -right-12 top-1/2 -translate-y-1/2 z-10 text-foreground hover:text-accent hover:bg-accent/10 w-10 h-10\"\n              onClick={goToNext}\n            >\n              <ChevronRight size={20} />\n            </Button>\n\n            {/* Testimonial Card */}\n            <Card className=\"bg-card border-border shadow-lg h-[280px] md:h-[240px] hover:shadow-xl transition-all duration-300 opacity-100\">\n              <CardContent className=\"p-6 md:p-8 h-full\">\n                <div className=\"flex flex-col md:flex-row items-center md:items-start gap-6 h-full\">\n                  {/* Left side - Avatar and Info */}\n                  <div className=\"flex flex-col items-center md:items-start flex-shrink-0 md:w-32\">\n                    {/* Avatar */}\n                    <div className=\"relative w-16 h-16 rounded-full overflow-hidden border-3 border-accent/30 mb-3\">\n                      {currentTestimonial?.avatar ? (\n                        <Image\n                          src={currentTestimonial.avatar}\n                          alt={currentTestimonial.name || 'Client'}\n                          fill\n                          className=\"object-cover\"\n                        />\n                      ) : (\n                        <div className=\"w-full h-full bg-accent flex items-center justify-center text-accent-foreground font-heading font-bold text-lg\">\n                          {currentTestimonial?.name?.charAt(0) || 'C'}\n                        </div>\n                      )}\n                    </div>\n\n                    {/* Author Info */}\n                    <div className=\"text-center md:text-left\">\n                      <cite className=\"text-accent font-semibold text-base not-italic block line-clamp-1\">\n                        {currentTestimonial?.name || 'Client Name'}\n                      </cite>\n                      <div className=\"text-muted-foreground text-sm line-clamp-2\">\n                        {currentTestimonial?.role || 'Client'}\n                        {currentTestimonial?.company && (\n                          <span className=\"block md:inline\"> {currentTestimonial.company}</span>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Rating */}\n                    {currentTestimonial?.rating && (\n                      <div className=\"flex gap-1 mt-2\">\n                        {[...Array(5)].map((_, i) => (\n                          <Star\n                            key={i}\n                            size={16}\n                            className={`${i < (currentTestimonial.rating || 0)\n                              ? 'text-warning fill-current'\n                              : 'text-muted-foreground/30'\n                              }`}\n                          />\n                        ))}\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Right side - Content */}\n                  <div className=\"flex-1 flex flex-col h-full md:min-h-0\">\n                    {/* Quote Icon */}\n                    <Quote size={32} className=\"text-accent opacity-40 mb-3 flex-shrink-0\" />\n\n                    {/* Testimonial Content */}\n                    <blockquote className=\"text-base md:text-lg leading-relaxed text-foreground italic flex-1 overflow-hidden\">\n                      <div className=\"line-clamp-4 md:line-clamp-3\">\n                        &ldquo;{currentTestimonial?.content || 'Loading testimonial...'}&rdquo;\n                      </div>\n                    </blockquote>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center gap-3 mt-8\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentIndex\n                  ? 'bg-accent scale-125'\n                  : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'\n                  }`}\n                aria-label={`Go to testimonial ${index + 1}`}\n              />\n            ))}\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 pt-8 border-t border-border/20\">\n            {[\n              { number: '98%', label: 'Client Satisfaction' },\n              { number: '50+', label: 'Projects Delivered' },\n              { number: '25+', label: 'Happy Clients' },\n              { number: '1M+', label: 'Views Generated' },\n            ].map((stat, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-heading font-bold text-accent mb-1\">\n                  {stat.number}\n                </div>\n                <div className=\"text-muted-foreground font-medium text-xs md:text-sm\">\n                  {stat.label}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;4DAAoB;oBACxB,IAAI;wBACF,MAAM,uBAAuB,MAAM,CAAA,GAAA,oHAAA,CAAA,0BAAuB,AAAD;wBAEzD,yDAAyD;wBACzD,IAAI,wBAAwB,qBAAqB,MAAM,GAAG,GAAG;4BAC3D,gBAAgB;wBAClB,OAAO;4BACL,6BAA6B;4BAC7B,MAAM,uBAAuB;gCAC3B;oCACE,KAAK;oCACL,MAAM;oCACN,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,QAAQ;oCACR,OAAO;oCACP,WAAW,IAAI,OAAO,WAAW;oCACjC,WAAW,IAAI,OAAO,WAAW;gCACnC;gCACA;oCACE,KAAK;oCACL,MAAM;oCACN,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,QAAQ;oCACR,OAAO;oCACP,WAAW,IAAI,OAAO,WAAW;oCACjC,WAAW,IAAI,OAAO,WAAW;gCACnC;gCACA;oCACE,KAAK;oCACL,MAAM;oCACN,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,SAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,QAAQ;oCACR,OAAO;oCACP,WAAW,IAAI,OAAO,WAAW;oCACjC,WAAW,IAAI,OAAO,WAAW;gCACnC;6BACD;4BACD,gBAAgB;wBAClB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;wBAE9C,6BAA6B;wBAC7B,MAAM,uBAAuB;4BAC3B;gCACE,KAAK;gCACL,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,SAAS;gCACT,QAAQ;gCACR,UAAU;gCACV,QAAQ;gCACR,OAAO;gCACP,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;yBACD;wBACD,gBAAgB;oBAClB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,iBAAiB,aAAa,MAAM,KAAK,GAAG;YAEjD,MAAM,WAAW;mDAAY;oBAC3B;2DAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;;gBAE5D;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;QAAe,aAAa,MAAM;KAAC;IAEvC,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO,QAAQ,oCAAoC;IACvF;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,iBAAiB,IAAI,aAAa,MAAM,GAAG,IAAI,eAAe;QAC/E,UAAU;IACZ;IAEA,MAAM,WAAW;QACf,MAAM,WAAW,iBAAiB,aAAa,MAAM,GAAG,IAAI,IAAI,eAAe;QAC/E,UAAU;IACZ;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,0CAA0C;IAC1C,IAAI,CAAC,sBAAsB,CAAC,SAAS;QACnC,QAAQ,GAAG,CAAC,+CAA+C,cAAc,kBAAkB;IAC7F;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAQ,IAAG;YAAe,WAAU;sBACnC,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgE;;;;;;0CAG9E,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUnC;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,qBACE,6LAAC;YAAQ,IAAG;YAAe,WAAU;sBACnC,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgE;;;;;;0CAG9E,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;IAKvD;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,MAAM;;;;;;;;;;;8CAErB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,MAAM;;;;;;;;;;;8CAItB,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEACZ,oBAAoB,uBACnB,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK,mBAAmB,MAAM;gEAC9B,KAAK,mBAAmB,IAAI,IAAI;gEAChC,IAAI;gEACJ,WAAU;;;;;qFAGZ,6LAAC;gEAAI,WAAU;0EACZ,oBAAoB,MAAM,OAAO,MAAM;;;;;;;;;;;sEAM9C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,oBAAoB,QAAQ;;;;;;8EAE/B,6LAAC;oEAAI,WAAU;;wEACZ,oBAAoB,QAAQ;wEAC5B,oBAAoB,yBACnB,6LAAC;4EAAK,WAAU;;gFAAkB;gFAAE,mBAAmB,OAAO;;;;;;;;;;;;;;;;;;;wDAMnE,oBAAoB,wBACnB,6LAAC;4DAAI,WAAU;sEACZ;mEAAI,MAAM;6DAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;oEAEH,MAAM;oEACN,WAAW,GAAG,IAAI,CAAC,mBAAmB,MAAM,IAAI,CAAC,IAC7C,8BACA,4BACA;mEALC;;;;;;;;;;;;;;;;8DAaf,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAG3B,6LAAC;4DAAW,WAAU;sEACpB,cAAA,6LAAC;gEAAI,WAAU;;oEAA+B;oEACpC,oBAAoB,WAAW;oEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU9E,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,iDAAiD,EAAE,UAAU,eACrE,wBACA,uDACA;oCACJ,cAAY,CAAC,kBAAkB,EAAE,QAAQ,GAAG;mCANvC;;;;;;;;;;sCAYX,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,QAAQ;oCAAO,OAAO;gCAAsB;gCAC9C;oCAAE,QAAQ;oCAAO,OAAO;gCAAqB;gCAC7C;oCAAE,QAAQ;oCAAO,OAAO;gCAAgB;gCACxC;oCAAE,QAAQ;oCAAO,OAAO;gCAAkB;6BAC3C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCALL;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcxB;GA/TwB;KAAA", "debugId": null}}, {"offset": {"line": 4312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Send, Mail, Phone, MapPin, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const containerRef = useStaggeredScrollAnimation();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n      setTimeout(() => setSubmitStatus('idle'), 5000);\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: 'Email',\n      value: '<EMAIL>',\n      href: 'mailto:<EMAIL>'\n    },\n    {\n      icon: Phone,\n      label: 'Phone',\n      value: '+977 ************',\n      href: 'tel:+9779840692118'\n    },\n    {\n      icon: MapPin,\n      label: 'Location',\n      value: 'Kathmandu, Nepal',\n      href: '#'\n    }\n  ];\n\n  const socialLinks = [\n    { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },\n    { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },\n    { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },\n    { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-16 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4 animate-slide-up\">\n            Let&apos;s Create Together\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto animate-fade-in stagger-2\">\n            Have a project in mind? Let&apos;s discuss how we can bring your vision to life through compelling video content.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n          {/* Contact Form */}\n          <Card className=\"shadow-lg animate-on-scroll animate-slide-in-left stagger-3 hover:shadow-xl transition-all duration-300\">\n            <CardHeader>\n              <CardTitle className=\"text-2xl font-heading text-primary\">\n                Send a Message\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleSubmit} className=\"space-y-5\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"name\">Your Name *</Label>\n                    <Input\n                      id=\"name\"\n                      name=\"name\"\n                      type=\"text\"\n                      placeholder=\"e.g., Jane Doe\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"focus:ring-ring focus:border-ring\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"email\">Your Email *</Label>\n                    <Input\n                      id=\"email\"\n                      name=\"email\"\n                      type=\"email\"\n                      placeholder=\"e.g., <EMAIL>\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"focus:ring-ring focus:border-ring\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"subject\">Subject</Label>\n                  <Input\n                    id=\"subject\"\n                    name=\"subject\"\n                    type=\"text\"\n                    placeholder=\"e.g., Video Editing Inquiry\"\n                    value={formData.subject}\n                    onChange={handleInputChange}\n                    className=\"focus:ring-ring focus:border-ring\"\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"message\">Your Message *</Label>\n                  <Textarea\n                    id=\"message\"\n                    name=\"message\"\n                    placeholder=\"Tell me about your project...\"\n                    value={formData.message}\n                    onChange={handleInputChange}\n                    required\n                    rows={5}\n                    className=\"focus:ring-ring focus:border-ring resize-none\"\n                  />\n                </div>\n\n                <Button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  variant=\"secondary\"\n                  className=\"w-full py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 disabled:scale-100\"\n                >\n                  {isSubmitting ? (\n                    'Sending...'\n                  ) : (\n                    <>\n                      Send Message\n                      <Send size={18} className=\"ml-2\" />\n                    </>\n                  )}\n                </Button>\n\n                {/* Status Messages */}\n                {submitStatus === 'success' && (\n                  <div className=\"text-success text-center font-medium\">\n                    Thank you! Your message has been sent successfully.\n                  </div>\n                )}\n                {submitStatus === 'error' && (\n                  <div className=\"text-destructive text-center font-medium\">\n                    Sorry, there was an error sending your message. Please try again.\n                  </div>\n                )}\n              </form>\n            </CardContent>\n          </Card>\n\n          {/* Contact Information */}\n          <div className=\"space-y-6 animate-on-scroll animate-slide-in-right stagger-4\">\n            <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-heading text-primary\">\n                  Get in Touch\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-5\">\n                {contactInfo.map((info, index) => (\n                  <div key={index} className=\"flex items-center gap-4\">\n                    <div className=\"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center\">\n                      <info.icon size={20} className=\"text-primary\" />\n                    </div>\n                    <div>\n                      <div className=\"font-semibold text-primary\">{info.label}</div>\n                      {info.href !== '#' ? (\n                        <a\n                          href={info.href}\n                          className=\"text-muted-foreground hover:text-secondary transition-colors\"\n                        >\n                          {info.value}\n                        </a>\n                      ) : (\n                        <div className=\"text-muted-foreground\">{info.value}</div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </CardContent>\n            </Card>\n\n            {/* Social Links */}\n            <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in stagger-5\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-heading text-primary\">\n                  Follow Me\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex gap-4\">\n                  {socialLinks.map(({ icon: Icon, href, label }) => (\n                    <a\n                      key={label}\n                      href={href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-12 h-12 bg-primary/10 hover:bg-accent text-primary hover:text-accent-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110\"\n                      aria-label={label}\n                    >\n                      <Icon size={20} />\n                    </a>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* WhatsApp Quick Contact */}\n            <Card className=\"shadow-lg bg-success/10 border-success/20 hover:shadow-xl transition-all duration-300 animate-glow animate-scale-in stagger-6\">\n              <CardContent className=\"p-6\">\n                <div className=\"text-center\">\n                  <h3 className=\"font-heading font-semibold text-success mb-2\">\n                    Quick Chat on WhatsApp\n                  </h3>\n                  <p className=\"text-success/80 text-sm mb-4\">\n                    Need immediate assistance? Let&apos;s chat!\n                  </p>\n                  <Button\n                    asChild\n                    className=\"bg-success hover:bg-success/90 text-success-foreground px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105\"\n                  >\n                    <a\n                      href=\"https://wa.me/9779840692118\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"inline-flex items-center gap-2\"\n                    >\n                      <Phone size={18} />\n                      Chat on WhatsApp\n                    </a>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,gBAAgB;YAChB,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,EAAE,OAAM;YACN,gBAAgB;QAClB,SAAU;YACR,gBAAgB;YAChB,WAAW,IAAM,gBAAgB,SAAS;QAC5C;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;YAA0C,OAAO;QAAW;QACpF;YAAE,MAAM,+MAAA,CAAA,YAAS;YAAE,MAAM;YAAwC,OAAO;QAAY;QACpF;YAAE,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;YAAwC,OAAO;QAAU;QAChF;YAAE,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;YAAuC,OAAO;QAAW;KAClF;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;QAAsB,KAAK;kBACzD,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiF;;;;;;sCAG/F,6LAAC;4BAAE,WAAU;sCAA4E;;;;;;;;;;;;8BAK3F,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqC;;;;;;;;;;;8CAI5D,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;;;;;;;;;;;;0DAId,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;;;;;;;;;;;;0DAId,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU;gDACV,SAAQ;gDACR,WAAU;0DAET,eACC,6BAEA;;wDAAE;sEAEA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;;4CAM/B,iBAAiB,2BAChB,6LAAC;gDAAI,WAAU;0DAAuC;;;;;;4CAIvD,iBAAiB,yBAChB,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;sCASlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAqC;;;;;;;;;;;sDAI5D,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,KAAK,IAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;;;;;;sEAEjC,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAA8B,KAAK,KAAK;;;;;;gEACtD,KAAK,IAAI,KAAK,oBACb,6LAAC;oEACC,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;yFAGb,6LAAC;oEAAI,WAAU;8EAAyB,KAAK,KAAK;;;;;;;;;;;;;mDAd9C;;;;;;;;;;;;;;;;8CAuBhB,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAqC;;;;;;;;;;;sDAI5D,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC3C,6LAAC;wDAEC,MAAM;wDACN,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,cAAY;kEAEZ,cAAA,6LAAC;4DAAK,MAAM;;;;;;uDAPP;;;;;;;;;;;;;;;;;;;;;8CAef,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA+C;;;;;;8DAG7D,6LAAC;oDAAE,WAAU;8DAA+B;;;;;;8DAG5C,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;8DAEV,cAAA,6LAAC;wDACC,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;;0EAEV,6LAAC,uMAAA,CAAA,QAAK;gEAAC,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC;GAtQwB;;QASD,qIAAA,CAAA,8BAA2B;;;KAT1B", "debugId": null}}, {"offset": {"line": 4918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/ScrollToTop.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <Button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n      aria-label=\"Scroll to top\"\n    >\n      <ArrowUp size={20} />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,OAAO,WAAW,GAAG,KAAK;wBAC5B,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,6LAAC,+MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;;;;;;AAGrB;GApCwB;KAAA", "debugId": null}}]}