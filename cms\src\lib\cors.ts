// CORS middleware for API routes
import { NextRequest, NextResponse } from 'next/server';

export function corsHeaders(origin?: string) {
  const allowedOrigins = getAllowedOrigins();
  const originAllowed = isOriginAllowed(origin || '', allowedOrigins);

  // In development or if no specific origin is configured, be more permissive
  const isDevelopment = process.env.NODE_ENV === 'development';
  const hasProductionConfig = process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_FRONTEND_URL;

  let allowOrigin: string;
  if (originAllowed) {
    allowOrigin = origin || '*';
  } else if (isDevelopment || !hasProductionConfig) {
    allowOrigin = '*';
  } else {
    allowOrigin = 'null';
  }

  return {
    'Access-Control-Allow-Origin': allowOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Allow-Credentials': (originAllowed || allowOrigin === '*') ? 'true' : 'false',
  };
}

export function handleCors(request: NextRequest) {
  const origin = request.headers.get('origin');
  const headers = corsHeaders(origin || undefined);

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, { status: 200, headers });
  }

  return headers;
}

// Helper function to get allowed origins
export function getAllowedOrigins(): string[] {
  const frontendUrl = process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_FRONTEND_URL;
  const additionalOrigins = process.env.ADDITIONAL_CORS_ORIGINS?.split(',').map(url => url.trim()) || [];

  const baseOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    // Add production domains
    'https://uttamrimal.com.np',
    'https://www.uttamrimal.com.np',
    'https://uttam-portfolio.vercel.app',
    'https://uttam-portfolio-git-main.vercel.app',
    // Add any custom frontend URL from environment
    ...(frontendUrl ? [frontendUrl] : []),
    // Add additional origins from environment
    ...additionalOrigins,
  ].filter(Boolean); // Remove any undefined values

  // Remove duplicates
  return [...new Set(baseOrigins)];
}

// Helper function to check if origin is allowed (with wildcard support)
export function isOriginAllowed(origin: string | null, allowedOrigins: string[]): boolean {
  if (!origin) return false;

  // Direct match
  if (allowedOrigins.includes(origin)) return true;

  // Check for wildcard patterns (e.g., *.vercel.app)
  return allowedOrigins.some(allowed => {
    if (allowed.includes('*')) {
      const pattern = allowed.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(origin);
    }
    return false;
  });
}

// Helper function to set CORS headers on a response
export function setCorsHeaders(response: NextResponse, origin?: string): void {
  const allowedOrigins = getAllowedOrigins();
  const originAllowed = isOriginAllowed(origin || '', allowedOrigins);

  // In development or if no specific origin is configured, be more permissive
  const isDevelopment = process.env.NODE_ENV === 'development';
  const hasProductionConfig = process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_FRONTEND_URL;

  let allowOrigin: string;
  if (originAllowed) {
    allowOrigin = origin || '*';
  } else if (isDevelopment || !hasProductionConfig) {
    allowOrigin = '*';
  } else {
    allowOrigin = 'null';
  }

  response.headers.set('Access-Control-Allow-Origin', allowOrigin);
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  response.headers.set('Access-Control-Allow-Credentials', (originAllowed || allowOrigin === '*') ? 'true' : 'false');
}

// Enhanced CORS handler with logging
export function createCorsResponse(origin?: string, status: number = 200): NextResponse {
  const response = new NextResponse(null, { status });
  setCorsHeaders(response, origin);

  // Log CORS decisions in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`CORS: Origin "${origin}" ${isOriginAllowed(origin || '', getAllowedOrigins()) ? 'ALLOWED' : 'DENIED'}`);
  }

  return response;
}

export function withCors(handler: (request: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest) => {
    const origin = request.headers.get('origin');
    const corsHeadersObj = corsHeaders(origin || undefined);

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, { status: 200, headers: corsHeadersObj });
    }

    try {
      const response = await handler(request);

      // Add CORS headers to the response
      Object.entries(corsHeadersObj).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;
    } catch (error) {
      console.error('API Error:', error);
      const errorResponse = NextResponse.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      );

      // Add CORS headers to error response
      Object.entries(corsHeadersObj).forEach(([key, value]) => {
        errorResponse.headers.set(key, value);
      });

      return errorResponse;
    }
  };
}
