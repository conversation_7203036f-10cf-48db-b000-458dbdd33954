exports.id=9638,exports.ids=[9638],exports.modules={1724:(e,t,a)=>{Promise.resolve().then(a.bind(a,29131))},4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var r=a(49384),s=a(82348);function i(...e){return(0,s.QP)((0,r.$)(e))}},29131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>s});var r=a(12907);let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\contexts\\AuthContext.tsx","useAuth")},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var r=a(60687);a(43210);var s=a(8730),i=a(24224),n=a(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:a,asChild:i=!1,...d}){let l=i?s.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:a,className:e})),...d})}},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var r=a(60687);a(43210);var s=a(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},51921:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},61135:()=>{},63213:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>n});var r=a(60687),s=a(43210);let i=(0,s.createContext)(void 0);function n({children:e}){let[t,a]=(0,s.useState)(null),[n,o]=(0,s.useState)(!0),d=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();a(t.user)}else a(null)}catch(e){console.error("Auth check failed:",e),a(null)}finally{o(!1)}},l=async(e,t)=>{try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),s=await r.json();if(r.ok)return a(s.user),{success:!0};return{success:!1,error:s.error||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error"}}},c=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{a(null)}};return(0,r.jsx)(i.Provider,{value:{user:t,loading:n,login:l,logout:c,checkAuth:d},children:e})}function o(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72455:(e,t,a)=>{"use strict";a.d(t,{A:()=>eg});var r=a(60687),s=a(63213),i=a(16189),n=a(43210),o=a(8730),d=a(24224),l=a(51214),c=a(4780),u=a(29523);a(89667);var h=a(62369);function b({className:e,orientation:t="horizontal",decorative:a=!0,...s}){return(0,r.jsx)(h.b,{"data-slot":"separator-root",decorative:a,orientation:t,className:(0,c.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...s})}var f=a(26134),x=a(11860);function m({...e}){return(0,r.jsx)(f.bL,{"data-slot":"sheet",...e})}function p({...e}){return(0,r.jsx)(f.ZL,{"data-slot":"sheet-portal",...e})}function g({className:e,...t}){return(0,r.jsx)(f.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function v({className:e,children:t,side:a="right",...s}){return(0,r.jsxs)(p,{children:[(0,r.jsx)(g,{}),(0,r.jsxs)(f.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===a&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===a&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===a&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===a&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...s,children:[t,(0,r.jsxs)(f.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(x.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function j({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,c.cn)("flex flex-col gap-1.5 p-4",e),...t})}function w({className:e,...t}){return(0,r.jsx)(f.hE,{"data-slot":"sheet-title",className:(0,c.cn)("text-foreground font-semibold",e),...t})}function y({className:e,...t}){return(0,r.jsx)(f.VY,{"data-slot":"sheet-description",className:(0,c.cn)("text-muted-foreground text-sm",e),...t})}var N=a(20158);function A({delayDuration:e=0,...t}){return(0,r.jsx)(N.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function k({...e}){return(0,r.jsx)(A,{children:(0,r.jsx)(N.bL,{"data-slot":"tooltip",...e})})}function C({...e}){return(0,r.jsx)(N.l9,{"data-slot":"tooltip-trigger",...e})}function P({className:e,sideOffset:t=0,children:a,...s}){return(0,r.jsx)(N.ZL,{children:(0,r.jsxs)(N.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,c.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...s,children:[a,(0,r.jsx)(N.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let z=n.createContext(null);function _(){let e=n.useContext(z);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function S({defaultOpen:e=!0,open:t,onOpenChange:a,className:s,style:i,children:o,...d}){let l=function(){let[e,t]=n.useState(void 0);return n.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[u,h]=n.useState(!1),[b,f]=n.useState(e),x=t??b,m=n.useCallback(e=>{let t="function"==typeof e?e(x):e;a?a(t):f(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[a,x]),p=n.useCallback(()=>l?h(e=>!e):m(e=>!e),[l,m,h]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),p())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[p]);let g=x?"expanded":"collapsed",v=n.useMemo(()=>({state:g,open:x,setOpen:m,isMobile:l,openMobile:u,setOpenMobile:h,toggleSidebar:p}),[g,x,m,l,u,h,p]);return(0,r.jsx)(z.Provider,{value:v,children:(0,r.jsx)(A,{delayDuration:0,children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...i},className:(0,c.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...d,children:o})})})}function L({side:e="left",variant:t="sidebar",collapsible:a="offcanvas",className:s,children:i,...n}){let{isMobile:o,state:d,openMobile:l,setOpenMobile:u}=_();return"none"===a?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,c.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",s),...n,children:i}):o?(0,r.jsx)(m,{open:l,onOpenChange:u,...n,children:(0,r.jsxs)(v,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,r.jsxs)(j,{className:"sr-only",children:[(0,r.jsx)(w,{children:"Sidebar"}),(0,r.jsx)(y,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:i})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":d,"data-collapsible":"collapsed"===d?a:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,c.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,c.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",s),...n,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:i})})]})}function D({className:e,onClick:t,...a}){let{toggleSidebar:s}=_();return(0,r.jsxs)(u.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,c.cn)("size-7",e),onClick:e=>{t?.(e),s()},...a,children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function E({className:e,...t}){let{toggleSidebar:a}=_();return(0,r.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:a,title:"Toggle Sidebar",className:(0,c.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}function T({className:e,...t}){return(0,r.jsx)("main",{"data-slot":"sidebar-inset",className:(0,c.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function R({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,c.cn)("flex flex-col gap-2 p-2",e),...t})}function M({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,c.cn)("flex flex-col gap-2 p-2",e),...t})}function O({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,c.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function U({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,c.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t})}function V({className:e,asChild:t=!1,...a}){let s=t?o.DX:"div";return(0,r.jsx)(s,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,c.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...a})}function B({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,c.cn)("w-full text-sm",e),...t})}function $({className:e,...t}){return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,c.cn)("flex w-full min-w-0 flex-col gap-1",e),...t})}function K({className:e,...t}){return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,c.cn)("group/menu-item relative",e),...t})}let W=(0,d.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function X({asChild:e=!1,isActive:t=!1,variant:a="default",size:s="default",tooltip:i,className:n,...d}){let l=e?o.DX:"button",{isMobile:u,state:h}=_(),b=(0,r.jsx)(l,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":s,"data-active":t,className:(0,c.cn)(W({variant:a,size:s}),n),...d});return i?("string"==typeof i&&(i={children:i}),(0,r.jsxs)(k,{children:[(0,r.jsx)(C,{asChild:!0,children:b}),(0,r.jsx)(P,{side:"right",align:"center",hidden:"collapsed"!==h||u,...i})]})):b}function Z({className:e,...t}){return(0,r.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,c.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})}function I({className:e,...t}){return(0,r.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,c.cn)("group/menu-sub-item relative",e),...t})}function q({asChild:e=!1,size:t="md",isActive:a=!1,className:s,...i}){let n=e?o.DX:"a";return(0,r.jsx)(n,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":a,className:(0,c.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===t&&"text-xs","md"===t&&"text-sm","group-data-[collapsible=icon]:hidden",s),...i})}var F=a(85814),J=a.n(F),H=a(11096);function Q({className:e,...t}){return(0,r.jsx)(H.bL,{"data-slot":"avatar",className:(0,c.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Y({className:e,...t}){return(0,r.jsx)(H.H4,{"data-slot":"avatar-fallback",className:(0,c.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var G=a(49625),ee=a(10022),et=a(13861),ea=a(96474),er=a(2943),es=a(97840),ei=a(58887),en=a(64398),eo=a(41312),ed=a(79410),el=a(34318),ec=a(14952),eu=a(40083),eh=a(95682);function eb({...e}){return(0,r.jsx)(eh.bL,{"data-slot":"collapsible",...e})}function ef({...e}){return(0,r.jsx)(eh.R6,{"data-slot":"collapsible-trigger",...e})}function ex({...e}){return(0,r.jsx)(eh.Ke,{"data-slot":"collapsible-content",...e})}let em=[{title:"Overview",items:[{title:"Dashboard",url:"/dashboard",icon:G.A}]},{title:"Content Management",items:[{title:"Blog Posts",url:"/dashboard/blog",icon:ee.A,submenu:[{title:"All Posts",url:"/dashboard/blog",icon:et.A},{title:"New Post",url:"/dashboard/blog/new",icon:ea.A}]},{title:"Videos",url:"/dashboard/videos",icon:er.A,submenu:[{title:"All Videos",url:"/dashboard/videos",icon:et.A},{title:"New Video",url:"/dashboard/videos/new",icon:ea.A}]},{title:"Reels",url:"/dashboard/reels",icon:es.A,submenu:[{title:"All Reels",url:"/dashboard/reels",icon:et.A},{title:"New Reel",url:"/dashboard/reels/new",icon:ea.A}]},{title:"Testimonials",url:"/dashboard/testimonials",icon:ei.A,submenu:[{title:"All Testimonials",url:"/dashboard/testimonials",icon:en.A},{title:"New Testimonial",url:"/dashboard/testimonials/new",icon:ea.A}]},{title:"Clients",url:"/dashboard/clients",icon:eo.A,submenu:[{title:"All Clients",url:"/dashboard/clients",icon:ed.A},{title:"New Client",url:"/dashboard/clients/new",icon:ea.A}]}]}];function ep(){let{user:e,logout:t}=(0,s.A)(),a=(0,i.useRouter)(),n=(0,i.usePathname)(),o=async()=>{await t(),a.push("/login")},d=e=>"/dashboard"===e?"/dashboard"===n:n.startsWith(e);return(0,r.jsxs)(L,{variant:"inset",children:[(0,r.jsx)(R,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2",children:[(0,r.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground",children:(0,r.jsx)(el.A,{className:"size-4"})}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:"Portfolio CMS"}),(0,r.jsx)("span",{className:"truncate text-xs text-muted-foreground",children:"Content Management"})]})]})}),(0,r.jsx)(O,{children:em.map(e=>(0,r.jsxs)(U,{children:[(0,r.jsx)(V,{children:e.title}),(0,r.jsx)(B,{children:(0,r.jsx)($,{children:e.items.map(e=>(0,r.jsx)(K,{children:e.submenu?(0,r.jsx)(eb,{asChild:!0,defaultOpen:d(e.url),className:"group/collapsible",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(ef,{asChild:!0,children:(0,r.jsxs)(X,{tooltip:e.title,isActive:d(e.url),children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title}),(0,r.jsx)(ec.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,r.jsx)(ex,{children:(0,r.jsx)(Z,{children:e.submenu.map(e=>(0,r.jsx)(I,{children:(0,r.jsx)(q,{asChild:!0,isActive:n===e.url,children:(0,r.jsxs)(J(),{href:e.url,children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})]})}):(0,r.jsx)(X,{asChild:!0,tooltip:e.title,isActive:d(e.url),children:(0,r.jsxs)(J(),{href:e.url,children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})]},e.title))}),(0,r.jsx)(M,{children:(0,r.jsxs)($,{children:[(0,r.jsx)(K,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsx)(Q,{className:"h-8 w-8 rounded-lg",children:(0,r.jsx)(Y,{className:"rounded-lg",children:e?.name?.charAt(0).toUpperCase()||"U"})}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:e?.name||"User"}),(0,r.jsx)("span",{className:"truncate text-xs text-muted-foreground capitalize",children:e?.role||"user"})]})]})}),(0,r.jsx)(K,{children:(0,r.jsxs)(X,{onClick:o,className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,r.jsx)(eu.A,{}),(0,r.jsx)("span",{children:"Logout"})]})})]})}),(0,r.jsx)(E,{})]})}function eg({children:e}){let{user:t,loading:a}=(0,s.A)();(0,i.useRouter)();let n=(0,i.usePathname)();return a?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):t?(0,r.jsxs)(S,{children:[(0,r.jsx)(ep,{}),(0,r.jsxs)(T,{children:[(0,r.jsx)("header",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,r.jsx)(D,{className:"-ml-1"}),(0,r.jsx)(b,{orientation:"vertical",className:"mr-2 h-4"}),(0,r.jsx)("h1",{className:"text-lg font-semibold",children:(()=>{let e=n.split("/").filter(Boolean);if(1===e.length)return"Dashboard";let t=e[e.length-1];return"new"===t?"New":"blog"===t?"Blog Posts":"videos"===t?"Videos":"reels"===t?"Reels":"testimonials"===t?"Testimonials":"clients"===t?"Clients":t.charAt(0).toUpperCase()+t.slice(1)})()})]})}),(0,r.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:e})]})]}):null}},86769:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},88156:(e,t,a)=>{Promise.resolve().then(a.bind(a,63213))},89667:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var r=a(60687);a(43210);var s=a(4780);function i({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d,metadata:()=>o});var r=a(37413),s=a(25091),i=a.n(s);a(61135);var n=a(29131);let o={title:"Portfolio CMS - Content Management System",description:"Manage your portfolio content with ease"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} font-sans antialiased`,children:(0,r.jsx)(n.AuthProvider,{children:e})})})}}};