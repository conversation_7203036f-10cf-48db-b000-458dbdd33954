(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4076],{239:(e,t,a)=>{"use strict";a.d(t,{bL:()=>y,zi:()=>N});var s=a(2115),r=a(5185),l=a(6101),i=a(6081),n=a(5845),d=a(1275),c=a(3655),o=a(5155),u="Switch",[h,p]=(0,i.A)(u),[x,m]=h(u),v=s.forwardRef((e,t)=>{let{__scopeSwitch:a,name:i,checked:d,defaultChecked:h,required:p,disabled:m,value:v="on",onCheckedChange:f,form:g,...y}=e,[N,k]=s.useState(null),w=(0,l.s)(t,e=>k(e)),C=s.useRef(!1),A=!N||g||!!N.closest("form"),[T,F]=(0,n.i)({prop:d,defaultProp:null!=h&&h,onChange:f,caller:u});return(0,o.jsxs)(x,{scope:a,checked:T,disabled:m,children:[(0,o.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":p,"data-state":b(T),"data-disabled":m?"":void 0,disabled:m,value:v,...y,ref:w,onClick:(0,r.m)(e.onClick,e=>{F(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,o.jsx)(j,{control:N,bubbles:!C.current,name:i,value:v,checked:T,required:p,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var f="SwitchThumb",g=s.forwardRef((e,t)=>{let{__scopeSwitch:a,...s}=e,r=m(f,a);return(0,o.jsx)(c.sG.span,{"data-state":b(r.checked),"data-disabled":r.disabled?"":void 0,...s,ref:t})});g.displayName=f;var j=s.forwardRef((e,t)=>{let{__scopeSwitch:a,control:r,checked:i,bubbles:n=!0,...c}=e,u=s.useRef(null),h=(0,l.s)(u,t),p=function(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(i),x=(0,d.X)(r);return s.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==i&&t){let a=new Event("click",{bubbles:n});t.call(e,i),e.dispatchEvent(a)}},[p,i,n]),(0,o.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...c,tabIndex:-1,ref:h,style:{...c.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var y=v,N=g},333:(e,t,a)=>{"use strict";a.d(t,{d:()=>i});var s=a(5155);a(2115);var r=a(239),l=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},968:(e,t,a)=>{"use strict";a.d(t,{b:()=>n});var s=a(2115),r=a(3655),l=a(5155),i=s.forwardRef((e,t)=>(0,l.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var n=i},1154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2053:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var s=a(5155),r=a(2115),l=a(285),i=a(2523),n=a(5057),d=a(6695),c=a(4416),o=a(1154),u=a(7213);let h=(0,a(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var p=a(8164),x=a(6766);function m(e){let{value:t,onChange:a,label:m="Image",folder:v="portfolio-cms",className:f="",accept:g="image/*",maxSize:j=5}=e,[b,y]=(0,r.useState)(!1),[N,k]=(0,r.useState)(""),[w,C]=(0,r.useState)(""),[A,T]=(0,r.useState)(!1),F=(0,r.useRef)(null),S=async e=>{if(e){if(e.size>1024*j*1024)return void k("File size must be less than ".concat(j,"MB"));if(!e.type.startsWith("image/"))return void k("Please select a valid image file");y(!0),k("");try{let t=new FormData;t.append("file",e),t.append("folder",v);let s=await fetch("/api/upload",{method:"POST",body:t}),r=await s.json();s.ok?a(r.url):k(r.error||"Upload failed")}catch(e){k("Network error occurred")}finally{y(!1)}}},P=()=>{w.trim()&&(a(w.trim()),C(""),T(!1))};return(0,s.jsxs)("div",{className:"space-y-4 ".concat(f),children:[(0,s.jsx)(n.J,{children:m}),t?(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x.default,{src:t,alt:"Uploaded image",width:300,height:200,className:"w-full h-48 object-cover rounded-lg"}),(0,s.jsx)(l.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{a(""),F.current&&(F.current.value="")},children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2 truncate",children:t})]})}):(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&S(t)},onDragOver:e=>{e.preventDefault()},children:b?(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 animate-spin text-blue-500 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop an image here, or click to select"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=F.current)?void 0:e.click()},children:[(0,s.jsx)(h,{className:"mr-2 h-4 w-4"}),"Upload File"]}),(0,s.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>T(!A),children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Add URL"]})]})]})}),A&&(0,s.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,s.jsx)(i.p,{placeholder:"Enter image URL...",value:w,onChange:e=>C(e.target.value),onKeyPress:e=>"Enter"===e.key&&P()}),(0,s.jsx)(l.$,{type:"button",onClick:P,children:"Add"}),(0,s.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>T(!1),children:"Cancel"})]}),N&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-2",children:N}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Supports: JPG, PNG, GIF, WebP (max ",j,"MB)"]})]})}),(0,s.jsx)("input",{ref:F,type:"file",accept:g,onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];a&&S(a)},className:"hidden"})]})}},3858:(e,t,a)=>{Promise.resolve().then(a.bind(a,8958))},4229:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(5155);a(2115);var r=a(968),l=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>n,TN:()=>d});var s=a(5155);a(2115);var r=a(2085),l=a(9434);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(i({variant:a}),t),...r})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},7213:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7550:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8164:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>l});var s=a(5155);a(2115);var r=a(9434);function l(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},8958:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(5155),r=a(2115),l=a(5695),i=a(6913),n=a(2053),d=a(285),c=a(2523),o=a(8539),u=a(5057),h=a(6695),p=a(333),x=a(5365),m=a(7550),v=a(4229),f=a(2657),g=a(6874),j=a.n(g);function b(){let e=(0,l.useRouter)(),t=(0,l.useParams)(),[a,g]=(0,r.useState)(!1),[b,y]=(0,r.useState)(!0),[N,k]=(0,r.useState)(""),[w,C]=(0,r.useState)(""),[A,T]=(0,r.useState)({name:"",logo:"",description:"",website:"",industry:"",projectType:"",featured:!1,status:"draft",order:0});(0,r.useEffect)(()=>{t.id&&F()},[t.id]);let F=async()=>{try{let e=await fetch("/api/clients/".concat(t.id),{credentials:"include"});if(e.ok){let t=(await e.json()).data;T({name:t.name||"",logo:t.logo||"",description:t.description||"",website:t.website||"",industry:t.industry||"",projectType:t.projectType||"",featured:t.featured||!1,status:t.status||"draft",order:t.order||0})}else k("Failed to fetch client data")}catch(e){k("Network error occurred")}finally{y(!1)}},S=(e,t)=>{T(a=>({...a,[e]:t}))},P=async a=>{g(!0),k(""),C("");try{let s=await fetch("/api/clients/".concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...A,status:a})}),r=await s.json();s.ok?(C("Client ".concat("published"===a?"published":"updated"," successfully!")),setTimeout(()=>{e.push("/dashboard/clients")},1500)):k(r.error||"Failed to update client")}catch(e){k("Network error occurred")}finally{g(!1)}};return b?(0,s.jsx)(i.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})})}):(0,s.jsx)(i.A,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(j(),{href:"/dashboard/clients",children:(0,s.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Back to Clients"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Client"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Update client information"})]})]})}),N&&(0,s.jsx)(x.Fc,{variant:"destructive",children:(0,s.jsx)(x.TN,{children:N})}),w&&(0,s.jsx)(x.Fc,{children:(0,s.jsx)(x.TN,{children:w})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,s.jsxs)(h.Zp,{children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{children:"Client Information"})}),(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"name",children:"Client Name *"}),(0,s.jsx)(c.p,{id:"name",value:A.name,onChange:e=>S("name",e.target.value),placeholder:"Enter client name...",required:!0})]}),(0,s.jsx)("div",{children:(0,s.jsx)(n.A,{label:"Client Logo *",value:A.logo,onChange:e=>S("logo",e),folder:"clients"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"description",children:"Description *"}),(0,s.jsx)(o.T,{id:"description",value:A.description,onChange:e=>S("description",e.target.value),placeholder:"Brief description about the client and your work together...",rows:4,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"website",children:"Website"}),(0,s.jsx)(c.p,{id:"website",type:"url",value:A.website,onChange:e=>S("website",e.target.value),placeholder:"https://client-website.com"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"industry",children:"Industry"}),(0,s.jsx)(c.p,{id:"industry",value:A.industry,onChange:e=>S("industry",e.target.value),placeholder:"e.g., Technology, Healthcare, Finance"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"projectType",children:"Project Type"}),(0,s.jsx)(c.p,{id:"projectType",value:A.projectType,onChange:e=>S("projectType",e.target.value),placeholder:"e.g., Web Development, Branding, Marketing"})]})]})]})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(h.Zp,{children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{children:"Publish"})}),(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"status",children:"Status"}),(0,s.jsxs)("select",{id:"status",value:A.status,onChange:e=>S("status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,s.jsx)("option",{value:"draft",children:"Draft"}),(0,s.jsx)("option",{value:"published",children:"Published"}),(0,s.jsx)("option",{value:"archived",children:"Archived"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.d,{id:"featured",checked:A.featured,onCheckedChange:e=>S("featured",e)}),(0,s.jsx)(u.J,{htmlFor:"featured",children:"Featured Client"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(d.$,{onClick:()=>P("draft"),variant:"outline",className:"w-full",disabled:a,children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]}),(0,s.jsxs)(d.$,{onClick:()=>P("published"),className:"w-full",disabled:a||!A.name||!A.logo||!A.description,children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Update & Publish"]})]})]})]}),(0,s.jsxs)(h.Zp,{children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{children:"Settings"})}),(0,s.jsx)(h.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"order",children:"Display Order"}),(0,s.jsx)(c.p,{id:"order",type:"number",value:A.order,onChange:e=>S("order",parseInt(e.target.value)||0),placeholder:"0"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Lower numbers appear first"})]})})]})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,5812,6766,6823,8441,1684,7358],()=>t(3858)),_N_E=e.O()}]);