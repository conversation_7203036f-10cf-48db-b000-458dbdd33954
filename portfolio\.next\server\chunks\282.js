"use strict";exports.id=282,exports.ids=[282],exports.modules={3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},55491:(e,t,n)=>{n.d(t,{UC:()=>t3,In:()=>t5,q7:()=>t8,VF:()=>t4,p4:()=>t7,ZL:()=>t6,bL:()=>t0,wn:()=>nt,PP:()=>ne,l9:()=>t1,WT:()=>t2,LM:()=>t9});var r=n(43210),o=n(51215);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(70569),a=n(11273),s=n(98599),u=n(8730),c=n(60687),f=new WeakMap;function d(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=p(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function p(e){return e!=e||0===e?0:Math.trunc(e)}var h=r.createContext(void 0),m=n(31355),g=n(1359),v=n(32547),w=n(96963);let y=["top","right","bottom","left"],x=Math.min,b=Math.max,S=Math.round,R=Math.floor,C=e=>({x:e,y:e}),A={left:"right",right:"left",bottom:"top",top:"bottom"},T={start:"end",end:"start"};function P(e,t){return"function"==typeof e?e(t):e}function j(e){return e.split("-")[0]}function E(e){return e.split("-")[1]}function L(e){return"x"===e?"y":"x"}function k(e){return"y"===e?"height":"width"}function N(e){return["top","bottom"].includes(j(e))?"y":"x"}function M(e){return e.replace(/start|end/g,e=>T[e])}function D(e){return e.replace(/left|right|bottom|top/g,e=>A[e])}function O(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function I(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function H(e,t,n){let r,{reference:o,floating:i}=e,l=N(t),a=L(N(t)),s=k(a),u=j(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(E(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let B=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=H(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:v,data:w,reset:y}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(d=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:f}=H(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function F(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=P(t,e),h=O(p),m=a[d?"floating"===f?"reference":"floating":f],g=I(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),v="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=I(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:s}):v);return{top:(g.top-x.top+h.top)/y.y,bottom:(x.bottom-g.bottom+h.bottom)/y.y,left:(g.left-x.left+h.left)/y.x,right:(x.right-g.right+h.right)/y.x}}function W(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function V(e){return y.some(t=>e[t]>=0)}async function z(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=j(n),a=E(n),s="y"===N(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,f=P(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:d*u}:{x:d*u,y:p*c}}function _(){return"undefined"!=typeof window}function G(e){return q(e)?(e.nodeName||"").toLowerCase():"#document"}function $(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function K(e){var t;return null==(t=(q(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function q(e){return!!_()&&(e instanceof Node||e instanceof $(e).Node)}function U(e){return!!_()&&(e instanceof Element||e instanceof $(e).Element)}function Y(e){return!!_()&&(e instanceof HTMLElement||e instanceof $(e).HTMLElement)}function X(e){return!!_()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof $(e).ShadowRoot)}function Z(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=en(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function J(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Q(e){let t=ee(),n=U(e)?en(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ee(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function et(e){return["html","body","#document"].includes(G(e))}function en(e){return $(e).getComputedStyle(e)}function er(e){return U(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eo(e){if("html"===G(e))return e;let t=e.assignedSlot||e.parentNode||X(e)&&e.host||K(e);return X(t)?t.host:t}function ei(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eo(t);return et(n)?t.ownerDocument?t.ownerDocument.body:t.body:Y(n)&&Z(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=$(o);if(i){let e=el(l);return t.concat(l,l.visualViewport||[],Z(o)?o:[],e&&n?ei(e):[])}return t.concat(o,ei(o,[],n))}function el(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ea(e){let t=en(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=Y(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=S(n)!==i||S(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function es(e){return U(e)?e:e.contextElement}function eu(e){let t=es(e);if(!Y(t))return C(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ea(t),l=(i?S(n.width):n.width)/r,a=(i?S(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ec=C(0);function ef(e){let t=$(e);return ee()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ec}function ed(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=es(e),a=C(1);t&&(r?U(r)&&(a=eu(r)):a=eu(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===$(l))&&o)?ef(l):C(0),u=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=$(l),t=r&&U(r)?$(r):r,n=e,o=el(n);for(;o&&r&&t!==n;){let e=eu(o),t=o.getBoundingClientRect(),r=en(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=i,c+=l,o=el(n=$(o))}}return I({width:f,height:d,x:u,y:c})}function ep(e,t){let n=er(e).scrollLeft;return t?t.left+n:ed(K(e)).left+n}function eh(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ep(e,r)),y:r.top+t.scrollTop}}function em(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=$(e),r=K(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=ee();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=K(e),n=er(e),r=e.ownerDocument.body,o=b(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=b(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ep(e),a=-n.scrollTop;return"rtl"===en(r).direction&&(l+=b(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(K(e));else if(U(t))r=function(e,t){let n=ed(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Y(e)?eu(e):C(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=ef(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return I(r)}function eg(e){return"static"===en(e).position}function ev(e,t){if(!Y(e)||"fixed"===en(e).position)return null;if(t)return t(e);let n=e.offsetParent;return K(e)===n&&(n=n.ownerDocument.body),n}function ew(e,t){let n=$(e);if(J(e))return n;if(!Y(e)){let t=eo(e);for(;t&&!et(t);){if(U(t)&&!eg(t))return t;t=eo(t)}return n}let r=ev(e,t);for(;r&&["table","td","th"].includes(G(r))&&eg(r);)r=ev(r,t);return r&&et(r)&&eg(r)&&!Q(r)?n:r||function(e){let t=eo(e);for(;Y(t)&&!et(t);){if(Q(t))return t;if(J(t))break;t=eo(t)}return null}(e)||n}let ey=async function(e){let t=this.getOffsetParent||ew,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=Y(t),o=K(t),i="fixed"===n,l=ed(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=C(0);if(r||!r&&!i)if(("body"!==G(t)||Z(o))&&(a=er(t)),r){let e=ed(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ep(o));i&&!r&&o&&(s.x=ep(o));let u=!o||r||i?C(0):eh(o,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ex={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=K(r),a=!!t&&J(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},u=C(1),c=C(0),f=Y(r);if((f||!f&&!i)&&(("body"!==G(r)||Z(l))&&(s=er(r)),Y(r))){let e=ed(r);u=eu(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!l||f||i?C(0):eh(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:K,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?J(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ei(e,[],!1).filter(e=>U(e)&&"body"!==G(e)),o=null,i="fixed"===en(e).position,l=i?eo(e):e;for(;U(l)&&!et(l);){let t=en(l),n=Q(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||Z(l)&&!n&&function e(t,n){let r=eo(t);return!(r===n||!U(r)||et(r))&&("fixed"===en(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eo(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=em(t,n,o);return e.top=b(r.top,e.top),e.right=x(r.right,e.right),e.bottom=x(r.bottom,e.bottom),e.left=b(r.left,e.left),e},em(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ew,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ea(e);return{width:t,height:n}},getScale:eu,isElement:U,isRTL:function(e){return"rtl"===en(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eS=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=P(e,t)||{};if(null==u)return{};let f=O(c),d={x:n,y:r},p=L(N(o)),h=k(p),m=await l.getDimensions(u),g="y"===p,v=g?"clientHeight":"clientWidth",w=i.reference[h]+i.reference[p]-d[p]-i.floating[h],y=d[p]-i.reference[p],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),R=S?S[v]:0;R&&await (null==l.isElement?void 0:l.isElement(S))||(R=a.floating[v]||i.floating[h]);let C=R/2-m[h]/2-1,A=x(f[g?"top":"left"],C),T=x(f[g?"bottom":"right"],C),j=R-m[h]-T,M=R/2-m[h]/2+(w/2-y/2),D=b(A,x(M,j)),I=!s.arrow&&null!=E(o)&&M!==D&&i.reference[h]/2-(M<A?A:T)-m[h]/2<0,H=I?M<A?M-A:M-j:0;return{[p]:d[p]+H,data:{[p]:D,centerOffset:M-D-H,...I&&{alignmentOffset:H}},reset:I}}}),eR=(e,t,n)=>{let r=new Map,o={platform:ex,...n},i={...o.platform,_c:r};return B(e,t,{...o,platform:i})};var eC="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function eA(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eA(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eA(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eT(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eP(e,t){let n=eT(e);return Math.round(t*n)/n}function ej(e){let t=r.useRef(e);return eC(()=>{t.current=e}),t}let eE=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eS({element:n.current,padding:r}).fn(t):{}:n?eS({element:n,padding:r}).fn(t):{}}}),eL=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await z(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=P(e,t),u={x:n,y:r},c=await F(t,s),f=N(j(o)),d=L(f),p=u[d],h=u[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=b(n,x(p,r))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+c[e],r=h-c[t];h=b(n,x(h,r))}let m=a.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:l}}}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=P(e,t),c={x:n,y:r},f=N(o),d=L(f),p=c[d],h=c[f],m=P(a,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,n=i.reference[d]+i.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var v,w;let e="y"===d?"width":"height",t=["top","left"].includes(j(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(v=l.offset)?void 0:v[f])||0)+(t?0:g.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(w=l.offset)?void 0:w[f])||0)-(t?g.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}),eM=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l,a;let{placement:s,middlewareData:u,rects:c,initialPlacement:f,platform:d,elements:p}=t,{mainAxis:h=!0,crossAxis:m=!0,fallbackPlacements:g,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:y=!0,...x}=P(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=j(s),S=N(f),R=j(f)===f,C=await (null==d.isRTL?void 0:d.isRTL(p.floating)),A=g||(R||!y?[D(f)]:function(e){let t=D(e);return[M(e),t,M(t)]}(f)),T="none"!==w;!g&&T&&A.push(...function(e,t,n,r){let o=E(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(j(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(M)))),i}(f,y,w,C));let O=[f,...A],I=await F(t,x),H=[],B=(null==(r=u.flip)?void 0:r.overflows)||[];if(h&&H.push(I[b]),m){let e=function(e,t,n){void 0===n&&(n=!1);let r=E(e),o=L(N(e)),i=k(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=D(l)),[l,D(l)]}(s,c,C);H.push(I[e[0]],I[e[1]])}if(B=[...B,{placement:s,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=O[e];if(t){let n="alignment"===m&&S!==N(t),r=(null==(l=B[0])?void 0:l.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:B},reset:{placement:t}}}let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=B.filter(e=>{if(T){let t=N(e.placement);return t===S||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=f}if(s!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...f}=P(e,t),d=await F(t,f),p=j(l),h=E(l),m="y"===N(l),{width:g,height:v}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let w=v-d.top-d.bottom,y=g-d.left-d.right,S=x(v-d[o],w),R=x(g-d[i],y),C=!t.middlewareData.shift,A=S,T=R;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(T=y),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(A=w),C&&!h){let e=b(d.left,0),t=b(d.right,0),n=b(d.top,0),r=b(d.bottom,0);m?T=g-2*(0!==e||0!==t?e+t:b(d.left,d.right)):A=v-2*(0!==n||0!==r?n+r:b(d.top,d.bottom))}await c({...t,availableWidth:T,availableHeight:A});let L=await s.getDimensions(u.floating);return g!==L.width||v!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=P(e,t);switch(r){case"referenceHidden":{let e=W(await F(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:V(e)}}}case"escaped":{let e=W(await F(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:V(e)}}}default:return{}}}}}(e),options:[e,t]}),eI=(e,t)=>({...eE(e),options:[e,t]});var eH=n(14163),eB=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,c.jsx)(eH.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eB.displayName="Arrow";var eF=n(13495),eW=n(66156),eV="Popper",[ez,e_]=(0,a.A)(eV),[eG,e$]=ez(eV),eK=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,c.jsx)(eG,{scope:t,anchor:o,onAnchorChange:i,children:n})};eK.displayName=eV;var eq="PopperAnchor",eU=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=e$(eq,n),a=r.useRef(null),u=(0,s.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,c.jsx)(eH.sG.div,{...i,ref:u})});eU.displayName=eq;var eY="PopperContent",[eX,eZ]=ez(eY),eJ=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:u=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:w,...y}=e,S=e$(eY,n),[C,A]=r.useState(null),T=(0,s.s)(t,e=>A(e)),[P,j]=r.useState(null),E=function(e){let[t,n]=r.useState(void 0);return(0,eW.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(P),L=E?.width??0,k=E?.height??0,N="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],D=M.length>0,O={padding:N,boundary:M.filter(e2),altBoundary:D},{refs:I,floatingStyles:H,placement:B,isPositioned:F,middlewareData:W}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:f}=e,[d,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(i);eA(h,i)||m(i);let[g,v]=r.useState(null),[w,y]=r.useState(null),x=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),b=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),S=a||g,R=s||w,C=r.useRef(null),A=r.useRef(null),T=r.useRef(d),P=null!=c,j=ej(c),E=ej(l),L=ej(f),k=r.useCallback(()=>{if(!C.current||!A.current)return;let e={placement:t,strategy:n,middleware:h};E.current&&(e.platform=E.current),eR(C.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};N.current&&!eA(T.current,t)&&(T.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,E,L]);eC(()=>{!1===f&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[f]);let N=r.useRef(!1);eC(()=>(N.current=!0,()=>{N.current=!1}),[]),eC(()=>{if(S&&(C.current=S),R&&(A.current=R),S&&R){if(j.current)return j.current(S,R,k);k()}},[S,R,k,j,P]);let M=r.useMemo(()=>({reference:C,floating:A,setReference:x,setFloating:b}),[x,b]),D=r.useMemo(()=>({reference:S,floating:R}),[S,R]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eP(D.floating,d.x),r=eP(D.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eT(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:k,refs:M,elements:D,floatingStyles:O}),[d,k,M,D,O])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=es(e),f=i||l?[...c?ei(c):[],...ei(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let d=c&&s?function(e,t){let n,r=null,o=K(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=u;if(a||t(),!d||!p)return;let h=R(f),m=R(o.clientWidth-(c+d)),g={rootMargin:-h+"px "+-m+"px "+-R(o.clientHeight-(f+p))+"px "+-R(c)+"px",threshold:b(0,x(1,s))||1},v=!0;function w(t){let r=t[0].intersectionRatio;if(r!==s){if(!v)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eb(u,e.getBoundingClientRect())||l(),v=!1}try{r=new IntersectionObserver(w,{...g,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(w,g)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?ed(e):null;return u&&function t(){let r=ed(e);m&&!eb(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:S.anchor},middleware:[eL({mainAxis:l+k,alignmentAxis:u}),d&&ek({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eN():void 0,...O}),d&&eM({...O}),eD({...O,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),P&&eI({element:P,padding:f}),e5({arrowWidth:L,arrowHeight:k}),g&&eO({strategy:"referenceHidden",...O})]}),[V,z]=e6(B),_=(0,eF.c)(w);(0,eW.N)(()=>{F&&_?.()},[F,_]);let G=W.arrow?.x,$=W.arrow?.y,q=W.arrow?.centerOffset!==0,[U,Y]=r.useState();return(0,eW.N)(()=>{C&&Y(window.getComputedStyle(C).zIndex)},[C]),(0,c.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:F?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[W.transformOrigin?.x,W.transformOrigin?.y].join(" "),...W.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(eX,{scope:n,placedSide:V,onArrowChange:j,arrowX:G,arrowY:$,shouldHideArrow:q,children:(0,c.jsx)(eH.sG.div,{"data-side":V,"data-align":z,...y,ref:T,style:{...y.style,animation:F?void 0:"none"}})})})});eJ.displayName=eY;var eQ="PopperArrow",e0={top:"bottom",right:"left",bottom:"top",left:"right"},e1=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eZ(eQ,n),i=e0[o.placedSide];return(0,c.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(eB,{...r,ref:t,style:{...r.style,display:"block"}})})});function e2(e){return null!==e}e1.displayName=eQ;var e5=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,u]=e6(n),c={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=i?c:`${f}px`,h=`${-a}px`):"top"===s?(p=i?c:`${f}px`,h=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=i?c:`${d}px`):"left"===s&&(p=`${r.floating.width+a}px`,h=i?c:`${d}px`),{data:{x:p,y:h}}}});function e6(e){let[t,n="center"]=e.split("-");return[t,n]}var e3=n(25028),e9=n(65551),e8=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,c.jsx)(eH.sG.span,{...e,ref:t,style:{...e8,...e.style}})).displayName="VisuallyHidden";var e7=n(63376),e4=n(42247),te=[" ","Enter","ArrowUp","ArrowDown"],tt=[" ","Enter"],tn="Select",[tr,to,ti]=function(e){let t=e+"CollectionProvider",[n,o]=(0,a.A)(t),[i,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),l=r.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:l,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",p=(0,u.TL)(d),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(d,n),i=(0,s.s)(t,o.collectionRef);return(0,c.jsx)(p,{ref:i,children:r})});h.displayName=d;let m=e+"CollectionItemSlot",g="data-radix-collection-item",v=(0,u.TL)(m),w=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,a=r.useRef(null),u=(0,s.s)(t,a),f=l(m,n);return r.useEffect(()=>(f.itemMap.set(a,{ref:a,...i}),()=>void f.itemMap.delete(a))),(0,c.jsx)(v,{...{[g]:""},ref:u,children:o})});return w.displayName=m,[{Provider:f,Slot:h,ItemSlot:w},function(t){let n=l(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}(tn),[tl,ta]=(0,a.A)(tn,[ti,e_]),ts=e_(),[tu,tc]=tl(tn),[tf,td]=tl(tn),tp=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:m,required:g,form:v}=e,y=ts(t),[x,b]=r.useState(null),[S,R]=r.useState(null),[C,A]=r.useState(!1),T=function(e){let t=r.useContext(h);return e||t||"ltr"}(f),[P,j]=(0,e9.i)({prop:o,defaultProp:i??!1,onChange:l,caller:tn}),[E,L]=(0,e9.i)({prop:a,defaultProp:s,onChange:u,caller:tn}),k=r.useRef(null),N=!x||v||!!x.closest("form"),[M,D]=r.useState(new Set),O=Array.from(M).map(e=>e.props.value).join(";");return(0,c.jsx)(eK,{...y,children:(0,c.jsxs)(tu,{required:g,scope:t,trigger:x,onTriggerChange:b,valueNode:S,onValueNodeChange:R,valueNodeHasChildren:C,onValueNodeHasChildrenChange:A,contentId:(0,w.B)(),value:E,onValueChange:L,open:P,onOpenChange:j,dir:T,triggerPointerDownPosRef:k,disabled:m,children:[(0,c.jsx)(tr.Provider,{scope:t,children:(0,c.jsx)(tf,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,c.jsxs)(tX,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:p,value:E,onChange:e=>L(e.target.value),disabled:m,form:v,children:[void 0===E?(0,c.jsx)("option",{value:""}):null,Array.from(M)]},O):null]})})};tp.displayName=tn;var th="SelectTrigger",tm=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=ts(n),u=tc(th,n),f=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),p=to(n),h=r.useRef("touch"),[m,g,v]=tJ(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=tQ(t,e,n);void 0!==r&&u.onValueChange(r.value)}),w=e=>{f||(u.onOpenChange(!0),v()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,c.jsx)(eU,{asChild:!0,...a,children:(0,c.jsx)(eH.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:f,"data-disabled":f?"":void 0,"data-placeholder":tZ(u.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&w(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&te.includes(e.key)&&(w(),e.preventDefault())})})})});tm.displayName=th;var tg="SelectValue",tv=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=tc(tg,n),{onValueNodeHasChildrenChange:f}=u,d=void 0!==i,p=(0,s.s)(t,u.onValueNodeChange);return(0,eW.N)(()=>{f(d)},[f,d]),(0,c.jsx)(eH.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:tZ(u.value)?(0,c.jsx)(c.Fragment,{children:l}):i})});tv.displayName=tg;var tw=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,c.jsx)(eH.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tw.displayName="SelectIcon";var ty=e=>(0,c.jsx)(e3.Z,{asChild:!0,...e});ty.displayName="SelectPortal";var tx="SelectContent",tb=r.forwardRef((e,t)=>{let n=tc(tx,e.__scopeSelect),[i,l]=r.useState();return((0,eW.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,c.jsx)(tA,{...e,ref:t}):i?o.createPortal((0,c.jsx)(tS,{scope:e.__scopeSelect,children:(0,c.jsx)(tr.Slot,{scope:e.__scopeSelect,children:(0,c.jsx)("div",{children:e.children})})}),i):null});tb.displayName=tx;var[tS,tR]=tl(tx),tC=(0,u.TL)("SelectContent.RemoveScroll"),tA=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:f,sideOffset:d,align:p,alignOffset:h,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:R,...C}=e,A=tc(tx,n),[T,P]=r.useState(null),[j,E]=r.useState(null),L=(0,s.s)(t,e=>P(e)),[k,N]=r.useState(null),[M,D]=r.useState(null),O=to(n),[I,H]=r.useState(!1),B=r.useRef(!1);r.useEffect(()=>{if(T)return(0,e7.Eq)(T)},[T]),(0,g.Oh)();let F=r.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&j&&(j.scrollTop=0),n===r&&j&&(j.scrollTop=j.scrollHeight),n?.focus(),document.activeElement!==o))return},[O,j]),W=r.useCallback(()=>F([k,T]),[F,k,T]);r.useEffect(()=>{I&&W()},[I,W]);let{onOpenChange:V,triggerPointerDownPosRef:z}=A;r.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,V,z]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[_,G]=tJ(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=tQ(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),$=r.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==A.value&&A.value===t||r)&&(N(e),r&&(B.current=!0))},[A.value]),K=r.useCallback(()=>T?.focus(),[T]),q=r.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==A.value&&A.value===t||r)&&D(e)},[A.value]),U="popper"===o?tP:tT,Y=U===tP?{side:f,sideOffset:d,align:p,alignOffset:h,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:R}:{};return(0,c.jsx)(tS,{scope:n,content:T,viewport:j,onViewportChange:E,itemRefCallback:$,selectedItem:k,onItemLeave:K,itemTextRefCallback:q,focusSelectedItem:W,selectedItemText:M,position:o,isPositioned:I,searchRef:_,children:(0,c.jsx)(e4.A,{as:tC,allowPinchZoom:!0,children:(0,c.jsx)(v.n,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{A.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,c.jsx)(m.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,c.jsx)(U,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...C,...Y,onPlaced:()=>H(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,l.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tA.displayName="SelectContentImpl";var tT=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=tc(tx,n),u=tR(tx,n),[f,d]=r.useState(null),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e)),g=to(n),v=r.useRef(!1),w=r.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:b,focusSelectedItem:S}=u,R=r.useCallback(()=>{if(a.trigger&&a.valueNode&&f&&p&&y&&x&&b){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),c=i(l,[10,Math.max(10,window.innerWidth-10-u)]);f.style.minWidth=s+"px",f.style.left=c+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),c=i(l,[10,Math.max(10,window.innerWidth-10-u)]);f.style.minWidth=s+"px",f.style.right=c+"px"}let l=g(),s=window.innerHeight-20,u=y.scrollHeight,c=window.getComputedStyle(p),d=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),w=d+h+u+parseInt(c.paddingBottom,10)+m,S=Math.min(5*x.offsetHeight,w),R=window.getComputedStyle(y),C=parseInt(R.paddingTop,10),A=parseInt(R.paddingBottom,10),T=e.top+e.height/2-10,P=x.offsetHeight/2,j=d+h+(x.offsetTop+P);if(j<=T){let e=l.length>0&&x===l[l.length-1].ref.current;f.style.bottom="0px";let t=Math.max(s-T,P+(e?A:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+m);f.style.height=j+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;f.style.top="0px";let t=Math.max(T,d+y.offsetTop+(e?C:0)+P);f.style.height=t+(w-j)+"px",y.scrollTop=j-T+y.offsetTop}f.style.margin="10px 0",f.style.minHeight=S+"px",f.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[g,a.trigger,a.valueNode,f,p,y,x,b,a.dir,o]);(0,eW.N)(()=>R(),[R]);let[C,A]=r.useState();(0,eW.N)(()=>{p&&A(window.getComputedStyle(p).zIndex)},[p]);let T=r.useCallback(e=>{e&&!0===w.current&&(R(),S?.(),w.current=!1)},[R,S]);return(0,c.jsx)(tj,{scope:n,contentWrapper:f,shouldExpandOnScrollRef:v,onScrollButtonChange:T,children:(0,c.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,c.jsx)(eH.sG.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tT.displayName="SelectItemAlignedPosition";var tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=ts(n);return(0,c.jsx)(eJ,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tP.displayName="SelectPopperPosition";var[tj,tE]=tl(tx,{}),tL="SelectViewport",tk=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tR(tL,n),u=tE(tL,n),f=(0,s.s)(t,a.onViewportChange),d=r.useRef(0);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,c.jsx)(tr.Slot,{scope:n,children:(0,c.jsx)(eH.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:f,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tk.displayName=tL;var tN="SelectGroup",[tM,tD]=tl(tN);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,w.B)();return(0,c.jsx)(tM,{scope:n,id:o,children:(0,c.jsx)(eH.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=tN;var tO="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tD(tO,n);return(0,c.jsx)(eH.sG.div,{id:o.id,...r,ref:t})}).displayName=tO;var tI="SelectItem",[tH,tB]=tl(tI),tF=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...u}=e,f=tc(tI,n),d=tR(tI,n),p=f.value===o,[h,m]=r.useState(a??""),[g,v]=r.useState(!1),y=(0,s.s)(t,e=>d.itemRefCallback?.(e,o,i)),x=(0,w.B)(),b=r.useRef("touch"),S=()=>{i||(f.onValueChange(o),f.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,c.jsx)(tH,{scope:n,value:o,disabled:i,textId:x,isSelected:p,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,c.jsx)(tr.ItemSlot,{scope:n,value:o,disabled:i,textValue:h,children:(0,c.jsx)(eH.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":p&&g,"data-state":p?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:y,onFocus:(0,l.m)(u.onFocus,()=>v(!0)),onBlur:(0,l.m)(u.onBlur,()=>v(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{b.current=e.pointerType,i?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(tt.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});tF.displayName=tI;var tW="SelectItemText",tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,u=tc(tW,n),f=tR(tW,n),d=tB(tW,n),p=td(tW,n),[h,m]=r.useState(null),g=(0,s.s)(t,e=>m(e),d.onItemTextChange,e=>f.itemTextRefCallback?.(e,d.value,d.disabled)),v=h?.textContent,w=r.useMemo(()=>(0,c.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,eW.N)(()=>(y(w),()=>x(w)),[y,x,w]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eH.sG.span,{id:d.textId,...a,ref:g}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});tV.displayName=tW;var tz="SelectItemIndicator",t_=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tB(tz,n).isSelected?(0,c.jsx)(eH.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t_.displayName=tz;var tG="SelectScrollUpButton",t$=r.forwardRef((e,t)=>{let n=tR(tG,e.__scopeSelect),o=tE(tG,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eW.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,c.jsx)(tU,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t$.displayName=tG;var tK="SelectScrollDownButton",tq=r.forwardRef((e,t)=>{let n=tR(tK,e.__scopeSelect),o=tE(tK,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eW.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,c.jsx)(tU,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tq.displayName=tK;var tU=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tR("SelectScrollButton",n),s=r.useRef(null),u=to(n),f=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>f(),[f]),(0,eW.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,c.jsx)(eH.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{f()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,c.jsx)(eH.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var tY="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ts(n),i=tc(tY,n),l=tR(tY,n);return i.open&&"popper"===l.position?(0,c.jsx)(e1,{...o,...r,ref:t}):null}).displayName=tY;var tX=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),l=(0,s.s)(o,i),a=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,c.jsx)(eH.sG.select,{...n,style:{...e8,...n.style},ref:l,defaultValue:t})});function tZ(e){return""===e||void 0===e}function tJ(e){let t=(0,eF.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function tQ(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}tX.displayName="SelectBubbleInput";var t0=tp,t1=tm,t2=tv,t5=tw,t6=ty,t3=tb,t9=tk,t8=tF,t7=tV,t4=t_,ne=t$,nt=tq}};