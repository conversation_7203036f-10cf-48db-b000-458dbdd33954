import Link from 'next/link';
import { ArrowLeft, FileX } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-4">
        <div className="mb-8">
          <FileX size={80} className="mx-auto text-primary/50 mb-4" />
          <h1 className="text-4xl font-heading font-bold text-primary mb-2">
            Post Not Found
          </h1>
          <p className="text-lg text-muted-foreground">
            The blog post you&apos;re looking for doesn&apos;t exist or has been moved.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link href="/blog">
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
              <ArrowLeft size={16} className="mr-2" />
              Back to Blog
            </Button>
          </Link>
          
          <div>
            <Link 
              href="/#blog" 
              className="text-secondary hover:text-primary transition-colors"
            >
              Or browse featured posts
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
