export interface Testimonial {
  id: number;
  name: string;
  role: string;
  company?: string;
  content: string;
  avatar?: string;
  rating?: number;
}

export const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    role: 'Vlogger/YouTuber',
    content: "<PERSON><PERSON><PERSON>'s editing skills brought our project to life. His attention to detail and creative flair exceeded our expectations. Truly captured the essence we were aiming for.",
    avatar: '/images/placeholder.svg',
    rating: 5
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON>',
    role: 'Professional Designer',
    content: "Working with <PERSON><PERSON><PERSON> was a pleasure. He understood our vision from the start and delivered a final product that was both professional and highly engaging. Great communication!",
    avatar: '/images/placeholder.svg',
    rating: 5
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Marketing Director',
    company: 'Creative Solutions Inc.',
    content: "<PERSON><PERSON><PERSON>'s creativity and technical skills are truly impressive. He transformed raw footage into a compelling narrative that resonated perfectly with our target audience.",
    avatar: '/images/placeholder.svg',
    rating: 5
  },
  {
    id: 4,
    name: '<PERSON>',
    role: 'Content Creator',
    content: "The quality of work and turnaround time was exceptional. <PERSON><PERSON><PERSON> has a great eye for storytelling and knows how to make content that engages viewers from start to finish.",
    avatar: '/images/placeholder.svg',
    rating: 5
  },
  {
    id: 5,
    name: '<PERSON>',
    role: 'Brand Manager',
    company: 'Digital Dynamics',
    content: "Professional, creative, and reliable. Uttam delivered exactly what we needed for our brand campaign. The editing style perfectly matched our brand identity.",
    avatar: '/images/placeholder.svg',
    rating: 5
  }
];
