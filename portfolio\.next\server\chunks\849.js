exports.id=849,exports.ids=[849],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(49384),o=r(82348);function n(...e){return(0,o.QP)((0,s.$)(e))}},13570:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,92830)),Promise.resolve().then(r.bind(r,94743))},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(60687);r(43210);var o=r(8730),n=r(24224),i=r(4780);let a=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",success:"bg-success text-success-foreground shadow-xs hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40",warning:"bg-warning text-warning-foreground shadow-xs hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40",info:"bg-info text-info-foreground shadow-xs hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let d=n?o.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(a({variant:t,size:r,className:e})),...l})}},35112:(e,t,r)=>{Promise.resolve().then(r.bind(r,87112)),Promise.resolve().then(r.bind(r,94555))},41465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var s=r(60687);r(43210);var o=r(10218);function n({children:e}){return(0,s.jsx)(o.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,children:e})}},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(37413),o=r(4536),n=r.n(o),i=r(96083),a=r(18898),l=r(78768),d=r(51465),c=r(68517),m=r(68926),u=r(84712);function h(){return(0,s.jsxs)("main",{children:[(0,s.jsx)(m.default,{}),(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-muted via-background to-muted flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,s.jsxs)("div",{className:"relative mb-8",children:[(0,s.jsx)("div",{className:"text-8xl md:text-9xl font-bold text-primary/20 select-none",children:"404"}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"bg-card p-6 rounded-full shadow-lg animate-bounce",children:(0,s.jsx)(i.A,{size:48,className:"text-primary"})})})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-heading font-bold text-primary mb-4",children:"Oops! Page Not Found"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed mb-6",children:"The page you're looking for seems to have wandered off into the digital void. Don't worry, even the best video editors sometimes lose track of their clips!"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Let's get you back on track with some helpful options below."})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mb-12",children:[(0,s.jsx)(c.$,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/",className:"inline-flex items-center gap-2",children:[(0,s.jsx)(a.A,{size:20}),"Back to Home"]})}),(0,s.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,s.jsxs)(n(),{href:"/blog",className:"inline-flex items-center gap-2",children:[(0,s.jsx)(l.A,{size:20}),"Browse Blog"]})}),(0,s.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,s.jsxs)(n(),{href:"/videos",className:"inline-flex items-center gap-2",children:[(0,s.jsx)(d.A,{size:20}),"View Portfolio"]})})]}),(0,s.jsxs)("div",{className:"bg-card rounded-xl shadow-md p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-primary mb-4",children:"Popular Pages"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)(n(),{href:"/videos",className:"p-3 rounded-lg hover:bg-muted transition-colors text-center group",children:[(0,s.jsx)("div",{className:"text-primary group-hover:scale-110 transition-transform duration-300 mb-2",children:"\uD83C\uDFAC"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Videos"})]}),(0,s.jsxs)(n(),{href:"/reels",className:"p-3 rounded-lg hover:bg-muted transition-colors text-center group",children:[(0,s.jsx)("div",{className:"text-primary group-hover:scale-110 transition-transform duration-300 mb-2",children:"\uD83D\uDCF1"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Shorts"})]}),(0,s.jsxs)(n(),{href:"/blog",className:"p-3 rounded-lg hover:bg-muted transition-colors text-center group",children:[(0,s.jsx)("div",{className:"text-primary group-hover:scale-110 transition-transform duration-300 mb-2",children:"\uD83D\uDCDD"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Blog"})]}),(0,s.jsxs)(n(),{href:"/clients",className:"p-3 rounded-lg hover:bg-muted transition-colors text-center group",children:[(0,s.jsx)("div",{className:"text-primary group-hover:scale-110 transition-transform duration-300 mb-2",children:"\uD83E\uDD1D"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Clients"})]})]})]}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("p",{children:["\uD83D\uDCA1 ",(0,s.jsx)("strong",{children:"Pro Tip:"})," Just like in video editing, sometimes the best content is found when you explore different angles. Try the links above!"]})})]})}),(0,s.jsx)(u.default,{})]})}},55019:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},60595:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},61135:()=>{},68517:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(37413);r(61120);var o=r(70403),n=r(50662),i=r(75986),a=r(8974);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",success:"bg-success text-success-foreground shadow-xs hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40",warning:"bg-warning text-warning-foreground shadow-xs hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40",info:"bg-info text-info-foreground shadow-xs hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:n=!1,...d}){let c=n?o.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:function(...e){return(0,a.QP)((0,i.$)(e))}(l({variant:t,size:r,className:e})),...d})}},68926:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Header.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79290:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926))},84712:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Footer.tsx","default")},84718:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(60687),o=r(43210),n=r(23877),i=r(29523);function a(){let[e,t]=(0,o.useState)(!1),[r,a]=(0,o.useState)(!1),[l,d]=(0,o.useState)(!1),c=encodeURIComponent("Hi! I found your portfolio and would like to discuss a video editing project.");return e?(0,s.jsxs)("div",{className:"fixed bottom-24 right-8 z-50",children:[(0,s.jsxs)("div",{className:`
        absolute right-16 top-1/2 -translate-y-1/2 bg-success text-success-foreground px-4 py-3 rounded-lg text-sm whitespace-nowrap
        transition-all duration-500 pointer-events-none shadow-lg
        ${l&&!r?"opacity-100 translate-x-0 scale-100":"opacity-0 translate-x-4 scale-95"}
      `,children:["\uD83D\uDC4B Need help with video editing?",(0,s.jsx)("div",{className:"absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-success rotate-45"})]}),(0,s.jsxs)(i.$,{variant:"ghost",onClick:()=>{let e=`https://wa.me/${"+977XXXXXXXXXX".replace(/[^0-9]/g,"")}?text=${c}`;window.open(e,"_blank","noopener,noreferrer")},onMouseEnter:()=>{a(!0),d(!1)},onMouseLeave:()=>a(!1),className:`group transition-all duration-300 p-0 h-auto w-auto hover:bg-transparent ${e?"translate-y-0 opacity-100":"translate-y-16 opacity-0"}`,"aria-label":"Contact via WhatsApp",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-success opacity-30 animate-ping"}),(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-success opacity-20 animate-ping animation-delay-1000"}),(0,s.jsx)("div",{className:`
            relative bg-success hover:bg-success/90 text-success-foreground rounded-full p-4 shadow-lg hover:shadow-xl
            transition-all duration-300 transform hover:scale-110 group-hover:rotate-12
            ${r?"shadow-success/50":""}
          `,children:(0,s.jsx)(n.EcP,{size:30,className:"transition-transform duration-300"})}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full animate-ping"}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full"})]}),(0,s.jsxs)("div",{className:`
          absolute right-16 top-1/2 -translate-y-1/2 bg-popover text-popover-foreground px-3 py-2 rounded-lg text-sm whitespace-nowrap
          transition-all duration-300 pointer-events-none shadow-lg border border-border
          ${r?"opacity-100 translate-x-0":"opacity-0 translate-x-2"}
        `,children:["Chat with us on WhatsApp",(0,s.jsx)("div",{className:"absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-popover border-r border-b border-border rotate-45"})]})]})]}):null}},87112:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\components\\\\common\\\\WhatsAppButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\WhatsAppButton.tsx","default")},92830:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var s=r(60687),o=r(85814),n=r.n(o),i=r(98876),a=r(66232),l=r(84113),d=r(19526),c=r(41550),m=r(48340),u=r(97992),h=r(2975),f=r(67760),x=r(29523);function g(){let e=e=>{let t=e.substring(1),r=document.getElementById(t);if(r){let e=r.getBoundingClientRect().top+window.pageYOffset-80;window.scrollTo({top:e,behavior:"smooth"})}},t=[{icon:i.A,href:"https://www.linkedin.com/in/uttamrimal",label:"LinkedIn"},{icon:a.A,href:"https://www.instagram.com/uttamrimal",label:"Instagram"},{icon:l.A,href:"https://www.youtube.com/c/UttamRimal",label:"YouTube"},{icon:d.A,href:"https://www.facebook.com/uttamrimal",label:"Facebook"}],r=[{icon:c.A,text:"<EMAIL>",href:"mailto:<EMAIL>"},{icon:m.A,text:"+977 ************",href:"tel:+9779840692118"},{icon:u.A,text:"Kathmandu, Nepal",href:"#"}];return(0,s.jsxs)("footer",{className:"bg-muted text-foreground relative",children:[(0,s.jsx)(x.$,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"absolute -top-6 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300","aria-label":"Scroll to top",children:(0,s.jsx)(h.A,{size:20})}),(0,s.jsxs)("div",{className:"container mx-auto px-4 pt-16 pb-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsx)(n(),{href:"#home",className:"text-3xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 inline-block mb-4",onClick:t=>{t.preventDefault(),e("#home")},children:"Uttam Rimal"}),(0,s.jsx)("p",{className:"text-muted-foreground leading-relaxed mb-6 max-w-md",children:"Creative Video Editor & Storyteller passionate about transforming footage into compelling narratives. Let's bring your vision to life through the power of visual storytelling."}),(0,s.jsx)("div",{className:"flex gap-4",children:t.map(({icon:e,href:t,label:r})=>(0,s.jsx)("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-foreground/10 hover:bg-accent text-foreground hover:text-accent-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110","aria-label":r,children:(0,s.jsx)(e,{size:18})},r))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-heading font-semibold text-accent mb-4",children:"Quick Links"}),(0,s.jsx)("ul",{className:"space-y-2",children:[{name:"Home",href:"#home"},{name:"Work",href:"#featured-work"},{name:"Clients",href:"#clients"},{name:"Shorts",href:"#shorts"},{name:"Blog",href:"#blog"},{name:"Testimonials",href:"#testimonials"},{name:"Contact",href:"#contact"}].map(t=>(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:t.href,onClick:r=>{r.preventDefault(),e(t.href)},className:"text-muted-foreground hover:text-accent transition-colors duration-300 text-sm block py-1",children:t.name})},t.name))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-heading font-semibold text-accent mb-4",children:"Get in Touch"}),(0,s.jsx)("ul",{className:"space-y-3",children:r.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center gap-3",children:[(0,s.jsx)(e.icon,{size:16,className:"text-accent flex-shrink-0"}),"#"!==e.href?(0,s.jsx)("a",{href:e.href,className:"text-muted-foreground hover:text-accent transition-colors duration-300 text-sm",children:e.text}):(0,s.jsx)("span",{className:"text-muted-foreground text-sm",children:e.text})]},t))}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("a",{href:"https://wa.me/9779840692118",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 bg-success hover:bg-success/90 text-success-foreground px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105",children:[(0,s.jsx)(m.A,{size:16}),"WhatsApp"]})})]})]}),(0,s.jsxs)("div",{className:"border-t border-border pt-8",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"text-muted-foreground text-sm text-center md:text-left",children:(0,s.jsxs)("p",{className:"flex items-center justify-center md:justify-start gap-1",children:["\xa9 ",new Date().getFullYear()," Uttam Rimal."]})}),(0,s.jsxs)(n(),{href:"http://ashishkamat.com.np",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-1 text-muted-foreground hover:text-accent transition-colors duration-300 text-sm font-medium",children:[(0,s.jsx)("span",{children:"Made with"}),(0,s.jsx)(f.A,{size:14,className:"text-destructive fill-current heartbeat"}),(0,s.jsx)("span",{children:"by:"}),(0,s.jsx)("span",{className:"font-semibold blink",children:"Ashish Kamat"})]})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("p",{className:"text-muted-foreground/60 text-xs",children:"Professional video editing services • Available for freelance projects • Based in Kathmandu, Nepal"})})]})]})]})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var s=r(37413),o=r(82459),n=r.n(o),i=r(61804),a=r.n(i),l=r(56296),d=r.n(l);r(61135);var c=r(87112),m=r(94555);let u={title:"Uttam Rimal - Professional Video Editor Portfolio",description:"Creative Video Editor & Storyteller. Transforming footage into compelling narratives with over a year of experience crafting visually stunning videos that engage and inspire.",keywords:"video editor, video editing, storytelling, motion graphics, post-production, creative editing, Nepal, freelance",authors:[{name:"Uttam Rimal"}],creator:"Uttam Rimal",openGraph:{type:"website",locale:"en_US",url:"https://uttamrimal.com",title:"Uttam Rimal - Professional Video Editor Portfolio",description:"Creative Video Editor & Storyteller. Transforming footage into compelling narratives.",siteName:"Uttam Rimal Portfolio",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"Uttam Rimal - Video Editor Portfolio"}]},twitter:{card:"summary_large_image",title:"Uttam Rimal - Professional Video Editor Portfolio",description:"Creative Video Editor & Storyteller. Transforming footage into compelling narratives.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function h({children:e}){return(0,s.jsxs)("html",{lang:"en",className:"scroll-smooth",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,s.jsx)("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),(0,s.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,s.jsx)("meta",{name:"theme-color",content:"#c6a664"})]}),(0,s.jsx)("body",{className:`${n().variable} ${a().variable} ${d().variable} antialiased`,children:(0,s.jsxs)(m.ThemeProvider,{children:[e,(0,s.jsx)(c.default,{})]})})]})}},94555:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\providers\\ThemeProvider.tsx","ThemeProvider")},94743:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var s=r(60687),o=r(43210),n=r(85814),i=r.n(n),a=r(16189),l=r(78272),d=r(11860),c=r(12941),m=r(29523),u=r(21134),h=r(363),f=r(10218);function x(){let{theme:e,setTheme:t}=(0,f.D)(),[r,n]=o.useState(!1);return(o.useEffect(()=>{n(!0)},[]),r)?(0,s.jsxs)(m.$,{variant:"ghost",size:"icon",onClick:()=>t("light"===e?"dark":"light"),className:"w-9 h-9 hover:bg-accent hover:text-accent-foreground transition-colors",children:["light"===e?(0,s.jsx)(h.A,{className:"h-[1.2rem] w-[1.2rem] transition-all"}):(0,s.jsx)(u.A,{className:"h-[1.2rem] w-[1.2rem] transition-all"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}):(0,s.jsxs)(m.$,{variant:"ghost",size:"icon",className:"w-9 h-9",children:[(0,s.jsx)(u.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}let g=[{name:"Home",href:"/",anchor:"#home"},{name:"Videos",href:"/",anchor:"#featured-work",dropdown:[{name:"Featured Videos",href:"/",anchor:"#featured-work"},{name:"View All Videos",href:"/videos"}]},{name:"Clients",href:"/",anchor:"#clients"},{name:"Shorts",href:"/",anchor:"#shorts",dropdown:[{name:"Featured Shorts",href:"/",anchor:"#shorts"},{name:"View All Shorts",href:"/reels"}]},{name:"Blog",href:"/",anchor:"#blog",dropdown:[{name:"Latest Posts",href:"/",anchor:"#blog"},{name:"View All Posts",href:"/blog"}]},{name:"Testimonials",href:"/",anchor:"#testimonials"},{name:"Contact",href:"/",anchor:"#contact"}];function p(){let e=(0,a.useRouter)(),t=(0,a.usePathname)(),[r,n]=(0,o.useState)(!1),[u,h]=(0,o.useState)(!1),[f,p]=(0,o.useState)("home"),[v,b]=(0,o.useState)(null),j=(0,o.useRef)(null),w=(r,s)=>{h(!1),b(null),s?"/"!==t?(e.push("/"),setTimeout(()=>{y(s.substring(1))},100)):y(s.substring(1)):e.push(r)},y=e=>{let t=document.getElementById(e);if(t){let e=t.getBoundingClientRect().top+window.pageYOffset-100;window.scrollTo({top:e,behavior:"smooth"})}},N=e=>{j.current&&clearTimeout(j.current),b(e)},k=()=>{j.current=setTimeout(()=>{b(null)},150)};return(0,s.jsx)("header",{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${r?"bg-background/95 backdrop-blur-md shadow-lg border-b border-foreground/10":"bg-background/90 backdrop-blur-sm"}`,children:(0,s.jsxs)("nav",{className:"container mx-auto px-4 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(i(),{href:"/",className:"text-2xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 hover:scale-105 transform",onClick:()=>{"/"===t&&y("home")},children:"UR"}),(0,s.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:g.map(e=>{let r="/"===t&&e.anchor&&f===e.anchor.substring(1),o=t===e.href&&!e.anchor;return(0,s.jsx)("div",{className:"relative group",children:e.dropdown?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{onClick:()=>w(e.href,e.anchor),onMouseEnter:()=>N(e.name),onMouseLeave:k,className:`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 flex items-center gap-1 ${r||o?"text-accent":"text-foreground hover:text-accent"}`,children:[e.name,(0,s.jsx)(l.A,{size:14,className:`transition-transform duration-200 ${v===e.name?"rotate-180":""}`}),(0,s.jsx)("span",{className:`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${r||o?"w-full":"w-0 group-hover:w-full"}`})]}),(0,s.jsx)("div",{className:`absolute top-full left-0 mt-2 w-48 bg-background/95 backdrop-blur-md border border-foreground/10 rounded-lg shadow-lg transition-all duration-200 ${v===e.name?"opacity-100 visible translate-y-0":"opacity-0 invisible -translate-y-2"}`,onMouseEnter:()=>N(e.name),onMouseLeave:k,children:e.dropdown.map(e=>(0,s.jsx)("button",{onClick:()=>w(e.href,e.anchor),className:"block w-full text-left px-4 py-3 text-sm text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg",children:e.name},e.name))})]}):(0,s.jsxs)("button",{onClick:()=>w(e.href,e.anchor),className:`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 ${r||o?"text-accent":"text-foreground hover:text-accent"}`,children:[e.name,(0,s.jsx)("span",{className:`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${r||o?"w-full":"w-0 group-hover:w-full"}`})]})},e.name)})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x,{}),(0,s.jsx)(m.$,{variant:"ghost",size:"icon",className:"md:hidden text-foreground hover:text-accent hover:bg-foreground/10",onClick:()=>h(!u),"aria-label":"Toggle menu",children:u?(0,s.jsx)(d.A,{size:24}):(0,s.jsx)(c.A,{size:24})})]})]}),(0,s.jsx)("div",{className:`md:hidden transition-all duration-300 overflow-hidden ${u?"max-h-[500px] opacity-100":"max-h-0 opacity-0"}`,children:(0,s.jsxs)("div",{className:"py-4 space-y-2 border-t border-foreground/10 mt-4",children:[g.map(e=>{let r="/"===t&&e.anchor&&f===e.anchor.substring(1),o=t===e.href&&!e.anchor;return(0,s.jsxs)("div",{children:[(0,s.jsx)("button",{onClick:()=>w(e.href,e.anchor),className:`block w-full text-left px-4 py-3 text-sm font-semibold transition-colors duration-300 rounded-lg ${r||o?"text-accent bg-foreground/10":"text-foreground hover:text-accent hover:bg-foreground/5"}`,children:e.name}),e.dropdown&&(0,s.jsx)("div",{className:"ml-4 mt-1 space-y-1",children:e.dropdown.map(e=>(0,s.jsx)("button",{onClick:()=>w(e.href,e.anchor),className:"block w-full text-left px-4 py-2 text-xs text-foreground/80 hover:text-accent hover:bg-foreground/5 transition-colors duration-300 rounded-lg",children:e.name},e.name))})]},e.name)}),(0,s.jsx)("div",{className:"px-4 py-3 border-t border-foreground/10 mt-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-semibold text-foreground",children:"Theme"}),(0,s.jsx)(x,{})]})})]})})]})})}},98664:(e,t,r)=>{Promise.resolve().then(r.bind(r,84718)),Promise.resolve().then(r.bind(r,41465))}};