(()=>{var e={};e.id=2076,e.ids=[2076],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9005:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16119:(e,t,a)=>{Promise.resolve().then(a.bind(a,34015))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23527:(e,t,a)=>{"use strict";a.d(t,{A:()=>x});var s=a(60687),r=a(43210),i=a(29523),n=a(89667),l=a(80013),d=a(44493),o=a(11860),c=a(41862),u=a(9005);let p=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var h=a(47342),m=a(30474);function x({value:e,onChange:t,label:a="Image",folder:x="portfolio-cms",className:v="",accept:f="image/*",maxSize:g=5}){let[j,b]=(0,r.useState)(!1),[y,k]=(0,r.useState)(""),[w,N]=(0,r.useState)(""),[C,A]=(0,r.useState)(!1),P=(0,r.useRef)(null),R=async e=>{if(e){if(e.size>1024*g*1024)return void k(`File size must be less than ${g}MB`);if(!e.type.startsWith("image/"))return void k("Please select a valid image file");b(!0),k("");try{let a=new FormData;a.append("file",e),a.append("folder",x);let s=await fetch("/api/upload",{method:"POST",body:a}),r=await s.json();s.ok?t(r.url):k(r.error||"Upload failed")}catch(e){k("Network error occurred")}finally{b(!1)}}},F=()=>{w.trim()&&(t(w.trim()),N(""),A(!1))};return(0,s.jsxs)("div",{className:`space-y-4 ${v}`,children:[(0,s.jsx)(l.J,{children:a}),e?(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.default,{src:e,alt:"Uploaded image",width:300,height:200,className:"w-full h-48 object-cover rounded-lg"}),(0,s.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{t(""),P.current&&(P.current.value="")},children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2 truncate",children:e})]})}):(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&R(t)},onDragOver:e=>{e.preventDefault()},children:j?(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-500 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop an image here, or click to select"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>P.current?.click(),children:[(0,s.jsx)(p,{className:"mr-2 h-4 w-4"}),"Upload File"]}),(0,s.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>A(!C),children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Add URL"]})]})]})}),C&&(0,s.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,s.jsx)(n.p,{placeholder:"Enter image URL...",value:w,onChange:e=>N(e.target.value),onKeyPress:e=>"Enter"===e.key&&F()}),(0,s.jsx)(i.$,{type:"button",onClick:F,children:"Add"}),(0,s.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>A(!1),children:"Cancel"})]}),y&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-2",children:y}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Supports: JPG, PNG, GIF, WebP (max ",g,"MB)"]})]})}),(0,s.jsx)("input",{ref:P,type:"file",accept:f,onChange:e=>{let t=e.target.files?.[0];t&&R(t)},className:"hidden"})]})}},27791:(e,t,a)=>{Promise.resolve().then(a.bind(a,79017))},28559:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34015:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\testimonials\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\testimonials\\new\\page.tsx","default")},34729:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var s=a(60687);a(43210);var r=a(4780);function i({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},37497:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=a(65239),r=a(48088),i=a(88170),n=a.n(i),l=a(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(t,d);let o={children:["",{children:["dashboard",{children:["testimonials",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,34015)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\testimonials\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\testimonials\\new\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/testimonials/new/page",pathname:"/dashboard/testimonials/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},41862:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},47342:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},54987:(e,t,a)=>{"use strict";a.d(t,{d:()=>n});var s=a(60687);a(43210);var r=a(83680),i=a(4780);function n({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78148:(e,t,a)=>{"use strict";a.d(t,{b:()=>l});var s=a(43210),r=a(14163),i=a(60687),n=s.forwardRef((e,t)=>(0,i.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},79017:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var s=a(60687),r=a(43210),i=a(16189),n=a(72455),l=a(23527),d=a(29523),o=a(89667),c=a(34729),u=a(80013),p=a(44493),h=a(54987),m=a(91821),x=a(64398),v=a(28559),f=a(8819),g=a(13861),j=a(85814),b=a.n(j);function y(){let e=(0,i.useRouter)(),[t,a]=(0,r.useState)(!1),[j,y]=(0,r.useState)(""),[k,w]=(0,r.useState)(""),[N,C]=(0,r.useState)({name:"",role:"",company:"",content:"",avatar:"",rating:5,featured:!1,status:"draft",order:0,email:"",linkedinUrl:""}),A=(e,t)=>{C(a=>({...a,[e]:t}))},P=async t=>{a(!0),y(""),w("");try{let a=await fetch("/api/testimonials",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...N,status:t})}),s=await a.json();a.ok?(w(`Testimonial ${"published"===t?"published":"saved as draft"} successfully!`),setTimeout(()=>{e.push("/dashboard/testimonials")},1500)):y(s.error||"Failed to save testimonial")}catch(e){y("Network error occurred")}finally{a(!1)}},R=(e,t=!1)=>(0,s.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(a=>(0,s.jsx)(x.A,{className:`h-5 w-5 ${a<=e?"text-yellow-400 fill-current":"text-gray-300"} ${t?"cursor-pointer hover:text-yellow-400":""}`,onClick:t?()=>A("rating",a):void 0},a))});return(0,s.jsx)(n.A,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(b(),{href:"/dashboard/testimonials",children:(0,s.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back to Testimonials"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"New Testimonial"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Add a new client testimonial"})]})]})}),j&&(0,s.jsx)(m.Fc,{variant:"destructive",children:(0,s.jsx)(m.TN,{children:j})}),k&&(0,s.jsx)(m.Fc,{children:(0,s.jsx)(m.TN,{children:k})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Client Information"})}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"name",children:"Name *"}),(0,s.jsx)(o.p,{id:"name",value:N.name,onChange:e=>A("name",e.target.value),placeholder:"Client's full name...",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"role",children:"Role/Position *"}),(0,s.jsx)(o.p,{id:"role",value:N.role,onChange:e=>A("role",e.target.value),placeholder:"e.g., CEO, Marketing Director, Founder",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"company",children:"Company"}),(0,s.jsx)(o.p,{id:"company",value:N.company,onChange:e=>A("company",e.target.value),placeholder:"Company name (optional)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(o.p,{id:"email",type:"email",value:N.email,onChange:e=>A("email",e.target.value),placeholder:"<EMAIL> (optional)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"linkedinUrl",children:"LinkedIn URL"}),(0,s.jsx)(o.p,{id:"linkedinUrl",value:N.linkedinUrl,onChange:e=>A("linkedinUrl",e.target.value),placeholder:"https://linkedin.com/in/client (optional)"})]}),(0,s.jsx)("div",{children:(0,s.jsx)(l.A,{label:"Avatar/Photo",value:N.avatar,onChange:e=>A("avatar",e),folder:"testimonials"})})]})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Testimonial Content"})}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"content",children:"Testimonial Content *"}),(0,s.jsx)(c.T,{id:"content",value:N.content,onChange:e=>A("content",e.target.value),placeholder:"Write the client's testimonial here...",rows:6,required:!0}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[N.content.length," characters"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{children:"Rating *"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[R(N.rating,!0),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[N.rating," out of 5 stars"]})]})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Publish"})}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.d,{id:"featured",checked:N.featured,onCheckedChange:e=>A("featured",e)}),(0,s.jsx)(u.J,{htmlFor:"featured",children:"Featured Testimonial"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(d.$,{onClick:()=>P("draft"),variant:"outline",className:"w-full",disabled:t,children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Save Draft"]}),(0,s.jsxs)(d.$,{onClick:()=>P("published"),className:"w-full",disabled:t||!N.name||!N.role||!N.content,children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Publish"]})]})]})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Settings"})}),(0,s.jsx)(p.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"order",children:"Display Order"}),(0,s.jsx)(o.p,{id:"order",type:"number",value:N.order,onChange:e=>A("order",parseInt(e.target.value)||0),placeholder:"0"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Lower numbers appear first"})]})})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Preview"})}),(0,s.jsx)(p.Wu,{children:(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[N.avatar?(0,s.jsx)("img",{src:N.avatar,alt:N.name,className:"w-12 h-12 rounded-full object-cover"}):(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-500 text-sm",children:N.name.charAt(0).toUpperCase()||"?"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-sm",children:N.name||"Client Name"}),(0,s.jsxs)("div",{className:"text-xs text-gray-600",children:[N.role||"Role",N.company&&` at ${N.company}`]})]})]}),(0,s.jsxs)("p",{className:"text-sm text-gray-700 mb-2",children:['"',N.content||"Testimonial content will appear here...",'"']}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[R(N.rating),N.featured&&(0,s.jsx)("span",{className:"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:"Featured"})]})]})})]})]})]})]})})}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(60687);a(43210);var r=a(78148),i=a(4780);function n({className:e,...t}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},83680:(e,t,a)=>{"use strict";a.d(t,{bL:()=>y,zi:()=>k});var s=a(43210),r=a(70569),i=a(98599),n=a(11273),l=a(65551),d=a(18853),o=a(14163),c=a(60687),u="Switch",[p,h]=(0,n.A)(u),[m,x]=p(u),v=s.forwardRef((e,t)=>{let{__scopeSwitch:a,name:n,checked:d,defaultChecked:p,required:h,disabled:x,value:v="on",onCheckedChange:f,form:g,...y}=e,[k,w]=s.useState(null),N=(0,i.s)(t,e=>w(e)),C=s.useRef(!1),A=!k||g||!!k.closest("form"),[P,R]=(0,l.i)({prop:d,defaultProp:p??!1,onChange:f,caller:u});return(0,c.jsxs)(m,{scope:a,checked:P,disabled:x,children:[(0,c.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":P,"aria-required":h,"data-state":b(P),"data-disabled":x?"":void 0,disabled:x,value:v,...y,ref:N,onClick:(0,r.m)(e.onClick,e=>{R(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,c.jsx)(j,{control:k,bubbles:!C.current,name:n,value:v,checked:P,required:h,disabled:x,form:g,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var f="SwitchThumb",g=s.forwardRef((e,t)=>{let{__scopeSwitch:a,...s}=e,r=x(f,a);return(0,c.jsx)(o.sG.span,{"data-state":b(r.checked),"data-disabled":r.disabled?"":void 0,...s,ref:t})});g.displayName=f;var j=s.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:r=!0,...n},l)=>{let o=s.useRef(null),u=(0,i.s)(o,l),p=function(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(a),h=(0,d.X)(t);return s.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&t){let s=new Event("click",{bubbles:r});t.call(e,a),e.dispatchEvent(s)}},[p,a,r]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...n,tabIndex:-1,ref:u,style:{...n.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var y=v,k=g},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>l,TN:()=>d});var s=a(60687);a(43210);var r=a(24224),i=a(4780);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...a}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...a})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,1771,1658,9365,4758,474,9638],()=>a(37497));module.exports=s})();