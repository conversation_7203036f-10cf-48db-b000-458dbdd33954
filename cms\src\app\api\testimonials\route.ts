import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Testimonial from '@/models/Testimonial';
import { requireAuth } from '@/lib/auth';
import { withCors } from '@/lib/cors';

// GET /api/testimonials - Get all testimonials with pagination and filters
export const GET = withCors(async (request: NextRequest) => {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const featured = searchParams.get('featured');
    const rating = searchParams.get('rating');
    const search = searchParams.get('search');

    // Build query
    const query: any = {};

    if (status) query.status = status;
    if (featured !== null) query.featured = featured === 'true';
    if (rating) query.rating = parseInt(rating);
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { company: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { role: { $regex: search, $options: 'i' } },
      ];
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Get testimonials with pagination
    const testimonials = await Testimonial.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count
    const total = await Testimonial.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: testimonials,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Get testimonials error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// POST /api/testimonials - Create new testimonial
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const data = await request.json();
    const {
      name,
      role,
      company,
      content,
      avatar,
      rating = 5,
      featured = false,
      status = 'draft',
      order = 0,
      email,
      linkedinUrl,
    } = data;

    // Validate required fields
    if (!name || !role || !content) {
      return NextResponse.json(
        { error: 'Name, role, and content are required' },
        { status: 400 }
      );
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Create testimonial
    const testimonial = new Testimonial({
      name,
      role,
      company,
      content,
      avatar,
      rating,
      featured,
      status,
      order,
      email,
      linkedinUrl,
    });

    await testimonial.save();

    return NextResponse.json({
      success: true,
      data: testimonial,
    }, { status: 201 });
  } catch (error) {
    console.error('Create testimonial error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
