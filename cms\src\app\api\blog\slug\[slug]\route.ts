import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import BlogPost from '@/models/BlogPost';
import { setCorsHeaders } from '@/lib/cors';

interface RouteParams {
  params: Promise<{ slug: string }>;
}

// GET /api/blog/slug/[slug] - Get blog post by slug
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { slug } = await params;

    const post = await BlogPost.findOne({
      slug: slug,
      status: 'published'
    });

    if (!post) {
      const response = NextResponse.json(
        { success: false, message: 'Blog post not found' },
        { status: 404 }
      );
      setCorsHeaders(response, request.headers.get('origin'));
      return response;
    }

    const response = NextResponse.json({
      success: true,
      data: post
    });
    setCorsHeaders(response, request.headers.get('origin'));
    return response;

  } catch (error) {
    console.error('Error fetching blog post by slug:', error);
    const response = NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
    setCorsHeaders(response, request.headers.get('origin'));
    return response;
  }
}

// Note: OPTIONS requests are handled by middleware.ts
