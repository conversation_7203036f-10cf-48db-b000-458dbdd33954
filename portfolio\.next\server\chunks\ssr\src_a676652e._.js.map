{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        success:\n          \"bg-success text-success-foreground shadow-xs hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40\",\n        warning:\n          \"bg-warning text-warning-foreground shadow-xs hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40\",\n        info:\n          \"bg-info text-info-foreground shadow-xs hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,SACE;YACF,SACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/app/not-found.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Home, ArrowLeft, Search, FileX } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport Header from '@/components/layout/Header';\nimport Footer from '@/components/layout/Footer';\n\nexport default function NotFound() {\n  return (\n    <main>\n      <Header />\n      \n      <div className=\"min-h-screen bg-gradient-to-br from-muted via-background to-muted flex items-center justify-center px-4\">\n        <div className=\"max-w-2xl mx-auto text-center\">\n          {/* 404 Animation */}\n          <div className=\"relative mb-8\">\n            <div className=\"text-8xl md:text-9xl font-bold text-primary/20 select-none\">\n              404\n            </div>\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <div className=\"bg-card p-6 rounded-full shadow-lg animate-bounce\">\n                <FileX size={48} className=\"text-primary\" />\n              </div>\n            </div>\n          </div>\n\n          {/* Error Message */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl md:text-4xl font-heading font-bold text-primary mb-4\">\n              Oops! Page Not Found\n            </h1>\n            <p className=\"text-lg text-muted-foreground leading-relaxed mb-6\">\n              The page you&apos;re looking for seems to have wandered off into the digital void.\n              Don&apos;t worry, even the best video editors sometimes lose track of their clips!\n            </p>\n            <p className=\"text-muted-foreground\">\n              Let&apos;s get you back on track with some helpful options below.\n            </p>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n            <Button asChild>\n              <Link href=\"/\" className=\"inline-flex items-center gap-2\">\n                <Home size={20} />\n                Back to Home\n              </Link>\n            </Button>\n            \n            <Button variant=\"outline\" asChild>\n              <Link href=\"/blog\" className=\"inline-flex items-center gap-2\">\n                <Search size={20} />\n                Browse Blog\n              </Link>\n            </Button>\n            \n            <Button variant=\"outline\" asChild>\n              <Link href=\"/videos\" className=\"inline-flex items-center gap-2\">\n                <ArrowLeft size={20} />\n                View Portfolio\n              </Link>\n            </Button>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"bg-card rounded-xl shadow-md p-6 mb-8\">\n            <h2 className=\"text-xl font-semibold text-primary mb-4\">\n              Popular Pages\n            </h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <Link \n                href=\"/videos\" \n                className=\"p-3 rounded-lg hover:bg-muted transition-colors text-center group\"\n              >\n                <div className=\"text-primary group-hover:scale-110 transition-transform duration-300 mb-2\">\n                  🎬\n                </div>\n                <span className=\"text-sm font-medium\">Videos</span>\n              </Link>\n              \n              <Link\n                href=\"/reels\"\n                className=\"p-3 rounded-lg hover:bg-muted transition-colors text-center group\"\n              >\n                <div className=\"text-primary group-hover:scale-110 transition-transform duration-300 mb-2\">\n                  📱\n                </div>\n                <span className=\"text-sm font-medium\">Shorts</span>\n              </Link>\n              \n              <Link \n                href=\"/blog\" \n                className=\"p-3 rounded-lg hover:bg-muted transition-colors text-center group\"\n              >\n                <div className=\"text-primary group-hover:scale-110 transition-transform duration-300 mb-2\">\n                  📝\n                </div>\n                <span className=\"text-sm font-medium\">Blog</span>\n              </Link>\n              \n              <Link \n                href=\"/clients\" \n                className=\"p-3 rounded-lg hover:bg-muted transition-colors text-center group\"\n              >\n                <div className=\"text-primary group-hover:scale-110 transition-transform duration-300 mb-2\">\n                  🤝\n                </div>\n                <span className=\"text-sm font-medium\">Clients</span>\n              </Link>\n            </div>\n          </div>\n\n          {/* Fun Message */}\n          <div className=\"text-sm text-muted-foreground\">\n            <p>\n              💡 <strong>Pro Tip:</strong> Just like in video editing, sometimes the best content \n              is found when you explore different angles. Try the links above!\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <Footer />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA6D;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAIlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;8CAKtB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,OAAO;8CAC/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;;0DAC3B,8OAAC,sMAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;8CAKxB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,OAAO;8CAC/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;;0DAC7B,8OAAC,gNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;sCAO7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DAA4E;;;;;;8DAG3F,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAGxC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DAA4E;;;;;;8DAG3F,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAGxC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DAA4E;;;;;;8DAG3F,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAGxC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DAA4E;;;;;;8DAG3F,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;oCAAE;kDACE,8OAAC;kDAAO;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}