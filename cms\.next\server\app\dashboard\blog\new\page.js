(()=>{var e={};e.id=3551,e.ids=[3551],e.modules={2876:(e,t,s)=>{"use strict";s.d(t,{A:()=>S});var r=s(60687),a=s(7203),i=s(76928),n=s(79801),l=s(2188),o=s(84633),d=s(36293),c=s(55023),u=s(98926),x=s(29523),h=s(27777),p=s(11922),g=s(75687),m=s(80375),v=s(45984),f=s(69169),b=s(21782),j=s(25366),y=s(14290),w=s(98916),k=s(95999),N=s(80051),A=s(8366),C=s(47342),z=s(9005),P=s(54388),$=s(31110),T=s(43210);function S({content:e,onChange:t,placeholder:s="Start writing...",className:S=""}){let L=(0,a.hG)({extensions:[i.A,n.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),l.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-600 hover:text-blue-800 underline"}}),o.A.configure({types:["heading","paragraph"]}),d.A,c.A,u.Ay.configure({multicolor:!0})],content:e,onUpdate:({editor:e})=>{t(e.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[300px] p-4"}}}),_=(0,T.useCallback)(()=>{let e=window.prompt("Enter image URL:");e&&L&&L.chain().focus().setImage({src:e}).run()},[L]),E=(0,T.useCallback)(()=>{let e=L?.getAttributes("link").href,t=window.prompt("Enter URL:",e);if(null!==t){if(""===t)return void L?.chain().focus().extendMarkRange("link").unsetLink().run();L?.chain().focus().extendMarkRange("link").setLink({href:t}).run()}},[L]);return L?(0,r.jsxs)("div",{className:`border border-gray-300 rounded-lg ${S}`,children:[(0,r.jsxs)("div",{className:"border-b border-gray-300 p-2 flex flex-wrap gap-1",children:[(0,r.jsx)(x.$,{variant:L.isActive("bold")?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleBold().run(),children:(0,r.jsx)(h.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive("italic")?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleItalic().run(),children:(0,r.jsx)(p.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive("strike")?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleStrike().run(),children:(0,r.jsx)(g.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive("code")?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleCode().run(),children:(0,r.jsx)(m.A,{size:16})}),(0,r.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,r.jsx)(x.$,{variant:L.isActive("heading",{level:1})?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleHeading({level:1}).run(),children:(0,r.jsx)(v.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive("heading",{level:2})?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleHeading({level:2}).run(),children:(0,r.jsx)(f.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive("heading",{level:3})?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleHeading({level:3}).run(),children:(0,r.jsx)(b.A,{size:16})}),(0,r.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,r.jsx)(x.$,{variant:L.isActive("bulletList")?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleBulletList().run(),children:(0,r.jsx)(j.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive("orderedList")?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleOrderedList().run(),children:(0,r.jsx)(y.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive("blockquote")?"default":"outline",size:"sm",onClick:()=>L.chain().focus().toggleBlockquote().run(),children:(0,r.jsx)(w.A,{size:16})}),(0,r.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,r.jsx)(x.$,{variant:L.isActive({textAlign:"left"})?"default":"outline",size:"sm",onClick:()=>L.chain().focus().setTextAlign("left").run(),children:(0,r.jsx)(k.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive({textAlign:"center"})?"default":"outline",size:"sm",onClick:()=>L.chain().focus().setTextAlign("center").run(),children:(0,r.jsx)(N.A,{size:16})}),(0,r.jsx)(x.$,{variant:L.isActive({textAlign:"right"})?"default":"outline",size:"sm",onClick:()=>L.chain().focus().setTextAlign("right").run(),children:(0,r.jsx)(A.A,{size:16})}),(0,r.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,r.jsx)(x.$,{variant:"outline",size:"sm",onClick:E,children:(0,r.jsx)(C.A,{size:16})}),(0,r.jsx)(x.$,{variant:"outline",size:"sm",onClick:_,children:(0,r.jsx)(z.A,{size:16})}),(0,r.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,r.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>L.chain().focus().undo().run(),disabled:!L.can().undo(),children:(0,r.jsx)(P.A,{size:16})}),(0,r.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>L.chain().focus().redo().run(),disabled:!L.can().redo(),children:(0,r.jsx)($.A,{size:16})})]}),(0,r.jsx)(a.$Z,{editor:L,className:"min-h-[300px] max-h-[600px] overflow-y-auto"})]}):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12135:(e,t,s)=>{Promise.resolve().then(s.bind(s,64419))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23527:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(60687),a=s(43210),i=s(29523),n=s(89667),l=s(80013),o=s(44493),d=s(11860),c=s(41862),u=s(9005);let x=(0,s(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var h=s(47342),p=s(30474);function g({value:e,onChange:t,label:s="Image",folder:g="portfolio-cms",className:m="",accept:v="image/*",maxSize:f=5}){let[b,j]=(0,a.useState)(!1),[y,w]=(0,a.useState)(""),[k,N]=(0,a.useState)(""),[A,C]=(0,a.useState)(!1),z=(0,a.useRef)(null),P=async e=>{if(e){if(e.size>1024*f*1024)return void w(`File size must be less than ${f}MB`);if(!e.type.startsWith("image/"))return void w("Please select a valid image file");j(!0),w("");try{let s=new FormData;s.append("file",e),s.append("folder",g);let r=await fetch("/api/upload",{method:"POST",body:s}),a=await r.json();r.ok?t(a.url):w(a.error||"Upload failed")}catch(e){w("Network error occurred")}finally{j(!1)}}},$=()=>{k.trim()&&(t(k.trim()),N(""),C(!1))};return(0,r.jsxs)("div",{className:`space-y-4 ${m}`,children:[(0,r.jsx)(l.J,{children:s}),e?(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.default,{src:e,alt:"Uploaded image",width:300,height:200,className:"w-full h-48 object-cover rounded-lg"}),(0,r.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{t(""),z.current&&(z.current.value="")},children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2 truncate",children:e})]})}):(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&P(t)},onDragOver:e=>{e.preventDefault()},children:b?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-500 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop an image here, or click to select"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>z.current?.click(),children:[(0,r.jsx)(x,{className:"mr-2 h-4 w-4"}),"Upload File"]}),(0,r.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>C(!A),children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Add URL"]})]})]})}),A&&(0,r.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,r.jsx)(n.p,{placeholder:"Enter image URL...",value:k,onChange:e=>N(e.target.value),onKeyPress:e=>"Enter"===e.key&&$()}),(0,r.jsx)(i.$,{type:"button",onClick:$,children:"Add"}),(0,r.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>C(!1),children:"Cancel"})]}),y&&(0,r.jsx)("p",{className:"text-sm text-red-600 mt-2",children:y}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Supports: JPG, PNG, GIF, WebP (max ",f,"MB)"]})]})}),(0,r.jsx)("input",{ref:z,type:"file",accept:v,onChange:e=>{let t=e.target.files?.[0];t&&P(t)},className:"hidden"})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},42399:(e,t,s)=>{Promise.resolve().then(s.bind(s,48665))},48665:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(60687),a=s(43210),i=s(16189),n=s(72455),l=s(2876),o=s(23527),d=s(29523),c=s(89667),u=s(34729),x=s(80013),h=s(44493),p=s(54987),g=s(96834),m=s(91821),v=s(28559),f=s(8819),b=s(13861),j=s(11860),y=s(85814),w=s.n(y);function k(){let e=(0,i.useRouter)(),[t,s]=(0,a.useState)(!1),[y,k]=(0,a.useState)(""),[N,A]=(0,a.useState)(""),[C,z]=(0,a.useState)({title:"",excerpt:"",content:"",category:"",tags:[],featured:!1,status:"draft",thumbnail:""}),[P,$]=(0,a.useState)(""),T=(e,t)=>{z(s=>({...s,[e]:t}))},S=()=>{P.trim()&&!C.tags.includes(P.trim())&&(z(e=>({...e,tags:[...e.tags,P.trim()]})),$(""))},L=e=>{z(t=>({...t,tags:t.tags.filter(t=>t!==e)}))},_=()=>{let e=C.title.length>60?C.title.substring(0,57)+"...":C.title;return{seoTitle:e,seoDescription:C.excerpt.length>160?C.excerpt.substring(0,157)+"...":C.excerpt,seoKeywords:[...new Set([...C.title.toLowerCase().split(" ").filter(e=>e.length>3),C.category.toLowerCase(),...C.tags.map(e=>e.toLowerCase())].filter(Boolean).slice(0,10))]}},E=async t=>{s(!0),k(""),A("");try{let s=_(),r=await fetch("/api/blog",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...C,...s,status:t})}),a=await r.json();r.ok?(A(`Blog post ${"published"===t?"published":"saved as draft"} successfully!`),setTimeout(()=>{e.push("/dashboard/blog")},1500)):k(a.error||"Failed to save blog post")}catch(e){k("Network error occurred")}finally{s(!1)}};return(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(w(),{href:"/dashboard/blog",children:(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back to Posts"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"New Blog Post"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Create a new blog post"})]})]})}),y&&(0,r.jsx)(m.Fc,{variant:"destructive",children:(0,r.jsx)(m.TN,{children:y})}),N&&(0,r.jsx)(m.Fc,{children:(0,r.jsx)(m.TN,{children:N})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"xl:col-span-3 space-y-8",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:"Post Content"})}),(0,r.jsxs)(h.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"title",children:"Title *"}),(0,r.jsx)(c.p,{id:"title",value:C.title,onChange:e=>T("title",e.target.value),placeholder:"Enter post title...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"excerpt",children:"Excerpt *"}),(0,r.jsx)(u.T,{id:"excerpt",value:C.excerpt,onChange:e=>T("excerpt",e.target.value),placeholder:"Brief description of the post...",rows:3,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{children:"Content *"}),(0,r.jsx)(l.A,{content:C.content,onChange:e=>T("content",e),placeholder:"Start writing your blog post..."})]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"SEO Preview"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Auto-generated from your content"})]}),(0,r.jsxs)(h.Wu,{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-gray-50 p-6 rounded-lg border",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"text-blue-600 text-lg font-medium line-clamp-1",children:C.title||"Your blog post title will appear here"}),(0,r.jsxs)("div",{className:"text-green-700 text-sm",children:["yourwebsite.com/blog/",C.title?C.title.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""):"post-slug"]}),(0,r.jsx)("div",{className:"text-gray-700 text-sm line-clamp-2",children:C.excerpt||"Your post excerpt will appear here as the meta description..."})]})}),(C.title||C.excerpt||C.category||C.tags.length>0)&&(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{className:"text-sm font-medium",children:"Auto-generated Keywords:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:_().seoKeywords.map((e,t)=>(0,r.jsx)(g.E,{variant:"outline",className:"text-xs",children:e},t))})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6 xl:sticky xl:top-6 xl:h-fit",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:"Publish"})}),(0,r.jsxs)(h.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.d,{id:"featured",checked:C.featured,onCheckedChange:e=>T("featured",e)}),(0,r.jsx)(x.J,{htmlFor:"featured",children:"Featured Post"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(d.$,{onClick:()=>E("draft"),variant:"outline",className:"w-full",disabled:t,children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Save Draft"]}),(0,r.jsxs)(d.$,{onClick:()=>E("published"),className:"w-full",disabled:t||!C.title||!C.excerpt||!C.content,children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Publish"]})]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:"Post Settings"})}),(0,r.jsxs)(h.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"category",children:"Category *"}),(0,r.jsx)(c.p,{id:"category",value:C.category,onChange:e=>T("category",e.target.value),placeholder:"e.g., Tutorial, Tips, Review",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{children:"Tags"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(c.p,{value:P,onChange:e=>$(e.target.value),placeholder:"Add tag...",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),S())}),(0,r.jsx)(d.$,{type:"button",onClick:S,variant:"outline",children:"Add"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:C.tags.map((e,t)=>(0,r.jsxs)(g.E,{variant:"outline",className:"flex items-center gap-1",children:[e,(0,r.jsx)(j.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>L(e)})]},t))})]}),(0,r.jsx)("div",{children:(0,r.jsx)(o.A,{label:"Featured Image",value:C.thumbnail,onChange:e=>T("thumbnail",e),folder:"blog"})})]})]})]})]})]})})}},54987:(e,t,s)=>{"use strict";s.d(t,{d:()=>n});var r=s(60687);s(43210);var a=s(83680),i=s(4780);function n({className:e,...t}){return(0,r.jsx)(a.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64419:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\blog\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\blog\\new\\page.tsx","default")},73577:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["dashboard",{children:["blog",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,64419)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\blog\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\blog\\new\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/blog/new/page",pathname:"/dashboard/blog/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var r=s(60687);s(43210);var a=s(78148),i=s(4780);function n({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>l,TN:()=>o});var r=s(60687);s(43210);var a=s(24224),i=s(4780);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...s})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...i}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,1771,1658,9365,4758,474,2771,9638],()=>s(73577));module.exports=r})();