"use strict";(()=>{var e={};e.id=7368,e.ids=[7368],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46564:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>h,serverHooks:()=>j,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>A});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>w,PUT:()=>g});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(75745),d=t(63675),p=t(12909),l=t(27746),c=t(67912),x=t.n(c);let f=e=>{let r=e.nextUrl.pathname.split("/");return r[r.length-1]};async function w(e){try{await (0,u.A)();let r=f(e),t=await d.A.findOne({$or:[{_id:r},{slug:r}]}).lean();if(!t){let r=i.NextResponse.json({error:"Client not found"},{status:404});return(0,l.Sw)(r,e.headers.get("origin")),r}let s=i.NextResponse.json({success:!0,data:t});return(0,l.Sw)(s,e.headers.get("origin")),s}catch(t){console.error("Get client error:",t);let r=i.NextResponse.json({error:"Internal server error"},{status:500});return(0,l.Sw)(r,e.headers.get("origin")),r}}let g=(0,p.oC)(async e=>{try{await (0,u.A)();let r=f(e),t=await e.json(),s=await d.A.findById(r);if(!s)return i.NextResponse.json({error:"Client not found"},{status:404});if(t.name&&t.name!==s.name){let e=x()(t.name,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g}),s=e,n=1;for(;await d.A.findOne({slug:s,_id:{$ne:r}});)s=`${e}-${n++}`;t.slug=s}let n=await d.A.findByIdAndUpdate(r,{...t,updatedAt:new Date},{new:!0,runValidators:!0});return i.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Update client error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}),m=(0,p.oC)(async e=>{try{await (0,u.A)();let r=f(e);if(!await d.A.findById(r))return i.NextResponse.json({error:"Client not found"},{status:404});return await d.A.findByIdAndDelete(r),i.NextResponse.json({success:!0,message:"Client deleted successfully"})}catch(e){return console.error("Delete client error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}),h=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/clients/[id]/route",pathname:"/api/clients/[id]",filename:"route",bundlePath:"app/api/clients/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\clients\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:A,serverHooks:j}=h;function v(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:A})}},55511:e=>{e.exports=require("crypto")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,8404],()=>t(46564));module.exports=s})();