module.exports = {

"[project]/src/data/testimonials.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_data_testimonials_ts_46afb684._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/testimonials.ts [app-rsc] (ecmascript)");
    });
});
}}),

};