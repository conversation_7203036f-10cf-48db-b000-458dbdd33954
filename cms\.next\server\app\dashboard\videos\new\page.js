(()=>{var e={};e.id=9131,e.ids=[9131],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6069:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(60687),a=r(43210),i=r(16189),d=r(72455),n=r(29523),o=r(89667),l=r(34729),c=r(80013),u=r(44493),p=r(54987),h=r(91821),x=r(96834),v=r(28559),m=r(8819),g=r(13861),f=(r(2943),r(85814)),b=r.n(f);function j(){let e=(0,i.useRouter)(),[t,r]=(0,a.useState)(!1),[f,j]=(0,a.useState)(""),[y,w]=(0,a.useState)(""),[k,N]=(0,a.useState)({id:"",title:"",description:"",category:"",tags:[],featured:!1,status:"draft",order:0}),[C,P]=(0,a.useState)(""),A=(e,t)=>{N(r=>({...r,[e]:t}))},E=()=>{C.trim()&&!k.tags.includes(C.trim().toLowerCase())&&(N(e=>({...e,tags:[...e.tags,C.trim().toLowerCase()]})),P(""))},D=e=>{N(t=>({...t,tags:t.tags.filter(t=>t!==e)}))},_=async t=>{r(!0),j(""),w("");try{let r=await fetch("/api/videos",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...k,status:t})}),s=await r.json();r.ok?(w(`Video ${"published"===t?"published":"saved as draft"} successfully!`),setTimeout(()=>{e.push("/dashboard/videos")},1500)):j(s.error||"Failed to save video")}catch(e){j("Network error occurred")}finally{r(!1)}};return(0,s.jsx)(d.A,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(b(),{href:"/dashboard/videos",children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back to Videos"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"New Video"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Add a new video to your portfolio"})]})]})}),f&&(0,s.jsx)(h.Fc,{variant:"destructive",children:(0,s.jsx)(h.TN,{children:f})}),y&&(0,s.jsx)(h.Fc,{children:(0,s.jsx)(h.TN,{children:y})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Video Information"})}),(0,s.jsxs)(u.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"title",children:"Title *"}),(0,s.jsx)(o.p,{id:"title",value:k.title,onChange:e=>A("title",e.target.value),placeholder:"Enter video title...",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"id",children:"Video ID *"}),(0,s.jsx)(o.p,{id:"id",value:k.id,onChange:e=>A("id",e.target.value),placeholder:"Enter video ID (e.g., EPoaSIIOVQM)",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"YouTube video ID from the URL"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"description",children:"Description"}),(0,s.jsx)(l.T,{id:"description",value:k.description,onChange:e=>A("description",e.target.value),placeholder:"Brief description of the video...",rows:3})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"category",children:"Category"}),(0,s.jsx)(o.p,{id:"category",value:k.category,onChange:e=>A("category",e.target.value),placeholder:"e.g., Corporate, Travel, Commercial, Event, Film, Music"})]})]})]}),(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Tags"})}),(0,s.jsxs)(u.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"tags",children:"Add Tags"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(o.p,{id:"tags",value:C,onChange:e=>P(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),E())},placeholder:"Enter tag and press Enter"}),(0,s.jsx)(n.$,{type:"button",onClick:E,variant:"outline",children:"Add"})]})]}),k.tags.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:k.tags.map((e,t)=>(0,s.jsxs)(x.E,{variant:"secondary",className:"cursor-pointer",children:[e,(0,s.jsx)("button",{onClick:()=>D(e),className:"ml-2 text-xs hover:text-red-600",children:"\xd7"})]},t))})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Publish"})}),(0,s.jsxs)(u.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.d,{id:"featured",checked:k.featured,onCheckedChange:e=>A("featured",e)}),(0,s.jsx)(c.J,{htmlFor:"featured",children:"Featured Video"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(n.$,{onClick:()=>_("draft"),variant:"outline",className:"w-full",disabled:t,children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Save Draft"]}),(0,s.jsxs)(n.$,{onClick:()=>_("published"),className:"w-full",disabled:t||!k.title||!k.id,children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Publish"]})]})]})]}),(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Settings"})}),(0,s.jsx)(u.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,s.jsx)(o.p,{id:"order",type:"number",value:k.order,onChange:e=>A("order",parseInt(e.target.value)||0),placeholder:"0"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Lower numbers appear first"})]})})]}),(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Preview"})}),(0,s.jsx)(u.Wu,{children:(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden",children:(0,s.jsx)("img",{src:"/images/placeholder.svg",alt:k.title,className:"w-full h-full object-cover"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("h3",{className:"font-medium text-sm",children:k.title||"Video Title"})}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:k.description||"Video description will appear here..."}),k.category&&(0,s.jsx)(x.E,{variant:"secondary",className:"text-xs",children:k.category}),k.featured&&(0,s.jsx)(x.E,{className:"text-xs bg-yellow-100 text-yellow-800",children:"Featured"}),k.id&&(0,s.jsxs)("p",{className:"text-xs text-gray-400",children:["ID: ",k.id]})]})]})})]})]})]})]})})}},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},54987:(e,t,r)=>{"use strict";r.d(t,{d:()=>d});var s=r(60687);r(43210);var a=r(83680),i=r(4780);function d({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},60791:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\videos\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\videos\\new\\page.tsx","default")},61155:(e,t,r)=>{Promise.resolve().then(r.bind(r,6069))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75617:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),d=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let l={children:["",{children:["dashboard",{children:["videos",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60791)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\videos\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\videos\\new\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/videos/new/page",pathname:"/dashboard/videos/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var s=r(43210),a=r(14163),i=r(60687),d=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var n=d},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687);r(43210);var a=r(78148),i=r(4780);function d({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},83680:(e,t,r)=>{"use strict";r.d(t,{bL:()=>y,zi:()=>w});var s=r(43210),a=r(70569),i=r(98599),d=r(11273),n=r(65551),o=r(18853),l=r(14163),c=r(60687),u="Switch",[p,h]=(0,d.A)(u),[x,v]=p(u),m=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:d,checked:o,defaultChecked:p,required:h,disabled:v,value:m="on",onCheckedChange:g,form:f,...y}=e,[w,k]=s.useState(null),N=(0,i.s)(t,e=>k(e)),C=s.useRef(!1),P=!w||f||!!w.closest("form"),[A,E]=(0,n.i)({prop:o,defaultProp:p??!1,onChange:g,caller:u});return(0,c.jsxs)(x,{scope:r,checked:A,disabled:v,children:[(0,c.jsx)(l.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":h,"data-state":j(A),"data-disabled":v?"":void 0,disabled:v,value:m,...y,ref:N,onClick:(0,a.m)(e.onClick,e=>{E(e=>!e),P&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),P&&(0,c.jsx)(b,{control:w,bubbles:!C.current,name:d,value:m,checked:A,required:h,disabled:v,form:f,style:{transform:"translateX(-100%)"}})]})});m.displayName=u;var g="SwitchThumb",f=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,a=v(g,r);return(0,c.jsx)(l.sG.span,{"data-state":j(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});f.displayName=g;var b=s.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:a=!0,...d},n)=>{let l=s.useRef(null),u=(0,i.s)(l,n),p=function(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r),h=(0,o.X)(t);return s.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let s=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(s)}},[p,r,a]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...d,tabIndex:-1,ref:u,style:{...d.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var y=m,w=f},85131:(e,t,r)=>{Promise.resolve().then(r.bind(r,60791))},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>n,TN:()=>o});var s=r(60687);r(43210);var a=r(24224),i=r(4780);let d=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(d({variant:t}),e),...r})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(60687);r(43210);var a=r(8730),i=r(24224),d=r(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:r=!1,...i}){let o=r?a.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,d.cn)(n({variant:t}),e),...i})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,1771,1658,9365,4758,9638],()=>r(75617));module.exports=s})();