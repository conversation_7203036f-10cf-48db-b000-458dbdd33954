{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/ScrollToTop.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <Button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n      aria-label=\"Scroll to top\"\n    >\n      <ArrowUp size={20} />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,OAAO,WAAW,GAAG,KAAK;wBAC5B,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,6LAAC,+MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;;;;;;AAGrB;GApCwB;KAAA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40\",\n        info:\n          \"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/app/reels/ReelsPageClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ArrowLeft, Play, X, Grid, List } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger, DialogClose } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { type Reel } from '@/lib/api';\n\ninterface ReelsPageClientProps {\n  allReels: Reel[];\n  featuredReels: Reel[];\n}\n\nexport default function ReelsPageClient({ allReels, featuredReels }: ReelsPageClientProps) {\n  const [selectedReel, setSelectedReel] = useState<string | null>(null);\n  const [filteredReels] = useState<Reel[]>(allReels);\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  // No platform filtering needed - only YouTube Shorts supported\n\n  // Only YouTube Shorts supported now\n  const getPlatformIcon = () => <Play size={16} />;\n  const getPlatformColor = () => 'bg-accent';\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <div className=\"gradient-secondary text-foreground py-20\">\n        <div className=\"container mx-auto px-4\">\n          <Link\n            href=\"/#shorts\"\n            className=\"inline-flex items-center gap-2 text-accent hover:text-foreground transition-colors mb-8 font-accent\"\n          >\n            <ArrowLeft size={20} />\n            Back to Portfolio\n          </Link>\n\n          <h1 className=\"text-4xl md:text-5xl text-foreground font-heading font-bold mb-4\">\n            YouTube Shorts Collection\n          </h1>\n          <p className=\"text-xl text-muted-foreground max-w-2xl font-body\">\n            Discover my creative short-form content on YouTube. Engaging shorts that showcase dynamic editing and storytelling in under 60 seconds.\n          </p>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16\">\n        {/* Featured Shorts */}\n        {featuredReels.length > 0 && (\n          <section className=\"mb-16\">\n            <h2 className=\"text-3xl font-heading font-bold text-foreground mb-8\">\n              Featured YouTube Shorts\n            </h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6\">\n              {featuredReels.map((reel) => (\n                <Dialog\n                  key={reel._id}\n                  open={selectedReel === reel.id}\n                  onOpenChange={(open) => {\n                    if (!open) {\n                      setSelectedReel(null);\n                    }\n                  }}\n                >\n                  <DialogTrigger asChild>\n                    <div\n                      onClick={() => setSelectedReel(reel.id)}\n                      className=\"group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer\"\n                    >\n                      <div className=\"relative aspect-[9/16] overflow-hidden\">\n                        <Image\n                          src={reel.thumbnail || ''}\n                          alt={reel.title}\n                          fill\n                          className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                        />\n\n                        <div className=\"absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center\">\n                          <div className=\"bg-accent/90 backdrop-blur-sm rounded-full p-3 group-hover:scale-110 transition-transform duration-300\">\n                            <Play className=\"h-6 w-6 text-accent-foreground ml-0.5\" fill=\"currentColor\" />\n                          </div>\n                        </div>\n\n                        <div className=\"absolute top-3 left-3\">\n                          <Badge variant=\"default\" className=\"bg-accent text-accent-foreground text-xs font-accent\">\n                            Featured\n                          </Badge>\n                        </div>\n\n                        <div className=\"absolute top-3 right-3\">\n                          <div className={`${getPlatformColor()} text-primary-foreground p-1.5 rounded-full`}>\n                            {getPlatformIcon()}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"p-4\">\n                        <h3 className=\"text-sm font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 line-clamp-2\">\n                          {reel.title}\n                        </h3>\n\n                        {reel.description && (\n                          <p className=\"text-muted-foreground text-xs leading-relaxed line-clamp-2\">\n                            {reel.description}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </DialogTrigger>\n\n                  <DialogContent className=\"max-w-lg w-[95vw] p-0 bg-black border-0\">\n                    <div className=\"relative aspect-[9/16] max-h-[85vh] w-full\">\n                      <DialogClose asChild>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2\"\n                        >\n                          <X size={24} />\n                        </Button>\n                      </DialogClose>\n\n                      {selectedReel === reel.id && reel.embedUrl && (\n                        <iframe\n                          src={reel.embedUrl}\n                          title={reel.title}\n                          className=\"w-full h-full rounded-lg\"\n                          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                          allowFullScreen\n                        />\n                      )}\n                    </div>\n                  </DialogContent>\n                </Dialog>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* View Controls */}\n        <div className=\"flex justify-between items-center mb-8\">\n          <div className=\"flex items-center gap-2\">\n            <Play size={20} className=\"text-red-600\" />\n            <span className=\"font-medium text-primary\">YouTube Shorts</span>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant={viewMode === 'grid' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setViewMode('grid')}\n            >\n              <Grid size={16} />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n            >\n              <List size={16} />\n            </Button>\n          </div>\n        </div>\n\n        {/* All Reels */}\n        <section>\n          <h2 className=\"text-3xl font-heading font-bold text-primary mb-8\">\n            All YouTube Shorts\n            <span className=\"text-lg font-normal text-muted-foreground ml-2\">\n              ({filteredReels.length} shorts)\n            </span>\n          </h2>\n\n          {filteredReels.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <p className=\"text-lg text-muted-foreground\">No YouTube Shorts available at the moment.</p>\n            </div>\n          ) : (\n            <div className={viewMode === 'grid'\n              ? \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6\"\n              : \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n            }>\n              {filteredReels.map((reel) => (\n                <Dialog\n                  key={reel._id}\n                  open={selectedReel === reel.id}\n                  onOpenChange={(open) => {\n                    if (!open) {\n                      setSelectedReel(null);\n                    }\n                  }}\n                >\n                  <DialogTrigger asChild>\n                    <div\n                      onClick={() => setSelectedReel(reel.id)}\n                      className={`group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer ${viewMode === 'list' ? 'flex' : ''\n                        }`}\n                    >\n                      <div className={`relative overflow-hidden ${viewMode === 'list' ? 'w-32 aspect-[9/16]' : 'aspect-[9/16]'\n                        }`}>\n                        <Image\n                          src={reel.thumbnail || ''}\n                          alt={reel.title}\n                          fill\n                          className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                        />\n\n                        <div className=\"absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center\">\n                          <div className=\"bg-card/90 backdrop-blur-sm rounded-full p-3 group-hover:scale-110 transition-transform duration-300\">\n                            <Play className=\"h-6 w-6 text-primary ml-0.5\" fill=\"currentColor\" />\n                          </div>\n                        </div>\n\n                        {reel.featured && (\n                          <div className=\"absolute top-3 left-3\">\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              Featured\n                            </Badge>\n                          </div>\n                        )}\n\n                        <div className=\"absolute top-3 right-3\">\n                          <div className={`${getPlatformColor()} text-primary-foreground p-1.5 rounded-full`}>\n                            {getPlatformIcon()}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"p-4 flex-1\">\n                        <h3 className={`font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 ${viewMode === 'list' ? 'text-base line-clamp-2' : 'text-sm line-clamp-2'\n                          }`}>\n                          {reel.title}\n                        </h3>\n\n                        {reel.description && (\n                          <p className={`text-muted-foreground leading-relaxed ${viewMode === 'list' ? 'text-sm line-clamp-3' : 'text-xs line-clamp-2'\n                            }`}>\n                            {reel.description}\n                          </p>\n                        )}\n\n                        {viewMode === 'list' && (\n                          <div className=\"mt-3\">\n                            <Badge\n                              variant=\"outline\"\n                              className={`text-xs ${getPlatformColor()} text-white border-none`}\n                            >\n                              <span className=\"flex items-center gap-1\">\n                                {getPlatformIcon()}\n                                YouTube Shorts\n                              </span>\n                            </Badge>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </DialogTrigger>\n\n                  <DialogContent className=\"max-w-lg w-[95vw] p-0 bg-black border-0\">\n                    <div className=\"relative aspect-[9/16] max-h-[85vh] w-full\">\n                      <DialogClose asChild>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2\"\n                        >\n                          <X size={24} />\n                        </Button>\n                      </DialogClose>\n\n                      {selectedReel === reel.id && reel.embedUrl && (\n                        <iframe\n                          src={reel.embedUrl}\n                          title={reel.title}\n                          className=\"w-full h-full rounded-lg\"\n                          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                          allowFullScreen\n                        />\n                      )}\n                    </div>\n                  </DialogContent>\n                </Dialog>\n              ))}\n            </div>\n          )}\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAgBe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAwB;;IACvF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,+DAA+D;IAE/D,oCAAoC;IACpC,MAAM,kBAAkB,kBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;IAC1C,MAAM,mBAAmB,IAAM;IAE/B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,MAAM;;;;;;gCAAM;;;;;;;sCAIzB,6LAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,6LAAC;4BAAE,WAAU;sCAAoD;;;;;;;;;;;;;;;;;0BAMrE,6LAAC;gBAAI,WAAU;;oBAEZ,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,qIAAA,CAAA,SAAM;wCAEL,MAAM,iBAAiB,KAAK,EAAE;wCAC9B,cAAc,CAAC;4CACb,IAAI,CAAC,MAAM;gDACT,gBAAgB;4CAClB;wCACF;;0DAEA,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,OAAO;0DACpB,cAAA,6LAAC;oDACC,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,KAAK,SAAS,IAAI;oEACvB,KAAK,KAAK,KAAK;oEACf,IAAI;oEACJ,WAAU;;;;;;8EAGZ,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;4EAAwC,MAAK;;;;;;;;;;;;;;;;8EAIjE,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAAuD;;;;;;;;;;;8EAK5F,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAW,GAAG,mBAAmB,2CAA2C,CAAC;kFAC/E;;;;;;;;;;;;;;;;;sEAKP,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,KAAK,KAAK;;;;;;gEAGZ,KAAK,WAAW,kBACf,6LAAC;oEAAE,WAAU;8EACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;0DAO3B,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,cAAW;4DAAC,OAAO;sEAClB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;0EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,MAAM;;;;;;;;;;;;;;;;wDAIZ,iBAAiB,KAAK,EAAE,IAAI,KAAK,QAAQ,kBACxC,6LAAC;4DACC,KAAK,KAAK,QAAQ;4DAClB,OAAO,KAAK,KAAK;4DACjB,WAAU;4DACV,OAAM;4DACN,eAAe;;;;;;;;;;;;;;;;;;uCAxElB,KAAK,GAAG;;;;;;;;;;;;;;;;kCAoFvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAG7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE3B,cAAA,6LAAC,4MAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;kDAEd,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,6LAAC;wCAAK,WAAU;;4CAAiD;4CAC7D,cAAc,MAAM;4CAAC;;;;;;;;;;;;;4BAI1B,cAAc,MAAM,KAAK,kBACxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;qDAG/C,6LAAC;gCAAI,WAAW,aAAa,SACzB,wEACA;0CAED,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,qIAAA,CAAA,SAAM;wCAEL,MAAM,iBAAiB,KAAK,EAAE;wCAC9B,cAAc,CAAC;4CACb,IAAI,CAAC,MAAM;gDACT,gBAAgB;4CAClB;wCACF;;0DAEA,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,OAAO;0DACpB,cAAA,6LAAC;oDACC,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,WAAW,CAAC,gIAAgI,EAAE,aAAa,SAAS,SAAS,IACzK;;sEAEJ,6LAAC;4DAAI,WAAW,CAAC,yBAAyB,EAAE,aAAa,SAAS,uBAAuB,iBACrF;;8EACF,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,KAAK,SAAS,IAAI;oEACvB,KAAK,KAAK,KAAK;oEACf,IAAI;oEACJ,WAAU;;;;;;8EAGZ,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;4EAA8B,MAAK;;;;;;;;;;;;;;;;gEAItD,KAAK,QAAQ,kBACZ,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;;;;;;8EAMnD,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAW,GAAG,mBAAmB,2CAA2C,CAAC;kFAC/E;;;;;;;;;;;;;;;;;sEAKP,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAW,CAAC,uGAAuG,EAAE,aAAa,SAAS,2BAA2B,wBACtK;8EACD,KAAK,KAAK;;;;;;gEAGZ,KAAK,WAAW,kBACf,6LAAC;oEAAE,WAAW,CAAC,sCAAsC,EAAE,aAAa,SAAS,yBAAyB,wBAClG;8EACD,KAAK,WAAW;;;;;;gEAIpB,aAAa,wBACZ,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAW,CAAC,QAAQ,EAAE,mBAAmB,uBAAuB,CAAC;kFAEjE,cAAA,6LAAC;4EAAK,WAAU;;gFACb;gFAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAUjC,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,cAAW;4DAAC,OAAO;sEAClB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;0EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,MAAM;;;;;;;;;;;;;;;;wDAIZ,iBAAiB,KAAK,EAAE,IAAI,KAAK,QAAQ,kBACxC,6LAAC;4DACC,KAAK,KAAK,QAAQ;4DAClB,OAAO,KAAK,KAAK;4DACjB,WAAU;4DACV,OAAM;4DACN,eAAe;;;;;;;;;;;;;;;;;;uCA5FlB,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyG/B;GAnRwB;KAAA", "debugId": null}}]}