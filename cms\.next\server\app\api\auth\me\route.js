(()=>{var e={};e.id=8673,e.ids=[8673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>a,HU:()=>l,b9:()=>u,oC:()=>m,rL:()=>d});var s=t(43205),n=t.n(s),o=t(85663);let i=process.env.JWT_SECRET;if(!i)throw Error("Please define the JWT_SECRET environment variable");let a=async e=>await o.Ay.hash(e,12),u=async(e,r)=>await o.Ay.compare(e,r),l=e=>n().sign(e,i,{expiresIn:"7d"}),c=e=>{try{return n().verify(e,i)}catch(e){return null}},p=e=>{let r=e.headers.get("authorization");return r&&r.startsWith("Bearer ")?r.substring(7):e.cookies.get("auth-token")?.value||null},d=async e=>{let r=p(e);return r?c(r):null},m=e=>async(r,t)=>{let s=await d(r);return s?(r.user=s,e(r,t)):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(56037),n=t.n(s);let o=new s.Schema({email:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},password:{type:String,required:!0,minlength:6},name:{type:String,required:!0,trim:!0},role:{type:String,enum:["admin","editor"],default:"editor"},isActive:{type:Boolean,default:!0},lastLogin:{type:Date}},{timestamps:!0});o.index({email:1}),o.index({role:1});let i=n().models.User||n().model("User",o)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(56037),n=t.n(s);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose||{conn:null,promise:null};global.mongoose||(global.mongoose=i);let a=async function(){if(i.conn)return i.conn;i.promise||(i.promise=n().connect(o,{bufferCommands:!1}).then(e=>(console.log("✅ Connected to MongoDB"),e)));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},94990:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(75745),l=t(17063),c=t(12909);async function p(e){try{await (0,u.A)();let r=await (0,c.rL)(e);if(!r)return a.NextResponse.json({error:"Authentication required"},{status:401});let t=await l.A.findById(r.userId).select("-password");if(!t)return a.NextResponse.json({error:"User not found"},{status:404});if(!t.isActive)return a.NextResponse.json({error:"Account is deactivated"},{status:401});return a.NextResponse.json({success:!0,user:{id:t._id,email:t.email,name:t.name,role:t.role,lastLogin:t.lastLogin,createdAt:t.createdAt}})}catch(e){return console.error("Get user error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\auth\\me\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:g}=d;function x(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315],()=>t(94990));module.exports=s})();