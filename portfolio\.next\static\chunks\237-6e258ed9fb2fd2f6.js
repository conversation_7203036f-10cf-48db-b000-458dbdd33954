"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[237],{237:(e,t,n)=>{n.d(t,{bm:()=>er,UC:()=>en,hJ:()=>et,ZL:()=>ee,bL:()=>J,l9:()=>Q});var r=n(2115),o=n(5185),a=n(6101),i=n(6081),u=n(1285),l=n(5845),c=n(9178),s=n(7900),d=n(4378),f=n(2712),v=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,a]=r.useState(),i=r.useRef(null),u=r.useRef(e),l=r.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=p(i.current);l.current="mounted"===c?e:"none"},[c]),(0,f.N)(()=>{let t=i.current,n=u.current;if(n!==e){let r=l.current,o=p(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),(0,f.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=p(i.current).includes(e.animationName);if(e.target===o&&r&&(s("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(l.current=p(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}s("ANIMATION_END")},[o,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{i.current=e?getComputedStyle(e):null,a(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),u=(0,a.s)(o.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:u}):null};function p(e){return(null==e?void 0:e.animationName)||"none"}v.displayName="Presence";var m=n(3655),h=n(2293),g=n(3795),y=n(8168),E=n(9708),b=n(5155),w="Dialog",[N,C]=(0,i.A)(w),[R,S]=N(w),x=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,v]=(0,l.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:w});return(0,b.jsx)(R,{scope:t,triggerRef:s,contentRef:d,contentId:(0,u.B)(),titleId:(0,u.B)(),descriptionId:(0,u.B)(),open:f,onOpenChange:v,onOpenToggle:r.useCallback(()=>v(e=>!e),[v]),modal:c,children:n})};x.displayName=w;var O="DialogTrigger",D=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=S(O,n),u=(0,a.s)(t,i.triggerRef);return(0,b.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":X(i.open),...r,ref:u,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});D.displayName=O;var T="DialogPortal",[L,P]=N(T,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,i=S(T,t);return(0,b.jsx)(L,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,b.jsx)(v,{present:n||i.open,children:(0,b.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};A.displayName=T;var M="DialogOverlay",I=r.forwardRef((e,t)=>{let n=P(M,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=S(M,e.__scopeDialog);return a.modal?(0,b.jsx)(v,{present:r||a.open,children:(0,b.jsx)(j,{...o,ref:t})}):null});I.displayName=M;var k=(0,E.TL)("DialogOverlay.RemoveScroll"),j=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(M,n);return(0,b.jsx)(g.A,{as:k,allowPinchZoom:!0,shards:[o.contentRef],children:(0,b.jsx)(m.sG.div,{"data-state":X(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),F="DialogContent",W=r.forwardRef((e,t)=>{let n=P(F,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=S(F,e.__scopeDialog);return(0,b.jsx)(v,{present:r||a.open,children:a.modal?(0,b.jsx)(_,{...o,ref:t}):(0,b.jsx)(B,{...o,ref:t})})});W.displayName=F;var _=r.forwardRef((e,t)=>{let n=S(F,e.__scopeDialog),i=r.useRef(null),u=(0,a.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,y.Eq)(e)},[]),(0,b.jsx)(U,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=r.forwardRef((e,t)=>{let n=S(F,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,b.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let u=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),U=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:u,...l}=e,d=S(F,n),f=r.useRef(null),v=(0,a.s)(t,f);return(0,h.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:u,children:(0,b.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":X(d.open),...l,ref:v,onDismiss:()=>d.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(H,{titleId:d.titleId}),(0,b.jsx)(V,{contentRef:f,descriptionId:d.descriptionId})]})]})}),$="DialogTitle";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S($,n);return(0,b.jsx)(m.sG.h2,{id:o.titleId,...r,ref:t})}).displayName=$;var q="DialogDescription";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(q,n);return(0,b.jsx)(m.sG.p,{id:o.descriptionId,...r,ref:t})}).displayName=q;var G="DialogClose",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=S(G,n);return(0,b.jsx)(m.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function X(e){return e?"open":"closed"}K.displayName=G;var Y="DialogTitleWarning",[Z,z]=(0,i.q)(Y,{contentName:F,titleName:$,docsSlug:"dialog"}),H=e=>{let{titleId:t}=e,n=z(Y),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},V=e=>{let{contentRef:t,descriptionId:n}=e,o=z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(a))},[a,t,n]),null},J=x,Q=D,ee=A,et=I,en=W,er=K},1285:(e,t,n)=>{n.d(t,{B:()=>l});var r,o=n(2115),a=n(2712),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function l(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2293:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(2115),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>u});var r=n(2115),o=n(7650),a=n(9708),i=n(5155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,a.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{n.d(t,{A:()=>X});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var u=("function"==typeof SuppressedError&&SuppressedError,n(2115)),l="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?u.useLayoutEffect:u.useEffect,f=new WeakMap;function v(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=v),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=a({async:!0,ssr:!1},e),i}(),m=function(){},h=u.forwardRef(function(e,t){var n,r,o,l,c=u.useRef(null),v=u.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=v[0],g=v[1],y=e.forwardProps,E=e.children,b=e.className,w=e.removeScrollBar,N=e.enabled,C=e.shards,R=e.sideCar,S=e.noRelative,x=e.noIsolation,O=e.inert,D=e.allowPinchZoom,T=e.as,L=e.gapMode,P=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),A=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,u.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,d(function(){var e=f.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(l,n)},[n]),l),M=a(a({},P),h);return u.createElement(u.Fragment,null,N&&u.createElement(R,{sideCar:p,removeScrollBar:w,shards:C,noRelative:S,noIsolation:x,inert:O,setCallbacks:g,allowPinchZoom:!!D,lockRef:c,gapMode:L}),y?u.cloneElement(u.Children.only(E),a(a({},M),{ref:A})):u.createElement(void 0===T?"div":T,a({},M,{className:b,ref:A}),E))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:c,zeroRight:l};var g=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return u.createElement(r,a({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=y();return function(t,n){u.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},N=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[N(n),N(r),N(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=b(),x="data-scroll-locked",O=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(x,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(x,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},D=function(){var e=parseInt(document.body.getAttribute(x)||"0",10);return isFinite(e)?e:0},T=function(){u.useEffect(function(){return document.body.setAttribute(x,(D()+1).toString()),function(){var e=D()-1;e<=0?document.body.removeAttribute(x):document.body.setAttribute(x,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var a=u.useMemo(function(){return R(o)},[o]);return u.createElement(S,{styles:O(a,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var A=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",A,A),window.removeEventListener("test",A,A)}catch(e){P=!1}var M=!!P&&{passive:!1},I=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},k=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),j(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},j=function(e,t){return"v"===e?I(t,"overflowY"):I(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,l=n.target,c=t.contains(l),s=!1,d=u>0,f=0,v=0;do{var p=F(e,l),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&j(e,l)&&(f+=h,v+=m),l=l.parentNode.host||l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},_=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},$=0,q=[];let G=(r=function(e){var t=u.useRef([]),n=u.useRef([0,0]),r=u.useRef(),o=u.useState($++)[0],a=u.useState(b)[0],i=u.useRef(e);u.useEffect(function(){i.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=u.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=_(e),u=n.current,l="deltaX"in e?e.deltaX:u[0]-a[0],c="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=k(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=k(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var v=r.current||o;return W(v,t,e,"h"===v?l:c,!0)},[]),c=u.useCallback(function(e){if(q.length&&q[q.length-1]===a){var n="deltaY"in e?B(e):_(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=u.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=u.useCallback(function(e){n.current=_(e),r.current=void 0},[]),f=u.useCallback(function(t){s(t.type,B(t),t.target,l(t,e.lockRef.current))},[]),v=u.useCallback(function(t){s(t.type,_(t),t.target,l(t,e.lockRef.current))},[]);u.useEffect(function(){return q.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",c,M),document.addEventListener("touchmove",c,M),document.addEventListener("touchstart",d,M),function(){q=q.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,M),document.removeEventListener("touchmove",c,M),document.removeEventListener("touchstart",d,M)}},[]);var p=e.removeScrollBar,m=e.inert;return u.createElement(u.Fragment,null,m?u.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?u.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(r),g);var K=u.forwardRef(function(e,t){return u.createElement(h,a({},e,{ref:t,sideCar:G}))});K.classNames=h.classNames;let X=K},4378:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(2115),o=n(7650),a=n(3655),i=n(2712),u=n(5155),l=r.forwardRef((e,t)=>{var n,l;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,i.N)(()=>f(!0),[]);let v=c||d&&(null==(l=globalThis)||null==(n=l.document)?void 0:n.body);return v?o.createPortal((0,u.jsx)(a.sG.div,{...s,ref:t}),v):null});l.displayName="Portal"},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>u});var r,o=n(2115),a=n(2712),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[a,u,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),a=o.useRef(n),u=o.useRef(t);return i(()=>{u.current=t},[t]),o.useEffect(()=>{a.current!==n&&(u.current?.(n),a.current=n)},[n,a]),[n,r,u]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else u(t)},[c,e,u,l])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>a});var r=n(2115),o=n(5155);function a(e,t){let n=r.createContext(t),a=e=>{let{children:t,...a}=e,i=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),u=n.length;n=[...n,a];let l=t=>{let{scope:n,children:a,...l}=t,c=n?.[e]?.[u]||i,s=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:s,children:a})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[u]||i,c=r.useContext(l);if(c)return c;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),a=n(3655),i=n(9033),u=n(5155),l="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:h,onUnmountAutoFocus:g,...y}=e,[E,b]=r.useState(null),w=(0,i.c)(h),N=(0,i.c)(g),C=r.useRef(null),R=(0,o.s)(t,e=>b(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(S.paused||!E)return;let t=e.target;E.contains(t)?C.current=t:p(C.current,{select:!0})},t=function(e){if(S.paused||!E)return;let t=e.relatedTarget;null!==t&&(E.contains(t)||p(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(E)});return E&&n.observe(E,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,E,S.paused]),r.useEffect(()=>{if(E){m.add(S);let e=document.activeElement;if(!E.contains(e)){let t=new CustomEvent(l,s);E.addEventListener(l,w),E.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(p(r,{select:t}),document.activeElement!==n)return}(f(E).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(E))}return()=>{E.removeEventListener(l,w),setTimeout(()=>{let t=new CustomEvent(c,s);E.addEventListener(c,N),E.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),E.removeEventListener(c,N),m.remove(S)},0)}}},[E,w,N,S]);let x=r.useCallback(e=>{if(!n&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[v(t,e),v(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&p(a,{select:!0})):(e.preventDefault(),n&&p(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,S.paused]);return(0,u.jsx)(a.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:x})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=h(e,t)).unshift(t)},remove(t){var n;null==(n=(e=h(e,t))[0])||n.resume()}}}();function h(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},u=0,l=function(e){return e&&(e.host||l(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,v=new Set(c),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};c.forEach(p);var m=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,u=(o.get(e)||0)+1,l=(s.get(e)||0)+1;o.set(e,u),s.set(e,l),d.push(e),1===u&&i&&a.set(e,!0),1===l&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),u++,function(){d.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),i||e.removeAttribute(n)}),--u||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),c(o,a,n,"aria-hidden")):function(){return null}}},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(2115),a=n(5185),i=n(3655),u=n(6101),l=n(9033),c=n(5155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:E,onDismiss:b,...w}=e,N=o.useContext(d),[C,R]=o.useState(null),S=null!=(f=null==C?void 0:C.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,x]=o.useState({}),O=(0,u.s)(t,e=>R(e)),D=Array.from(N.layers),[T]=[...N.layersWithOutsidePointerEventsDisabled].slice(-1),L=D.indexOf(T),P=C?D.indexOf(C):-1,A=N.layersWithOutsidePointerEventsDisabled.size>0,M=P>=L,I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...N.branches].some(e=>e.contains(t));M&&!n&&(null==g||g(e),null==E||E(e),e.defaultPrevented||null==b||b())},S),k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...N.branches].some(e=>e.contains(t))&&(null==y||y(e),null==E||E(e),e.defaultPrevented||null==b||b())},S);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===N.layers.size-1&&(null==h||h(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},S),o.useEffect(()=>{if(C)return m&&(0===N.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),N.layersWithOutsidePointerEventsDisabled.add(C)),N.layers.add(C),v(),()=>{m&&1===N.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[C,S,m,N]),o.useEffect(()=>()=>{C&&(N.layers.delete(C),N.layersWithOutsidePointerEventsDisabled.delete(C),v())},[C,N]),o.useEffect(()=>{let e=()=>x({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(i.sG.div,{...w,ref:O,style:{pointerEvents:A?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function v(){let e=new CustomEvent(s);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,u):a.dispatchEvent(u)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"}}]);