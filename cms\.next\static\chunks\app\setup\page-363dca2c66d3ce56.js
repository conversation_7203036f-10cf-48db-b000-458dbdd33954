(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[620],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var r=s(5155);s(2115);var a=s(9708),n=s(2085),i=s(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,l=o?a.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:n,className:t})),...c})}},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},6630:(e,t,s)=>{Promise.resolve().then(s.bind(s,7137))},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var r=s(5155);s(2115);var a=s(9434);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}},7137:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(5155),a=s(2115),n=s(285),i=s(6695),d=s(9946);let o=(0,d.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var c=s(1007);let l=(0,d.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var u=s(1154);let h=(0,d.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),x=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);function m(){let[e,t]=(0,a.useState)([{id:"database",title:"Database Connection",description:"Connect to MongoDB database",status:"pending",icon:o},{id:"admin",title:"Admin User",description:"Create admin user account",status:"pending",icon:c.A},{id:"auth",title:"Authentication",description:"Verify authentication system",status:"pending",icon:l}]),[s,d]=(0,a.useState)(!1),[m,p]=(0,a.useState)(!1),g=(e,s,r)=>{t(t=>t.map(t=>t.id===e?{...t,status:s,error:r}:t))},b=async()=>{d(!0),p(!1);try{g("database","running"),g("admin","running");let e=await fetch("/api/init",{method:"POST"}),t=await e.json();e.ok?(g("database","success"),g("admin","success"),g("auth","running"),(await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t.credentials.email,password:t.credentials.password})})).ok?(g("auth","success"),p(!0)):g("auth","error","Authentication test failed")):(g("database","error",t.error||"Database connection failed"),g("admin","error","Failed to create admin user"),g("auth","error","Cannot test authentication"))}catch(e){console.error("Setup error:",e),g("database","error","Network error or MongoDB not running"),g("admin","error","Setup failed"),g("auth","error","Setup failed")}finally{d(!1)}},v=e=>{switch(e){case"running":return(0,r.jsx)(u.A,{className:"h-5 w-5 animate-spin text-blue-500"});case"success":return(0,r.jsx)(h,{className:"h-5 w-5 text-green-500"});case"error":return(0,r.jsx)(x,{className:"h-5 w-5 text-red-500"});default:return(0,r.jsx)("div",{className:"h-5 w-5 rounded-full border-2 border-gray-300"})}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,r.jsxs)(i.Zp,{className:"w-full max-w-2xl",children:[(0,r.jsxs)(i.aR,{className:"text-center",children:[(0,r.jsx)(i.ZB,{className:"text-3xl font-bold",children:"Portfolio CMS Setup"}),(0,r.jsx)(i.BT,{children:"Initialize your content management system"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Prerequisites"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• MongoDB should be running on localhost:27017"}),(0,r.jsx)("li",{children:"• Make sure your .env.local file is configured"}),(0,r.jsx)("li",{children:"• Default admin credentials: <EMAIL> / admin123"})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:v(e.status)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),e.error&&(0,r.jsx)("p",{className:"text-sm text-red-600 mt-1",children:e.error})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(e.icon,{className:"h-6 w-6 text-gray-400"})})]},e.id))}),(0,r.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,r.jsx)(n.$,{onClick:b,disabled:s,className:"px-8",children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Setting up..."]}):"Start Setup"}),m&&(0,r.jsx)(n.$,{variant:"outline",onClick:()=>window.location.href="/login",children:"Go to Login"})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"MongoDB Setup"}),(0,r.jsxs)("div",{className:"text-sm text-yellow-800 space-y-2",children:[(0,r.jsx)("p",{children:"If you don't have MongoDB running, here are quick setup options:"}),(0,r.jsxs)("div",{className:"bg-yellow-100 p-3 rounded font-mono text-xs",children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Option 1 - Local MongoDB:"})}),(0,r.jsx)("p",{children:"1. Download from: https://www.mongodb.com/try/download/community"}),(0,r.jsx)("p",{children:"2. Install and start the service"}),(0,r.jsx)("p",{children:"3. Default connection: mongodb://localhost:27017"})]}),(0,r.jsxs)("div",{className:"bg-yellow-100 p-3 rounded font-mono text-xs",children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Option 2 - MongoDB Atlas (Cloud):"})}),(0,r.jsx)("p",{children:"1. Create free account at: https://cloud.mongodb.com"}),(0,r.jsx)("p",{children:"2. Create cluster and get connection string"}),(0,r.jsx)("p",{children:"3. Update MONGODB_URI in .env.local"})]})]})]}),m&&(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 text-center",children:[(0,r.jsx)(h,{className:"h-8 w-8 text-green-500 mx-auto mb-2"}),(0,r.jsx)("h3",{className:"font-semibold text-green-900",children:"Setup Complete!"}),(0,r.jsx)("p",{className:"text-sm text-green-800 mt-1",children:"Your CMS is ready to use. You can now login with the admin credentials."})]})]})]})})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(2596),a=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,8441,1684,7358],()=>t(6630)),_N_E=e.O()}]);