"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[239],{156:(t,e,n)=>{function r(t){this.content=t}n.d(e,{S4:()=>j,ZF:()=>G,FK:()=>i,CU:()=>a,sX:()=>P,bP:()=>S,u$:()=>b,vI:()=>h,Sj:()=>H,Ji:()=>c}),r.prototype={constructor:r,find:function(t){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===t)return e;return -1},get:function(t){var e=this.find(t);return -1==e?void 0:this.content[e+1]},update:function(t,e,n){var i=n&&n!=t?this.remove(n):this,s=i.find(t),o=i.content.slice();return -1==s?o.push(n||t,e):(o[s+1]=e,n&&(o[s]=n)),new r(o)},remove:function(t){var e=this.find(t);if(-1==e)return this;var n=this.content.slice();return n.splice(e,2),new r(n)},addToStart:function(t,e){return new r([t,e].concat(this.remove(t).content))},addToEnd:function(t,e){var n=this.remove(t).content.slice();return n.push(t,e),new r(n)},addBefore:function(t,e,n){var i=this.remove(e),s=i.content.slice(),o=i.find(t);return s.splice(-1==o?s.length:o,0,e,n),new r(s)},forEach:function(t){for(var e=0;e<this.content.length;e+=2)t(this.content[e],this.content[e+1])},prepend:function(t){return(t=r.from(t)).size?new r(t.content.concat(this.subtract(t).content)):this},append:function(t){return(t=r.from(t)).size?new r(this.subtract(t).content.concat(t.content)):this},subtract:function(t){var e=this;t=r.from(t);for(var n=0;n<t.content.length;n+=2)e=e.remove(t.content[n]);return e},toObject:function(){var t={};return this.forEach(function(e,n){t[e]=n}),t},get size(){return this.content.length>>1}},r.from=function(t){if(t instanceof r)return t;var e=[];if(t)for(var n in t)e.push(n,t[n]);return new r(e)};class i{constructor(t,e){if(this.content=t,this.size=e||0,null==e)for(let e=0;e<t.length;e++)this.size+=t[e].nodeSize}nodesBetween(t,e,n,r=0,i){for(let s=0,o=0;o<e;s++){let l=this.content[s],a=o+l.nodeSize;if(a>t&&!1!==n(l,r+o,i||null,s)&&l.content.size){let i=o+1;l.nodesBetween(Math.max(0,t-i),Math.min(l.content.size,e-i),n,r+i)}o=a}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,n,r){let i="",s=!0;return this.nodesBetween(t,e,(o,l)=>{let a=o.isText?o.text.slice(Math.max(t,l)-l,e-l):o.isLeaf?r?"function"==typeof r?r(o):r:o.type.spec.leafText?o.type.spec.leafText(o):"":"";o.isBlock&&(o.isLeaf&&a||o.isTextblock)&&n&&(s?s=!1:i+=n),i+=a},0),i}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,n=t.firstChild,r=this.content.slice(),s=0;for(e.isText&&e.sameMarkup(n)&&(r[r.length-1]=e.withText(e.text+n.text),s=1);s<t.content.length;s++)r.push(t.content[s]);return new i(r,this.size+t.size)}cut(t,e=this.size){if(0==t&&e==this.size)return this;let n=[],r=0;if(e>t)for(let i=0,s=0;s<e;i++){let o=this.content[i],l=s+o.nodeSize;l>t&&((s<t||l>e)&&(o=o.isText?o.cut(Math.max(0,t-s),Math.min(o.text.length,e-s)):o.cut(Math.max(0,t-s-1),Math.min(o.content.size,e-s-1))),n.push(o),r+=o.nodeSize),s=l}return new i(n,r)}cutByIndex(t,e){return t==e?i.empty:0==t&&e==this.content.length?this:new i(this.content.slice(t,e))}replaceChild(t,e){let n=this.content[t];if(n==e)return this;let r=this.content.slice(),s=this.size+e.nodeSize-n.nodeSize;return r[t]=e,new i(r,s)}addToStart(t){return new i([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new i(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let e=0;e<this.content.length;e++)if(!this.content[e].eq(t.content[e]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let e=0,n=0;e<this.content.length;e++){let r=this.content[e];t(r,n,e),n+=r.nodeSize}}findDiffStart(t,e=0){return function t(e,n,r){for(let i=0;;i++){if(i==e.childCount||i==n.childCount)return e.childCount==n.childCount?null:r;let s=e.child(i),o=n.child(i);if(s==o){r+=s.nodeSize;continue}if(!s.sameMarkup(o))return r;if(s.isText&&s.text!=o.text){for(let t=0;s.text[t]==o.text[t];t++)r++;return r}if(s.content.size||o.content.size){let e=t(s.content,o.content,r+1);if(null!=e)return e}r+=s.nodeSize}}(this,t,e)}findDiffEnd(t,e=this.size,n=t.size){return function t(e,n,r,i){for(let s=e.childCount,o=n.childCount;;){if(0==s||0==o)return s==o?null:{a:r,b:i};let l=e.child(--s),a=n.child(--o),h=l.nodeSize;if(l==a){r-=h,i-=h;continue}if(!l.sameMarkup(a))return{a:r,b:i};if(l.isText&&l.text!=a.text){let t=0,e=Math.min(l.text.length,a.text.length);for(;t<e&&l.text[l.text.length-t-1]==a.text[a.text.length-t-1];)t++,r--,i--;return{a:r,b:i}}if(l.content.size||a.content.size){let e=t(l.content,a.content,r-1,i-1);if(e)return e}r-=h,i-=h}}(this,t,e,n)}findIndex(t,e=-1){if(0==t)return o(0,t);if(t==this.size)return o(this.content.length,t);if(t>this.size||t<0)throw RangeError(`Position ${t} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=t){if(i==t||e>0)return o(n+1,i);return o(n,r)}r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(t=>t.toJSON()):null}static fromJSON(t,e){if(!e)return i.empty;if(!Array.isArray(e))throw RangeError("Invalid input for Fragment.fromJSON");return new i(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return i.empty;let e,n=0;for(let r=0;r<t.length;r++){let i=t[r];n+=i.nodeSize,r&&i.isText&&t[r-1].sameMarkup(i)?(e||(e=t.slice(0,r)),e[e.length-1]=i.withText(e[e.length-1].text+i.text)):e&&e.push(i)}return new i(e||t,n)}static from(t){if(!t)return i.empty;if(t instanceof i)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new i([t],t.nodeSize);throw RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}i.empty=new i([],0);let s={index:0,offset:0};function o(t,e){return s.index=t,s.offset=e,s}function l(t,e){if(t===e)return!0;if(!(t&&"object"==typeof t)||!(e&&"object"==typeof e))return!1;let n=Array.isArray(t);if(Array.isArray(e)!=n)return!1;if(n){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!l(t[n],e[n]))return!1}else{for(let n in t)if(!(n in e)||!l(t[n],e[n]))return!1;for(let n in e)if(!(n in t))return!1}return!0}class a{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,n=!1;for(let r=0;r<t.length;r++){let i=t[r];if(this.eq(i))return t;if(this.type.excludes(i.type))e||(e=t.slice(0,r));else{if(i.type.excludes(this.type))return t;!n&&i.type.rank>this.type.rank&&(e||(e=t.slice(0,r)),e.push(this),n=!0),e&&e.push(i)}}return e||(e=t.slice()),n||e.push(this),e}removeFromSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return t.slice(0,e).concat(t.slice(e+1));return t}isInSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return!0;return!1}eq(t){return this==t||this.type==t.type&&l(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw RangeError("Invalid input for Mark.fromJSON");let n=t.marks[e.type];if(!n)throw RangeError(`There is no mark type ${e.type} in this schema`);let r=n.create(e.attrs);return n.checkAttrs(r.attrs),r}static sameSet(t,e){if(t==e)return!0;if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].eq(e[n]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return a.none;if(t instanceof a)return[t];let e=t.slice();return e.sort((t,e)=>t.type.rank-e.type.rank),e}}a.none=[];class h extends Error{}class c{constructor(t,e,n){this.content=t,this.openStart=e,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let n=function t(e,n,r,i){let{index:s,offset:o}=e.findIndex(n),l=e.maybeChild(s);if(o==n||l.isText)return i&&!i.canReplace(s,s,r)?null:e.cut(0,n).append(r).append(e.cut(n));let a=t(l.content,n-o-1,r);return a&&e.replaceChild(s,l.copy(a))}(this.content,t+this.openStart,e);return n&&new c(n,this.openStart,this.openEnd)}removeBetween(t,e){return new c(function t(e,n,r){let{index:i,offset:s}=e.findIndex(n),o=e.maybeChild(i),{index:l,offset:a}=e.findIndex(r);if(s==n||o.isText){if(a!=r&&!e.child(l).isText)throw RangeError("Removing non-flat range");return e.cut(0,n).append(e.cut(r))}if(i!=l)throw RangeError("Removing non-flat range");return e.replaceChild(i,o.copy(t(o.content,n-s-1,r-s-1)))}(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return c.empty;let n=e.openStart||0,r=e.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new c(i.fromJSON(t,e.content),n,r)}static maxOpen(t,e=!0){let n=0,r=0;for(let r=t.firstChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=t.lastChild;n&&!n.isLeaf&&(e||!n.type.spec.isolating);n=n.lastChild)r++;return new c(t,n,r)}}function p(t,e){if(!e.type.compatibleContent(t.type))throw new h("Cannot join "+e.type.name+" onto "+t.type.name)}function u(t,e,n){let r=t.node(n);return p(r,e.node(n)),r}function d(t,e){let n=e.length-1;n>=0&&t.isText&&t.sameMarkup(e[n])?e[n]=t.withText(e[n].text+t.text):e.push(t)}function f(t,e,n,r){let i=(e||t).node(n),s=0,o=e?e.index(n):i.childCount;t&&(s=t.index(n),t.depth>n?s++:t.textOffset&&(d(t.nodeAfter,r),s++));for(let t=s;t<o;t++)d(i.child(t),r);e&&e.depth==n&&e.textOffset&&d(e.nodeBefore,r)}function m(t,e){return t.type.checkContent(e),t.copy(e)}function g(t,e,n){let r=[];return f(null,t,n,r),t.depth>n&&d(m(u(t,e,n+1),g(t,e,n+1)),r),f(e,null,n,r),new i(r)}c.empty=new c(i.empty,0,0);class y{constructor(t,e,n){this.pos=t,this.path=e,this.parentOffset=n,this.depth=e.length/3-1}resolveDepth(t){return null==t?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[3*this.resolveDepth(t)]}index(t){return this.path[3*this.resolveDepth(t)+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t!=this.depth||this.textOffset?1:0)}start(t){return 0==(t=this.resolveDepth(t))?0:this.path[3*t-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(!(t=this.resolveDepth(t)))throw RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]}after(t){if(!(t=this.resolveDepth(t)))throw RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]+this.path[3*t].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=t.child(e);return n?t.child(e).cut(n):r}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):0==t?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let n=this.path[3*e],r=0==e?0:this.path[3*e-1]+1;for(let e=0;e<t;e++)r+=n.child(e).nodeSize;return r}marks(){let t=this.parent,e=this.index();if(0==t.content.size)return a.none;if(this.textOffset)return t.child(e).marks;let n=t.maybeChild(e-1),r=t.maybeChild(e);if(!n){let t=n;n=r,r=t}let i=n.marks;for(var s=0;s<i.length;s++)!1!==i[s].type.spec.inclusive||r&&i[s].isInSet(r.marks)||(i=i[s--].removeFromSet(i));return i}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let n=e.marks,r=t.parent.maybeChild(t.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);n>=0;n--)if(t.pos<=this.end(n)&&(!e||e(this.node(n))))return new b(this,t,n);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let e=1;e<=this.depth;e++)t+=(t?"/":"")+this.node(e).type.name+"_"+this.index(e-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw RangeError("Position "+e+" out of range");let n=[],r=0,i=e;for(let e=t;;){let{index:t,offset:s}=e.content.findIndex(i),o=i-s;if(n.push(e,t,r+s),!o||(e=e.child(t)).isText)break;i=o-1,r+=s+1}return new y(e,n,i)}static resolveCached(t,e){let n=w.get(t);if(n)for(let t=0;t<n.elts.length;t++){let r=n.elts[t];if(r.pos==e)return r}else w.set(t,n=new k);let r=n.elts[n.i]=y.resolve(t,e);return n.i=(n.i+1)%v,r}}class k{constructor(){this.elts=[],this.i=0}}let v=12,w=new WeakMap;class b{constructor(t,e,n){this.$from=t,this.$to=e,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let x=Object.create(null);class S{constructor(t,e,n,r=a.none){this.type=t,this.attrs=e,this.marks=r,this.content=n||i.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,n,r=0){this.content.nodesBetween(t,e,n,r,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,n,r){return this.content.textBetween(t,e,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,n){return this.type==t&&l(this.attrs,e||t.defaultAttrs||x)&&a.sameSet(this.marks,n||a.none)}copy(t=null){return t==this.content?this:new S(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new S(this.type,this.attrs,this.content,t)}cut(t,e=this.content.size){return 0==t&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,n=!1){if(t==e)return c.empty;let r=this.resolve(t),i=this.resolve(e),s=n?0:r.sharedDepth(e),o=r.start(s);return new c(r.node(s).content.cut(r.pos-o,i.pos-o),r.depth-s,i.depth-s)}replace(t,e,n){var r=this.resolve(t),s=this.resolve(e);if(n.openStart>r.depth)throw new h("Inserted content deeper than insertion position");if(r.depth-n.openStart!=s.depth-n.openEnd)throw new h("Inconsistent open depths");return function t(e,n,r,s){let o=e.index(s),l=e.node(s);if(o==n.index(s)&&s<e.depth-r.openStart){let i=t(e,n,r,s+1);return l.copy(l.content.replaceChild(o,i))}if(!r.content.size)return m(l,g(e,n,s));if(r.openStart||r.openEnd||e.depth!=s||n.depth!=s){let{start:t,end:o}=function(t,e){let n=e.depth-t.openStart,r=e.node(n).copy(t.content);for(let t=n-1;t>=0;t--)r=e.node(t).copy(i.from(r));return{start:r.resolveNoCache(t.openStart+n),end:r.resolveNoCache(r.content.size-t.openEnd-n)}}(r,e);return m(l,function t(e,n,r,s,o){let l=e.depth>o&&u(e,n,o+1),a=s.depth>o&&u(r,s,o+1),h=[];return f(null,e,o,h),l&&a&&n.index(o)==r.index(o)?(p(l,a),d(m(l,t(e,n,r,s,o+1)),h)):(l&&d(m(l,g(e,n,o+1)),h),f(n,r,o,h),a&&d(m(a,g(r,s,o+1)),h)),f(s,null,o,h),new i(h)}(e,t,o,n,s))}{let t=e.parent,i=t.content;return m(t,i.cut(0,e.parentOffset).append(r.content).append(i.cut(n.parentOffset)))}}(r,s,n,0)}nodeAt(t){for(let e=this;;){let{index:n,offset:r}=e.content.findIndex(t);if(!(e=e.maybeChild(n)))return null;if(r==t||e.isText)return e;t-=r+1}}childAfter(t){let{index:e,offset:n}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:n}}childBefore(t){if(0==t)return{node:null,index:0,offset:0};let{index:e,offset:n}=this.content.findIndex(t);if(n<t)return{node:this.content.child(e),index:e,offset:n};let r=this.content.child(e-1);return{node:r,index:e-1,offset:n-r.nodeSize}}resolve(t){return y.resolveCached(this,t)}resolveNoCache(t){return y.resolve(this,t)}rangeHasMark(t,e,n){let r=!1;return e>t&&this.nodesBetween(t,e,t=>(n.isInSet(t.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),A(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,n=i.empty,r=0,s=n.childCount){let o=this.contentMatchAt(t).matchFragment(n,r,s),l=o&&o.matchFragment(this.content,e);if(!l||!l.validEnd)return!1;for(let t=r;t<s;t++)if(!this.type.allowsMarks(n.child(t).marks))return!1;return!0}canReplaceWith(t,e,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(t).matchType(n),s=i&&i.matchFragment(this.content,e);return!!s&&s.validEnd}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let t=a.none;for(let e=0;e<this.marks.length;e++){let n=this.marks[e];n.type.checkAttrs(n.attrs),t=n.addToSet(t)}if(!a.sameSet(t,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map(t=>t.toJSON())),t}static fromJSON(t,e){let n;if(!e)throw RangeError("Invalid input for Node.fromJSON");if(e.marks){if(!Array.isArray(e.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=e.marks.map(t.markFromJSON)}if("text"==e.type){if("string"!=typeof e.text)throw RangeError("Invalid text node in JSON");return t.text(e.text,n)}let r=i.fromJSON(t,e.content),s=t.nodeType(e.type).create(e.attrs,r,n);return s.type.checkAttrs(s.attrs),s}}S.prototype.text=void 0;class M extends S{constructor(t,e,n,r){if(super(t,e,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):A(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new M(this.type,this.attrs,this.text,t)}withText(t){return t==this.text?this:new M(this.type,this.attrs,t,this.marks)}cut(t=0,e=this.text.length){return 0==t&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let t=super.toJSON();return t.text=this.text,t}}function A(t,e){for(let n=t.length-1;n>=0;n--)e=t[n].type.name+"("+e+")";return e}class C{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){var n;let r,i=new T(t,e);if(null==i.next)return C.empty;let s=function t(e){let n=[];do n.push(function(e){let n=[];do n.push(function(e){let n=function(e){if(e.eat("(")){let n=t(e);return e.eat(")")||e.err("Missing closing paren"),n}if(/\W/.test(e.next))e.err("Unexpected token '"+e.next+"'");else{let t=(function(t,e){let n=t.nodeTypes,r=n[e];if(r)return[r];let i=[];for(let t in n){let r=n[t];r.isInGroup(e)&&i.push(r)}return 0==i.length&&t.err("No node type or group '"+e+"' found"),i})(e,e.next).map(t=>(null==e.inline?e.inline=t.isInline:e.inline!=t.isInline&&e.err("Mixing inline and block content"),{type:"name",value:t}));return e.pos++,1==t.length?t[0]:{type:"choice",exprs:t}}}(e);for(;;)if(e.eat("+"))n={type:"plus",expr:n};else if(e.eat("*"))n={type:"star",expr:n};else if(e.eat("?"))n={type:"opt",expr:n};else if(e.eat("{"))n=function(t,e){let n=E(t),r=n;return t.eat(",")&&(r="}"!=t.next?E(t):-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:e}}(e,n);else break;return n}(e));while(e.next&&")"!=e.next&&"|"!=e.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(e));while(e.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(i);i.next&&i.err("Unexpected trailing text");let o=(n=function(t){let e=[[]];return i(function t(e,s){if("choice"==e.type)return e.exprs.reduce((e,n)=>e.concat(t(n,s)),[]);if("seq"==e.type)for(let r=0;;r++){let o=t(e.exprs[r],s);if(r==e.exprs.length-1)return o;i(o,s=n())}else if("star"==e.type){let o=n();return r(s,o),i(t(e.expr,o),o),[r(o)]}else if("plus"==e.type){let o=n();return i(t(e.expr,s),o),i(t(e.expr,o),o),[r(o)]}else if("opt"==e.type)return[r(s)].concat(t(e.expr,s));else if("range"==e.type){let o=s;for(let r=0;r<e.min;r++){let r=n();i(t(e.expr,o),r),o=r}if(-1==e.max)i(t(e.expr,o),o);else for(let s=e.min;s<e.max;s++){let s=n();r(o,s),i(t(e.expr,o),s),o=s}return[r(o)]}else if("name"==e.type)return[r(s,void 0,e.value)];else throw Error("Unknown expr type")}(t,0),n()),e;function n(){return e.push([])-1}function r(t,n,r){let i={term:r,to:n};return e[t].push(i),i}function i(t,e){t.forEach(t=>t.to=e)}}(s),r=Object.create(null),function t(e){let i=[];e.forEach(t=>{n[t].forEach(({term:t,to:e})=>{let r;if(t){for(let e=0;e<i.length;e++)i[e][0]==t&&(r=i[e][1]);R(n,e).forEach(e=>{r||i.push([t,r=[]]),-1==r.indexOf(e)&&r.push(e)})}})});let s=r[e.join(",")]=new C(e.indexOf(n.length-1)>-1);for(let e=0;e<i.length;e++){let n=i[e][1].sort(O);s.next.push({type:i[e][0],next:r[n.join(",")]||t(n)})}return s}(R(n,0)));return function(t,e){for(let n=0,r=[t];n<r.length;n++){let t=r[n],i=!t.validEnd,s=[];for(let e=0;e<t.next.length;e++){let{type:n,next:o}=t.next[e];s.push(n.name),i&&!(n.isText||n.hasRequiredAttrs())&&(i=!1),-1==r.indexOf(o)&&r.push(o)}i&&e.err("Only non-generatable nodes ("+s.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(o,i),o}matchType(t){for(let e=0;e<this.next.length;e++)if(this.next[e].type==t)return this.next[e].next;return null}matchFragment(t,e=0,n=t.childCount){let r=this;for(let i=e;r&&i<n;i++)r=r.matchType(t.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let t=0;t<this.next.length;t++){let{type:e}=this.next[t];if(!(e.isText||e.hasRequiredAttrs()))return e}return null}compatible(t){for(let e=0;e<this.next.length;e++)for(let n=0;n<t.next.length;n++)if(this.next[e].type==t.next[n].type)return!0;return!1}fillBefore(t,e=!1,n=0){let r=[this];return function s(o,l){let a=o.matchFragment(t,n);if(a&&(!e||a.validEnd))return i.from(l.map(t=>t.createAndFill()));for(let t=0;t<o.next.length;t++){let{type:e,next:n}=o.next[t];if(!(e.isText||e.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let t=s(n,l.concat(e));if(t)return t}}return null}(this,[])}findWrapping(t){for(let e=0;e<this.wrapCache.length;e+=2)if(this.wrapCache[e]==t)return this.wrapCache[e+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(t)){let t=[];for(let e=r;e.type;e=e.via)t.push(e.type);return t.reverse()}for(let t=0;t<i.next.length;t++){let{type:s,next:o}=i.next[t];s.isLeaf||s.hasRequiredAttrs()||s.name in e||r.type&&!o.validEnd||(n.push({match:s.contentMatch,type:s,via:r}),e[s.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];return!function e(n){t.push(n);for(let r=0;r<n.next.length;r++)-1==t.indexOf(n.next[r].next)&&e(n.next[r].next)}(this),t.map((e,n)=>{let r=n+(e.validEnd?"*":" ")+" ";for(let n=0;n<e.next.length;n++)r+=(n?", ":"")+e.next[n].type.name+"->"+t.indexOf(e.next[n].next);return r}).join("\n")}}C.empty=new C(!0);class T{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw SyntaxError(t+" (in content expression '"+this.string+"')")}}function E(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function O(t,e){return e-t}function R(t,e){let n=[];return function e(r){let i=t[r];if(1==i.length&&!i[0].term)return e(i[0].to);n.push(r);for(let t=0;t<i.length;t++){let{term:r,to:s}=i[t];r||-1!=n.indexOf(s)||e(s)}}(e),n.sort(O)}function I(t){let e=Object.create(null);for(let n in t){let r=t[n];if(!r.hasDefault)return null;e[n]=r.default}return e}function N(t,e){let n=Object.create(null);for(let r in t){let i=e&&e[r];if(void 0===i){let e=t[r];if(e.hasDefault)i=e.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=i}return n}function L(t,e,n,r){for(let r in e)if(!(r in t))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in t){let r=t[n];r.validate&&r.validate(e[n])}}function z(t,e){let n=Object.create(null);if(e)for(let r in e)n[r]=new D(t,r,e[r]);return n}class F{constructor(t,e,n){this.name=t,this.schema=e,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=z(t,n.attrs),this.defaultAttrs=I(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==t),this.isText="text"==t}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==C.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(t){return this.groups.indexOf(t)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:N(this.attrs,t)}create(t=null,e,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new S(this,this.computeAttrs(t),i.from(e),a.setFrom(n))}createChecked(t=null,e,n){return e=i.from(e),this.checkContent(e),new S(this,this.computeAttrs(t),e,a.setFrom(n))}createAndFill(t=null,e,n){if(t=this.computeAttrs(t),(e=i.from(e)).size){let t=this.contentMatch.fillBefore(e);if(!t)return null;e=t.append(e)}let r=this.contentMatch.matchFragment(e),s=r&&r.fillBefore(i.empty,!0);return s?new S(this,t,e.append(s),a.setFrom(n)):null}validContent(t){let e=this.contentMatch.matchFragment(t);if(!e||!e.validEnd)return!1;for(let e=0;e<t.childCount;e++)if(!this.allowsMarks(t.child(e).marks))return!1;return!0}checkContent(t){if(!this.validContent(t))throw RangeError(`Invalid content for node ${this.name}: ${t.toString().slice(0,50)}`)}checkAttrs(t){L(this.attrs,t,"node",this.name)}allowsMarkType(t){return null==this.markSet||this.markSet.indexOf(t)>-1}allowsMarks(t){if(null==this.markSet)return!0;for(let e=0;e<t.length;e++)if(!this.allowsMarkType(t[e].type))return!1;return!0}allowedMarks(t){let e;if(null==this.markSet)return t;for(let n=0;n<t.length;n++)this.allowsMarkType(t[n].type)?e&&e.push(t[n]):e||(e=t.slice(0,n));return e?e.length?e:a.none:t}static compile(t,e){let n=Object.create(null);t.forEach((t,r)=>n[t]=new F(t,e,r));let r=e.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let t in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class D{constructor(t,e,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(t,e,n){let r=n.split("|");return n=>{let i=null===n?"null":typeof n;if(0>r.indexOf(i))throw RangeError(`Expected value of type ${r} for attribute ${e} on type ${t}, got ${i}`)}}(t,e,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class P{constructor(t,e,n,r){this.name=t,this.rank=e,this.schema=n,this.spec=r,this.attrs=z(t,r.attrs),this.excluded=null;let i=I(this.attrs);this.instance=i?new a(this,i):null}create(t=null){return!t&&this.instance?this.instance:new a(this,N(this.attrs,t))}static compile(t,e){let n=Object.create(null),r=0;return t.forEach((t,i)=>n[t]=new P(t,r++,e,i)),n}removeFromSet(t){for(var e=0;e<t.length;e++)t[e].type==this&&(t=t.slice(0,e).concat(t.slice(e+1)),e--);return t}isInSet(t){for(let e=0;e<t.length;e++)if(t[e].type==this)return t[e]}checkAttrs(t){L(this.attrs,t,"mark",this.name)}excludes(t){return this.excluded.indexOf(t)>-1}}class H{constructor(t){this.linebreakReplacement=null,this.cached=Object.create(null);let e=this.spec={};for(let n in t)e[n]=t[n];e.nodes=r.from(t.nodes),e.marks=r.from(t.marks||{}),this.nodes=F.compile(this.spec.nodes,this),this.marks=P.compile(this.spec.marks,this);let n=Object.create(null);for(let t in this.nodes){if(t in this.marks)throw RangeError(t+" can not be both a node and a mark");let e=this.nodes[t],r=e.spec.content||"",i=e.spec.marks;if(e.contentMatch=n[r]||(n[r]=C.parse(r,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==i?null:i?B(this,i.split(" ")):""!=i&&e.inlineContent?null:[]}for(let t in this.marks){let e=this.marks[t],n=e.spec.excludes;e.excluded=null==n?[e]:""==n?[]:B(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,n,r){if("string"==typeof t)t=this.nodeType(t);else if(t instanceof F){if(t.schema!=this)throw RangeError("Node type from different schema used ("+t.name+")")}else throw RangeError("Invalid node type: "+t);return t.createChecked(e,n,r)}text(t,e){let n=this.nodes.text;return new M(n,n.defaultAttrs,t,a.setFrom(e))}mark(t,e){return"string"==typeof t&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return S.fromJSON(this,t)}markFromJSON(t){return a.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw RangeError("Unknown node type: "+t);return e}}function B(t,e){let n=[];for(let r=0;r<e.length;r++){let i=e[r],s=t.marks[i],o=s;if(s)n.push(s);else for(let e in t.marks){let r=t.marks[e];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(o=r)}if(!o)throw SyntaxError("Unknown mark type: '"+e[r]+"'")}return n}class j{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[];let n=this.matchedStyles=[];e.forEach(t=>{if(null!=t.tag)this.tags.push(t);else if(null!=t.style){let e=/[^=]*/.exec(t.style)[0];0>n.indexOf(e)&&n.push(e),this.styles.push(t)}}),this.normalizeLists=!this.tags.some(e=>{if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;let n=t.nodes[e.node];return n.contentMatch.matchType(n)})}parse(t,e={}){let n=new W(this,e,!1);return n.addAll(t,a.none,e.from,e.to),n.finish()}parseSlice(t,e={}){let n=new W(this,e,!0);return n.addAll(t,a.none,e.from,e.to),c.maxOpen(n.finish())}matchTag(t,e,n){for(let s=n?this.tags.indexOf(n)+1:0;s<this.tags.length;s++){var r,i;let n=this.tags[s];if(r=t,i=n.tag,(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,i)&&(void 0===n.namespace||t.namespaceURI==n.namespace)&&(!n.context||e.matchesContext(n.context))){if(n.getAttrs){let e=n.getAttrs(t);if(!1===e)continue;n.attrs=e||void 0}return n}}}matchStyle(t,e,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],s=r.style;if(0==s.indexOf(t)&&(!r.context||n.matchesContext(r.context))&&(!(s.length>t.length)||61==s.charCodeAt(t.length)&&s.slice(t.length+1)==e)){if(r.getAttrs){let t=r.getAttrs(e);if(!1===t)continue;r.attrs=t||void 0}return r}}}static schemaRules(t){let e=[];function n(t){let n=null==t.priority?50:t.priority,r=0;for(;r<e.length;r++){let t=e[r];if((null==t.priority?50:t.priority)<n)break}e.splice(r,0,t)}for(let e in t.marks){let r=t.marks[e].spec.parseDOM;r&&r.forEach(t=>{n(t=U(t)),t.mark||t.ignore||t.clearMark||(t.mark=e)})}for(let e in t.nodes){let r=t.nodes[e].spec.parseDOM;r&&r.forEach(t=>{n(t=U(t)),t.node||t.ignore||t.mark||(t.node=e)})}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new j(t,j.schemaRules(t)))}}let J={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},$={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},K={ol:!0,ul:!0};function V(t,e,n){return null!=e?!!e|2*("full"===e):t&&"pre"==t.whitespace?3:-5&n}class _{constructor(t,e,n,r,i,s){this.type=t,this.attrs=e,this.marks=n,this.solid=r,this.options=s,this.content=[],this.activeMarks=a.none,this.match=i||(4&s?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(i.from(t));if(e)this.match=this.type.contentMatch.matchFragment(e);else{let e=this.type.contentMatch,n;return(n=e.findWrapping(t.type))?(this.match=e,n):null}}return this.match.findWrapping(t.type)}finish(t){if(!(1&this.options)){let t=this.content[this.content.length-1],e;t&&t.isText&&(e=/[ \t\r\n\u000c]+$/.exec(t.text))&&(t.text.length==e[0].length?this.content.pop():this.content[this.content.length-1]=t.withText(t.text.slice(0,t.text.length-e[0].length)))}let e=i.from(this.content);return!t&&this.match&&(e=e.append(this.match.fillBefore(i.empty,!0))),this.type?this.type.create(this.attrs,e,this.marks):e}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!J.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class W{constructor(t,e,n){this.parser=t,this.options=e,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=e.topNode,i,s=V(null,e.preserveWhitespace,0)|4*!!n;i=r?new _(r.type,r.attrs,a.none,!0,e.topMatch||r.type.contentMatch,s):n?new _(null,null,a.none,!0,null,s):new _(t.schema.topNodeType,null,a.none,!0,null,s),this.nodes=[i],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t,e){3==t.nodeType?this.addTextNode(t,e):1==t.nodeType&&this.addElement(t,e)}addTextNode(t,e){let n=t.nodeValue,r=this.top,i=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===i||r.inlineContext(t)||/[^ \t\r\n\u000c]/.test(n)){if(i)n="full"!==i?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let e=r.content[r.content.length-1],i=t.previousSibling;(!e||i&&"BR"==i.nodeName||e.isText&&/[ \t\r\n\u000c]$/.test(e.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),e,!/\S/.test(n)),this.findInText(t)}else this.findInside(t)}addElement(t,e,n){let r=this.localPreserveWS,i=this.top;("PRE"==t.tagName||/pre/.test(t.style&&t.style.whiteSpace))&&(this.localPreserveWS=!0);let s=t.nodeName.toLowerCase(),o;K.hasOwnProperty(s)&&this.parser.normalizeLists&&function(t){for(let e=t.firstChild,n=null;e;e=e.nextSibling){let t=1==e.nodeType?e.nodeName.toLowerCase():null;t&&K.hasOwnProperty(t)&&n?(n.appendChild(e),e=n):"li"==t?n=e:t&&(n=null)}}(t);let l=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(o=this.parser.matchTag(t,this,n));t:if(l?l.ignore:$.hasOwnProperty(s))this.findInside(t),this.ignoreFallback(t,e);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(t=l.skip);let n,r=this.needsBlock;if(J.hasOwnProperty(s))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),n=!0,i.type||(this.needsBlock=!0);else if(!t.firstChild){this.leafFallback(t,e);break t}let o=l&&l.skip?e:this.readStyles(t,e);o&&this.addAll(t,o),n&&this.sync(i),this.needsBlock=r}else{let n=this.readStyles(t,e);n&&this.addElementByRule(t,l,n,!1===l.consuming?o:void 0)}this.localPreserveWS=r}leafFallback(t,e){"BR"==t.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode("\n"),e)}ignoreFallback(t,e){"BR"!=t.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),e,!0)}readStyles(t,e){let n=t.style;if(n&&n.length)for(let t=0;t<this.parser.matchedStyles.length;t++){let r=this.parser.matchedStyles[t],i=n.getPropertyValue(r);if(i)for(let t;;){let n=this.parser.matchStyle(r,i,this,t);if(!n)break;if(n.ignore)return null;if(e=n.clearMark?e.filter(t=>!n.clearMark(t)):e.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)t=n;else break}}return e}addElementByRule(t,e,n,r){let i,s;if(e.node)if((s=this.parser.schema.nodes[e.node]).isLeaf)this.insertNode(s.create(e.attrs),n,"BR"==t.nodeName)||this.leafFallback(t,n);else{let t=this.enter(s,e.attrs||null,n,e.preserveWhitespace);t&&(i=!0,n=t)}else{let t=this.parser.schema.marks[e.mark];n=n.concat(t.create(e.attrs))}let o=this.top;if(s&&s.isLeaf)this.findInside(t);else if(r)this.addElement(t,n,r);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach(t=>this.insertNode(t,n,!1));else{let r=t;"string"==typeof e.contentElement?r=t.querySelector(e.contentElement):"function"==typeof e.contentElement?r=e.contentElement(t):e.contentElement&&(r=e.contentElement),this.findAround(t,r,!0),this.addAll(r,n),this.findAround(t,r,!1)}i&&this.sync(o)&&this.open--}addAll(t,e,n,r){let i=n||0;for(let s=n?t.childNodes[n]:t.firstChild,o=null==r?null:t.childNodes[r];s!=o;s=s.nextSibling,++i)this.findAtPoint(t,i),this.addDOM(s,e);this.findAtPoint(t,i)}findPlace(t,e,n){let r,i;for(let e=this.open,s=0;e>=0;e--){let o=this.nodes[e],l=o.findWrapping(t);if(l&&(!r||r.length>l.length+s)&&(r=l,i=o,!l.length))break;if(o.solid){if(n)break;s+=2}}if(!r)return null;this.sync(i);for(let t=0;t<r.length;t++)e=this.enterInner(r[t],null,e,!1);return e}insertNode(t,e,n){if(t.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&(e=this.enterInner(t,null,e))}let r=this.findPlace(t,e,n);if(r){this.closeExtra();let e=this.top;e.match&&(e.match=e.match.matchType(t.type));let n=a.none;for(let i of r.concat(t.marks))(e.type?e.type.allowsMarkType(i.type):q(i.type,t.type))&&(n=i.addToSet(n));return e.content.push(t.mark(n)),!0}return!1}enter(t,e,n,r){let i=this.findPlace(t.create(e),n,!1);return i&&(i=this.enterInner(t,e,n,!0,r)),i}enterInner(t,e,n,r=!1,i){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(t);let o=V(t,i,s.options);4&s.options&&0==s.content.length&&(o|=4);let l=a.none;return n=n.filter(e=>(s.type?!s.type.allowsMarkType(e.type):!q(e.type,t))||(l=e.addToSet(l),!1)),this.nodes.push(new _(t,e,l,r,null,o)),this.open++,n}closeExtra(t=!1){let e=this.nodes.length-1;if(e>this.open){for(;e>this.open;e--)this.nodes[e-1].content.push(this.nodes[e].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(t){for(let e=this.open;e>=0;e--)if(this.nodes[e]==t)return this.open=e,!0;else this.localPreserveWS&&(this.nodes[e].options|=1);return!1}get currentPos(){this.closeExtra();let t=0;for(let e=this.open;e>=0;e--){let n=this.nodes[e].content;for(let e=n.length-1;e>=0;e--)t+=n[e].nodeSize;e&&t++}return t}findAtPoint(t,e){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==t&&this.find[n].offset==e&&(this.find[n].pos=this.currentPos)}findInside(t){if(this.find)for(let e=0;e<this.find.length;e++)null==this.find[e].pos&&1==t.nodeType&&t.contains(this.find[e].node)&&(this.find[e].pos=this.currentPos)}findAround(t,e,n){if(t!=e&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==t.nodeType&&t.contains(this.find[r].node)&&e.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].node==t&&(this.find[e].pos=this.currentPos-(t.nodeValue.length-this.find[e].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),i=-(n?n.depth+1:0)+ +!r,s=(t,o)=>{for(;t>=0;t--){let l=e[t];if(""==l){if(t==e.length-1||0==t)continue;for(;o>=i;o--)if(s(t-1,o))return!0;return!1}{let t=o>0||0==o&&r?this.nodes[o].type:n&&o>=i?n.node(o-i).type:null;if(!t||t.name!=l&&!t.isInGroup(l))return!1;o--}}return!0};return s(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let n=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}}function U(t){let e={};for(let n in t)e[n]=t[n];return e}function q(t,e){let n=e.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(t))continue;let s=[],o=t=>{s.push(t);for(let n=0;n<t.edgeCount;n++){let{type:r,next:i}=t.edge(n);if(r==e||0>s.indexOf(i)&&o(i))return!0}};if(o(i.contentMatch))return!0}}class G{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},n){n||(n=X(e).createDocumentFragment());let r=n,i=[];return t.forEach(t=>{if(i.length||t.marks.length){let n=0,s=0;for(;n<i.length&&s<t.marks.length;){let e=t.marks[s];if(!this.marks[e.type.name]){s++;continue}if(!e.eq(i[n][0])||!1===e.type.spec.spanning)break;n++,s++}for(;n<i.length;)r=i.pop()[1];for(;s<t.marks.length;){let n=t.marks[s++],o=this.serializeMark(n,t.isInline,e);o&&(i.push([n,r]),r.appendChild(o.dom),r=o.contentDOM||o.dom)}}r.appendChild(this.serializeNodeInner(t,e))}),n}serializeNodeInner(t,e){let{dom:n,contentDOM:r}=Z(X(e),this.nodes[t.type.name](t),null,t.attrs);if(r){if(t.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,r)}return n}serializeNode(t,e={}){let n=this.serializeNodeInner(t,e);for(let r=t.marks.length-1;r>=0;r--){let i=this.serializeMark(t.marks[r],t.isInline,e);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(t,e,n={}){let r=this.marks[t.type.name];return r&&Z(X(n),r(t,e),null,t.attrs)}static renderSpec(t,e,n=null,r){return Z(t,e,n,r)}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new G(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=Y(t.nodes);return e.text||(e.text=t=>t.text),e}static marksFromSchema(t){return Y(t.marks)}}function Y(t){let e={};for(let n in t){let r=t[n].spec.toDOM;r&&(e[n]=r)}return e}function X(t){return t.document||window.document}let Q=new WeakMap;function Z(t,e,n,r){let i,s,o;if("string"==typeof e)return{dom:t.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let l=e[0],a;if("string"!=typeof l)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(s=Q.get(r))&&Q.set(r,(o=null,!function t(e){if(e&&"object"==typeof e)if(Array.isArray(e))if("string"==typeof e[0])o||(o=[]),o.push(e);else for(let n=0;n<e.length;n++)t(e[n]);else for(let n in e)t(e[n])}(r),s=o)),a=s)&&a.indexOf(e)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let h=l.indexOf(" ");h>0&&(n=l.slice(0,h),l=l.slice(h+1));let c=n?t.createElementNS(n,l):t.createElement(l),p=e[1],u=1;if(p&&"object"==typeof p&&null==p.nodeType&&!Array.isArray(p)){for(let t in u=2,p)if(null!=p[t]){let e=t.indexOf(" ");e>0?c.setAttributeNS(t.slice(0,e),t.slice(e+1),p[t]):c.setAttribute(t,p[t])}}for(let s=u;s<e.length;s++){let o=e[s];if(0===o){if(s<e.length-1||s>u)throw RangeError("Content hole must be the only child of its parent node");return{dom:c,contentDOM:c}}{let{dom:e,contentDOM:s}=Z(t,o,n,r);if(c.appendChild(e),s){if(i)throw RangeError("Multiple content holes");i=s}}}return{dom:c,contentDOM:i}}},224:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},239:(t,e,n)=>{n.d(e,{bL:()=>b,zi:()=>x});var r=n(2115),i=n(5185),s=n(6101),o=n(6081),l=n(5845),a=n(1275),h=n(3655),c=n(5155),p="Switch",[u,d]=(0,o.A)(p),[f,m]=u(p),g=r.forwardRef((t,e)=>{let{__scopeSwitch:n,name:o,checked:a,defaultChecked:u,required:d,disabled:m,value:g="on",onCheckedChange:y,form:k,...b}=t,[x,S]=r.useState(null),M=(0,s.s)(e,t=>S(t)),A=r.useRef(!1),C=!x||k||!!x.closest("form"),[T,E]=(0,l.i)({prop:a,defaultProp:null!=u&&u,onChange:y,caller:p});return(0,c.jsxs)(f,{scope:n,checked:T,disabled:m,children:[(0,c.jsx)(h.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":d,"data-state":w(T),"data-disabled":m?"":void 0,disabled:m,value:g,...b,ref:M,onClick:(0,i.m)(t.onClick,t=>{E(t=>!t),C&&(A.current=t.isPropagationStopped(),A.current||t.stopPropagation())})}),C&&(0,c.jsx)(v,{control:x,bubbles:!A.current,name:o,value:g,checked:T,required:d,disabled:m,form:k,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var y="SwitchThumb",k=r.forwardRef((t,e)=>{let{__scopeSwitch:n,...r}=t,i=m(y,n);return(0,c.jsx)(h.sG.span,{"data-state":w(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:e})});k.displayName=y;var v=r.forwardRef((t,e)=>{let{__scopeSwitch:n,control:i,checked:o,bubbles:l=!0,...h}=t,p=r.useRef(null),u=(0,s.s)(p,e),d=function(t){let e=r.useRef({value:t,previous:t});return r.useMemo(()=>(e.current.value!==t&&(e.current.previous=e.current.value,e.current.value=t),e.current.previous),[t])}(o),f=(0,a.X)(i);return r.useEffect(()=>{let t=p.current;if(!t)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==o&&e){let n=new Event("click",{bubbles:l});e.call(t,o),t.dispatchEvent(n)}},[d,o,l]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...h,tabIndex:-1,ref:u,style:{...h.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(t){return t?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var b=g,x=k},290:(t,e,n)=>{n.d(e,{$f:()=>I,G2:()=>w,I$:()=>A,Im:()=>L,Qv:()=>h,Sd:()=>k,Z1:()=>C,_G:()=>p,_e:()=>f,bh:()=>b,eB:()=>c,eT:()=>y,ec:()=>N,hy:()=>E,ic:()=>l,iz:()=>T,pC:()=>S,yY:()=>x,y_:()=>z});var r,i=n(808),s=n(156),o=n(2571);let l=(t,e)=>!t.selection.empty&&(e&&e(t.tr.deleteSelection().scrollIntoView()),!0);function a(t,e){let{$cursor:n}=t.selection;return n&&(e?e.endOfTextblock("backward",t):!(n.parentOffset>0))?n:null}let h=(t,e,n)=>{let r=a(t,n);if(!r)return!1;let l=m(r);if(!l){let n=r.blockRange(),s=n&&(0,i.jP)(n);return null!=s&&(e&&e(t.tr.lift(n,s).scrollIntoView()),!0)}let h=l.nodeBefore;if(O(t,l,e,-1))return!0;if(0==r.parent.content.size&&(d(h,"end")||o.nh.isSelectable(h)))for(let n=r.depth;;n--){let a=(0,i.$L)(t.doc,r.before(n),r.after(n),s.Ji.empty);if(a&&a.slice.size<a.to-a.from){if(e){let n=t.tr.step(a);n.setSelection(d(h,"end")?o.LN.findFrom(n.doc.resolve(n.mapping.map(l.pos,-1)),-1):o.nh.create(n.doc,l.pos-h.nodeSize)),e(n.scrollIntoView())}return!0}if(1==n||r.node(n-1).childCount>1)break}return!!h.isAtom&&l.depth==r.depth-1&&(e&&e(t.tr.delete(l.pos-h.nodeSize,l.pos).scrollIntoView()),!0)},c=(t,e,n)=>{let r=a(t,n);if(!r)return!1;let i=m(r);return!!i&&u(t,i,e)},p=(t,e,n)=>{let r=g(t,n);if(!r)return!1;let i=v(r);return!!i&&u(t,i,e)};function u(t,e,n){let r=e.nodeBefore,l=e.pos-1;for(;!r.isTextblock;l--){if(r.type.spec.isolating)return!1;let t=r.lastChild;if(!t)return!1;r=t}let a=e.nodeAfter,h=e.pos+1;for(;!a.isTextblock;h++){if(a.type.spec.isolating)return!1;let t=a.firstChild;if(!t)return!1;a=t}let c=(0,i.$L)(t.doc,l,h,s.Ji.empty);if(!c||c.from!=l||c instanceof i.Ln&&c.slice.size>=h-l)return!1;if(n){let e=t.tr.step(c);e.setSelection(o.U3.create(e.doc,l)),n(e.scrollIntoView())}return!0}function d(t,e,n=!1){for(let r=t;r;r="start"==e?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let f=(t,e,n)=>{let{$head:r,empty:i}=t.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",t):r.parentOffset>0)return!1;s=m(r)}let l=s&&s.nodeBefore;return!!l&&!!o.nh.isSelectable(l)&&(e&&e(t.tr.setSelection(o.nh.create(t.doc,s.pos-l.nodeSize)).scrollIntoView()),!0)};function m(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){if(t.index(e)>0)return t.doc.resolve(t.before(e+1));if(t.node(e).type.spec.isolating)break}return null}function g(t,e){let{$cursor:n}=t.selection;return n&&(e?e.endOfTextblock("forward",t):!(n.parentOffset<n.parent.content.size))?n:null}let y=(t,e,n)=>{let r=g(t,n);if(!r)return!1;let l=v(r);if(!l)return!1;let a=l.nodeAfter;if(O(t,l,e,1))return!0;if(0==r.parent.content.size&&(d(a,"start")||o.nh.isSelectable(a))){let n=(0,i.$L)(t.doc,r.before(),r.after(),s.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(e){let r=t.tr.step(n);r.setSelection(d(a,"start")?o.LN.findFrom(r.doc.resolve(r.mapping.map(l.pos)),1):o.nh.create(r.doc,r.mapping.map(l.pos))),e(r.scrollIntoView())}return!0}}return!!a.isAtom&&l.depth==r.depth-1&&(e&&e(t.tr.delete(l.pos,l.pos+a.nodeSize).scrollIntoView()),!0)},k=(t,e,n)=>{let{$head:r,empty:i}=t.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",t):r.parentOffset<r.parent.content.size)return!1;s=v(r)}let l=s&&s.nodeAfter;return!!l&&!!o.nh.isSelectable(l)&&(e&&e(t.tr.setSelection(o.nh.create(t.doc,s.pos)).scrollIntoView()),!0)};function v(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){let n=t.node(e);if(t.index(e)+1<n.childCount)return t.doc.resolve(t.after(e+1));if(n.type.spec.isolating)break}return null}let w=(t,e)=>{let n=t.selection,r=n instanceof o.nh,s;if(r){if(n.node.isTextblock||!(0,i.n9)(t.doc,n.from))return!1;s=n.from}else if(null==(s=(0,i.N0)(t.doc,n.from,-1)))return!1;if(e){let n=t.tr.join(s);r&&n.setSelection(o.nh.create(n.doc,s-t.doc.resolve(s).nodeBefore.nodeSize)),e(n.scrollIntoView())}return!0},b=(t,e)=>{let n=t.selection,r;if(n instanceof o.nh){if(n.node.isTextblock||!(0,i.n9)(t.doc,n.to))return!1;r=n.to}else if(null==(r=(0,i.N0)(t.doc,n.to,1)))return!1;return e&&e(t.tr.join(r).scrollIntoView()),!0},x=(t,e)=>{let{$from:n,$to:r}=t.selection,s=n.blockRange(r),o=s&&(0,i.jP)(s);return null!=o&&(e&&e(t.tr.lift(s,o).scrollIntoView()),!0)},S=(t,e)=>{let{$head:n,$anchor:r}=t.selection;return!!n.parent.type.spec.code&&!!n.sameParent(r)&&(e&&e(t.tr.insertText("\n").scrollIntoView()),!0)};function M(t){for(let e=0;e<t.edgeCount;e++){let{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let A=(t,e)=>{let{$head:n,$anchor:r}=t.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let i=n.node(-1),s=n.indexAfter(-1),l=M(i.contentMatchAt(s));if(!l||!i.canReplaceWith(s,s,l))return!1;if(e){let r=n.after(),i=t.tr.replaceWith(r,r,l.createAndFill());i.setSelection(o.LN.near(i.doc.resolve(r),1)),e(i.scrollIntoView())}return!0},C=(t,e)=>{let n=t.selection,{$from:r,$to:i}=n;if(n instanceof o.i5||r.parent.inlineContent||i.parent.inlineContent)return!1;let s=M(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let n=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=t.tr.insert(n,s.createAndFill());l.setSelection(o.U3.create(l.doc,n+1)),e(l.scrollIntoView())}return!0},T=(t,e)=>{let{$cursor:n}=t.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if((0,i.zy)(t.doc,r))return e&&e(t.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),s=r&&(0,i.jP)(r);return null!=s&&(e&&e(t.tr.lift(r,s).scrollIntoView()),!0)},E=(t,e)=>{let{$from:n,to:r}=t.selection,i,s=n.sharedDepth(r);return 0!=s&&(i=n.before(s),e&&e(t.tr.setSelection(o.nh.create(t.doc,i))),!0)};function O(t,e,n,r){let l,a,h,c=e.nodeBefore,p=e.nodeAfter,u,f,m=c.type.spec.isolating||p.type.spec.isolating;if(!m&&(l=e.nodeBefore,a=e.nodeAfter,h=e.index(),l&&a&&l.type.compatibleContent(a.type)&&(!l.content.size&&e.parent.canReplace(h-1,h)?(n&&n(t.tr.delete(e.pos-l.nodeSize,e.pos).scrollIntoView()),!0):!!e.parent.canReplace(h,h+1)&&!!(a.isTextblock||(0,i.n9)(t.doc,e.pos))&&(n&&n(t.tr.join(e.pos).scrollIntoView()),!0))))return!0;let g=!m&&e.parent.canReplace(e.index(),e.index()+1);if(g&&(u=(f=c.contentMatchAt(c.childCount)).findWrapping(p.type))&&f.matchType(u[0]||p.type).validEnd){if(n){let r=e.pos+p.nodeSize,o=s.FK.empty;for(let t=u.length-1;t>=0;t--)o=s.FK.from(u[t].create(null,o));o=s.FK.from(c.copy(o));let l=t.tr.step(new i.Wg(e.pos-1,r,e.pos,r,new s.Ji(o,1,0),u.length,!0)),a=l.doc.resolve(r+2*u.length);a.nodeAfter&&a.nodeAfter.type==c.type&&(0,i.n9)(l.doc,a.pos)&&l.join(a.pos),n(l.scrollIntoView())}return!0}let y=p.type.spec.isolating||r>0&&m?null:o.LN.findFrom(e,1),k=y&&y.$from.blockRange(y.$to),v=k&&(0,i.jP)(k);if(null!=v&&v>=e.depth)return n&&n(t.tr.lift(k,v).scrollIntoView()),!0;if(g&&d(p,"start",!0)&&d(c,"end")){let r=c,o=[];for(;o.push(r),!r.isTextblock;)r=r.lastChild;let l=p,a=1;for(;!l.isTextblock;l=l.firstChild)a++;if(r.canReplace(r.childCount,r.childCount,l.content)){if(n){let r=s.FK.empty;for(let t=o.length-1;t>=0;t--)r=s.FK.from(o[t].copy(r));n(t.tr.step(new i.Wg(e.pos-o.length,e.pos+p.nodeSize,e.pos+a,e.pos+p.nodeSize-a,new s.Ji(r,o.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function R(t){return function(e,n){let r=e.selection,i=t<0?r.$from:r.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return!!i.node(s).isTextblock&&(n&&n(e.tr.setSelection(o.U3.create(e.doc,t<0?i.start(s):i.end(s)))),!0)}}let I=R(-1),N=R(1);function L(t,e=null){return function(n,r){let{$from:s,$to:o}=n.selection,l=s.blockRange(o),a=l&&(0,i.oM)(l,t,e);return!!a&&(r&&r(n.tr.wrap(l,a).scrollIntoView()),!0)}}function z(t,e=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:s},$to:{pos:o}}=n.selection.ranges[r];n.doc.nodesBetween(s,o,(r,s)=>{if(i)return!1;if(!(!r.isTextblock||r.hasMarkup(t,e)))if(r.type==t)i=!0;else{let e=n.doc.resolve(s),r=e.index();i=e.parent.canReplaceWith(r,r+1,t)}})}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:s},$to:{pos:o}}=n.selection.ranges[r];i.setBlockType(s,o,t,e)}r(i.scrollIntoView())}return!0}}function F(...t){return function(e,n,r){for(let i=0;i<t.length;i++)if(t[i](e,n,r))return!0;return!1}}let D=F(l,h,f),P=F(l,y,k),H={Enter:F(S,C,T,(t,e)=>{let{$from:n,$to:r}=t.selection;if(t.selection instanceof o.nh&&t.selection.node.isBlock)return!!n.parentOffset&&!!(0,i.zy)(t.doc,n.pos)&&(e&&e(t.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let s=[],l,a,h=!1,c=!1;for(let t=n.depth;;t--){if(n.node(t).isBlock){let e;h=n.end(t)==n.pos+(n.depth-t),c=n.start(t)==n.pos-(n.depth-t),a=M(n.node(t-1).contentMatchAt(n.indexAfter(t-1)));s.unshift(e||(h&&a?{type:a}:null)),l=t;break}if(1==t)return!1;s.unshift(null)}let p=t.tr;(t.selection instanceof o.U3||t.selection instanceof o.i5)&&p.deleteSelection();let u=p.mapping.map(n.pos),d=(0,i.zy)(p.doc,u,s.length,s);if(d||(s[0]=a?{type:a}:null,d=(0,i.zy)(p.doc,u,s.length,s)),!d)return!1;if(p.split(u,s.length,s),!h&&c&&n.node(l).type!=a){let t=p.mapping.map(n.before(l)),e=p.doc.resolve(t);a&&n.node(l-1).canReplaceWith(e.index(),e.index()+1,a)&&p.setNodeMarkup(p.mapping.map(n.before(l)),a)}return e&&e(p.scrollIntoView()),!0}),"Mod-Enter":A,Backspace:D,"Mod-Backspace":D,"Shift-Backspace":D,Delete:P,"Mod-Delete":P,"Mod-a":(t,e)=>(e&&e(t.tr.setSelection(new o.i5(t.doc))),!0)},B={"Ctrl-h":H.Backspace,"Alt-Backspace":H["Mod-Backspace"],"Ctrl-d":H.Delete,"Ctrl-Alt-Backspace":H["Mod-Delete"],"Alt-Delete":H["Mod-Delete"],"Alt-d":H["Mod-Delete"],"Ctrl-a":I,"Ctrl-e":N};for(let t in H)B[t]=H[t];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform()},808:(t,e,n)=>{n.d(e,{$L:()=>O,Ln:()=>m,N0:()=>T,Um:()=>E,Wg:()=>g,X9:()=>o,dL:()=>j,jP:()=>v,n9:()=>A,oM:()=>w,zy:()=>M});var r=n(156);class i{constructor(t,e,n){this.pos=t,this.delInfo=e,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class s{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&s.empty)return s.empty}recover(t){let e=0,n=65535&t;if(!this.inverted)for(let t=0;t<n;t++)e+=this.ranges[3*t+2]-this.ranges[3*t+1];return this.ranges[3*n]+e+(t-(65535&t))/65536}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,n){let r=0,s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>t)break;let h=this.ranges[l+s],c=this.ranges[l+o],p=a+h;if(t<=p){let s=h?t==a?-1:t==p?1:e:e,o=a+r+(s<0?0:c);if(n)return o;let u=t==(e<0?a:p)?null:l/3+(t-a)*65536,d=t==a?2:t==p?1:4;return(e<0?t!=a:t!=p)&&(d|=8),new i(o,d,u)}r+=c-h}return n?t+r:new i(t+r,0,null)}touches(t,e){let n=0,r=65535&e,i=this.inverted?2:1,s=this.inverted?1:2;for(let e=0;e<this.ranges.length;e+=3){let o=this.ranges[e]-(this.inverted?n:0);if(o>t)break;let l=this.ranges[e+i];if(t<=o+l&&e==3*r)return!0;n+=this.ranges[e+s]-l}return!1}forEach(t){let e=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let s=this.ranges[r],o=s-(this.inverted?i:0),l=s+(this.inverted?0:i),a=this.ranges[r+e],h=this.ranges[r+n];t(o,o+a,l,l+h),i+=h-a}}invert(){return new s(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return 0==t?s.empty:new s(t<0?[0,-t,0]:[0,0,t])}}s.empty=new s([]);class o{constructor(t,e,n=0,r=t?t.length:0){this.mirror=e,this.from=n,this.to=r,this._maps=t||[],this.ownData=!(t||e)}get maps(){return this._maps}slice(t=0,e=this.maps.length){return new o(this._maps,this.mirror,t,e)}appendMap(t,e){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(t),null!=e&&this.setMirror(this._maps.length-1,e)}appendMapping(t){for(let e=0,n=this._maps.length;e<t._maps.length;e++){let r=t.getMirror(e);this.appendMap(t._maps[e],null!=r&&r<e?n+r:void 0)}}getMirror(t){if(this.mirror){for(let e=0;e<this.mirror.length;e++)if(this.mirror[e]==t)return this.mirror[e+(e%2?-1:1)]}}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let e=t.maps.length-1,n=this._maps.length+t._maps.length;e>=0;e--){let r=t.getMirror(e);this.appendMap(t._maps[e].invert(),null!=r&&r>e?n-r-1:void 0)}}invert(){let t=new o;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let n=this.from;n<this.to;n++)t=this._maps[n].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this._maps[n].mapResult(t,e);if(null!=i.recover){let e=this.getMirror(n);if(null!=e&&e>n&&e<this.to){n=e,t=this._maps[e].recover(i.recover);continue}}r|=i.delInfo,t=i.pos}return n?t:new i(t,r,null)}}let l=Object.create(null);class a{getMap(){return s.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=l[e.stepType];if(!n)throw RangeError(`No step type ${e.stepType} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in l)throw RangeError("Duplicate use of step JSON ID "+t);return l[t]=e,e.prototype.jsonID=t,e}}class h{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new h(t,null)}static fail(t){return new h(null,t)}static fromReplace(t,e,n,i){try{return h.ok(t.replace(e,n,i))}catch(t){if(t instanceof r.vI)return h.fail(t.message);throw t}}}function c(t,e,n){let i=[];for(let r=0;r<t.childCount;r++){let s=t.child(r);s.content.size&&(s=s.copy(c(s.content,e,s))),s.isInline&&(s=e(s,n,r)),i.push(s)}return r.FK.fromArray(i)}class p extends a{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=t.resolve(this.from),i=n.node(n.sharedDepth(this.to)),s=new r.Ji(c(e.content,(t,e)=>t.isAtom&&e.type.allowsMarkType(this.mark.type)?t.mark(this.mark.addToSet(t.marks)):t,i),e.openStart,e.openEnd);return h.fromReplace(t,this.from,this.to,s)}invert(){return new u(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new p(e.pos,n.pos,this.mark)}merge(t){return t instanceof p&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new p(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new p(e.from,e.to,t.markFromJSON(e.mark))}}a.jsonID("addMark",p);class u extends a{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=new r.Ji(c(e.content,t=>t.mark(this.mark.removeFromSet(t.marks)),t),e.openStart,e.openEnd);return h.fromReplace(t,this.from,this.to,n)}invert(){return new p(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new u(e.pos,n.pos,this.mark)}merge(t){return t instanceof u&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new u(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new u(e.from,e.to,t.markFromJSON(e.mark))}}a.jsonID("removeMark",u);class d extends a{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return h.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!e.isLeaf))}invert(t){let e=t.nodeAt(this.pos);if(e){let t=this.mark.addToSet(e.marks);if(t.length==e.marks.length){for(let n=0;n<e.marks.length;n++)if(!e.marks[n].isInSet(t))return new d(this.pos,e.marks[n]);return new d(this.pos,this.mark)}}return new f(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new d(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new d(e.pos,t.markFromJSON(e.mark))}}a.jsonID("addNodeMark",d);class f extends a{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return h.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!e.isLeaf))}invert(t){let e=t.nodeAt(this.pos);return e&&this.mark.isInSet(e.marks)?new d(this.pos,this.mark):this}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new f(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new f(e.pos,t.markFromJSON(e.mark))}}a.jsonID("removeNodeMark",f);class m extends a{constructor(t,e,n,r=!1){super(),this.from=t,this.to=e,this.slice=n,this.structure=r}apply(t){return this.structure&&y(t,this.from,this.to)?h.fail("Structure replace would overwrite content"):h.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new s([this.from,this.to-this.from,this.slice.size])}invert(t){return new m(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deletedAcross&&n.deletedAcross?null:new m(e.pos,Math.max(e.pos,n.pos),this.slice,this.structure)}merge(t){if(!(t instanceof m)||t.structure||this.structure)return null;if(this.from+this.slice.size!=t.from||this.slice.openEnd||t.slice.openStart)if(t.to!=this.from||this.slice.openStart||t.slice.openEnd)return null;else{let e=this.slice.size+t.slice.size==0?r.Ji.empty:new r.Ji(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new m(t.from,this.to,e,this.structure)}{let e=this.slice.size+t.slice.size==0?r.Ji.empty:new r.Ji(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new m(this.from,this.to+(t.to-t.from),e,this.structure)}}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new m(e.from,e.to,r.Ji.fromJSON(t,e.slice),!!e.structure)}}a.jsonID("replace",m);class g extends a{constructor(t,e,n,r,i,s,o=!1){super(),this.from=t,this.to=e,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=s,this.structure=o}apply(t){if(this.structure&&(y(t,this.from,this.gapFrom)||y(t,this.gapTo,this.to)))return h.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return h.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,e.content);return n?h.fromReplace(t,this.from,this.to,n):h.fail("Content does not fit in gap")}getMap(){return new s([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new g(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1),r=this.from==this.gapFrom?e.pos:t.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:t.map(this.gapTo,1);return e.deletedAcross&&n.deletedAcross||r<e.pos||i>n.pos?null:new g(e.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to||"number"!=typeof e.gapFrom||"number"!=typeof e.gapTo||"number"!=typeof e.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new g(e.from,e.to,e.gapFrom,e.gapTo,r.Ji.fromJSON(t,e.slice),e.insert,!!e.structure)}}function y(t,e,n){let r=t.resolve(e),i=n-e,s=r.depth;for(;i>0&&s>0&&r.indexAfter(s)==r.node(s).childCount;)s--,i--;if(i>0){let t=r.node(s).maybeChild(r.indexAfter(s));for(;i>0;){if(!t||t.isLeaf)return!0;t=t.firstChild,i--}}return!1}function k(t,e,n,i=n.contentMatch,s=!0){let o=t.doc.nodeAt(e),l=[],a=e+1;for(let e=0;e<o.childCount;e++){let h=o.child(e),c=a+h.nodeSize,p=i.matchType(h.type);if(p){i=p;for(let e=0;e<h.marks.length;e++)n.allowsMarkType(h.marks[e].type)||t.step(new u(a,c,h.marks[e]));if(s&&h.isText&&"pre"!=n.whitespace){let t,e=/\r?\n|\r/g,i;for(;t=e.exec(h.text);)i||(i=new r.Ji(r.FK.from(n.schema.text(" ",n.allowedMarks(h.marks))),0,0)),l.push(new m(a+t.index,a+t.index+t[0].length,i))}}else l.push(new m(a,c,r.Ji.empty));a=c}if(!i.validEnd){let e=i.fillBefore(r.FK.empty,!0);t.replace(a,a,new r.Ji(e,0,0))}for(let e=l.length-1;e>=0;e--)t.step(l[e])}function v(t){let e=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let n=t.depth;;--n){let r=t.$from.node(n),i=t.$from.index(n),s=t.$to.indexAfter(n);if(n<t.depth&&r.canReplace(i,s,e))return n;if(0==n||r.type.spec.isolating||!((0==i||r.canReplace(i,r.childCount))&&(s==r.childCount||r.canReplace(0,s))))break}return null}function w(t,e,n=null,r=t){let i=function(t,e){let{parent:n,startIndex:r,endIndex:i}=t,s=n.contentMatchAt(r).findWrapping(e);if(!s)return null;let o=s.length?s[0]:e;return n.canReplaceWith(r,i,o)?s:null}(t,e),s=i&&function(t,e){let{parent:n,startIndex:r,endIndex:i}=t,s=n.child(r),o=e.contentMatch.findWrapping(s.type);if(!o)return null;let l=(o.length?o[o.length-1]:e).contentMatch;for(let t=r;l&&t<i;t++)l=l.matchType(n.child(t).type);return l&&l.validEnd?o:null}(r,e);return s?i.map(b).concat({type:e,attrs:n}).concat(s.map(b)):null}function b(t){return{type:t,attrs:null}}function x(t,e,n,r){e.forEach((i,s)=>{if(i.isText){let o,l=/\r?\n|\r/g;for(;o=l.exec(i.text);){let i=t.mapping.slice(r).map(n+1+s+o.index);t.replaceWith(i,i+1,e.type.schema.linebreakReplacement.create())}}})}function S(t,e,n,r){e.forEach((i,s)=>{if(i.type==i.type.schema.linebreakReplacement){let i=t.mapping.slice(r).map(n+1+s);t.replaceWith(i,i+1,e.type.schema.text("\n"))}})}function M(t,e,n=1,r){let i=t.resolve(e),s=i.depth-n,o=r&&r[r.length-1]||i.parent;if(s<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!o.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let t=i.depth-1,e=n-2;t>s;t--,e--){let n=i.node(t),s=i.index(t);if(n.type.spec.isolating)return!1;let o=n.content.cutByIndex(s,n.childCount),l=r&&r[e+1];l&&(o=o.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[e]||n;if(!n.canReplace(s+1,n.childCount)||!a.type.validContent(o))return!1}let l=i.indexAfter(s),a=r&&r[0];return i.node(s).canReplaceWith(l,l,a?a.type:i.node(s+1).type)}function A(t,e){let n=t.resolve(e),r=n.index();return C(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function C(t,e){return!!(t&&e&&!t.isLeaf&&function(t,e){e.content.size||t.type.compatibleContent(e.type);let n=t.contentMatchAt(t.childCount),{linebreakReplacement:r}=t.type.schema;for(let i=0;i<e.childCount;i++){let s=e.child(i),o=s.type==r?t.type.schema.nodes.text:s.type;if(!(n=n.matchType(o))||!t.type.allowsMarks(s.marks))return!1}return n.validEnd}(t,e))}function T(t,e,n=-1){let r=t.resolve(e);for(let t=r.depth;;t--){let i,s,o=r.index(t);if(t==r.depth?(i=r.nodeBefore,s=r.nodeAfter):n>0?(i=r.node(t+1),o++,s=r.node(t).maybeChild(o)):(i=r.node(t).maybeChild(o-1),s=r.node(t+1)),i&&!i.isTextblock&&C(i,s)&&r.node(t).canReplace(o,o+1))return e;if(0==t)break;e=n<0?r.before(t):r.after(t)}}function E(t,e,n){let r=t.resolve(e);if(!n.content.size)return e;let i=n.content;for(let t=0;t<n.openStart;t++)i=i.firstChild.content;for(let t=1;t<=(0==n.openStart&&n.size?2:1);t++)for(let e=r.depth;e>=0;e--){let n=e==r.depth?0:r.pos<=(r.start(e+1)+r.end(e+1))/2?-1:1,s=r.index(e)+ +(n>0),o=r.node(e),l=!1;if(1==t)l=o.canReplace(s,s,i);else{let t=o.contentMatchAt(s).findWrapping(i.firstChild.type);l=t&&o.canReplaceWith(s,s,t[0])}if(l)return 0==n?r.pos:n<0?r.before(e+1):r.after(e+1)}return null}function O(t,e,n=e,i=r.Ji.empty){if(e==n&&!i.size)return null;let s=t.resolve(e),o=t.resolve(n);return R(s,o,i)?new m(e,n,i):new I(s,o,i).fit()}function R(t,e,n){return!n.openStart&&!n.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),n.content)}a.jsonID("replaceAround",g);class I{constructor(t,e,n){this.$from=t,this.$to=e,this.unplaced=n,this.frontier=[],this.placed=r.FK.empty;for(let e=0;e<=t.depth;e++){let n=t.node(e);this.frontier.push({type:n.type,match:n.contentMatchAt(t.indexAfter(e))})}for(let e=t.depth;e>0;e--)this.placed=r.FK.from(t.node(e).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let t=this.findFittable();t?this.placeNodes(t):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,n=this.$from,i=this.close(t<0?this.$to:n.doc.resolve(t));if(!i)return null;let s=this.placed,o=n.depth,l=i.depth;for(;o&&l&&1==s.childCount;)s=s.firstChild.content,o--,l--;let a=new r.Ji(s,o,l);return t>-1?new g(n.pos,t,this.$to.pos,this.$to.end(),a,e):a.size||n.pos!=this.$to.pos?new m(n.pos,i.pos,a):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<t;n++){let i=e.firstChild;if(e.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){t=n;break}e=i.content}for(let e=1;e<=2;e++)for(let n=1==e?t:this.unplaced.openStart;n>=0;n--){let t,i=null,s=(n?(i=z(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let t=this.depth;t>=0;t--){let{type:o,match:l}=this.frontier[t],a,h=null;if(1==e&&(s?l.matchType(s.type)||(h=l.fillBefore(r.FK.from(s),!1)):i&&o.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:t,parent:i,inject:h};if(2==e&&s&&(a=l.findWrapping(s.type)))return{sliceDepth:n,frontierDepth:t,parent:i,wrap:a};if(i&&l.matchType(i.type))break}}}openMore(){let{content:t,openStart:e,openEnd:n}=this.unplaced,i=z(t,e);return!!i.childCount&&!i.firstChild.isLeaf&&(this.unplaced=new r.Ji(t,e+1,Math.max(n,i.size+e>=t.size-n?e+1:0)),!0)}dropNode(){let{content:t,openStart:e,openEnd:n}=this.unplaced,i=z(t,e);if(i.childCount<=1&&e>0){let s=t.size-e<=e+i.size;this.unplaced=new r.Ji(N(t,e-1,1),e-1,s?e-1:n)}else this.unplaced=new r.Ji(N(t,e,1),e,n)}placeNodes({sliceDepth:t,frontierDepth:e,parent:n,inject:i,wrap:s}){for(;this.depth>e;)this.closeFrontierNode();if(s)for(let t=0;t<s.length;t++)this.openFrontierNode(s[t]);let o=this.unplaced,l=n?n.content:o.content,a=o.openStart-t,h=0,c=[],{match:p,type:u}=this.frontier[e];if(i){for(let t=0;t<i.childCount;t++)c.push(i.child(t));p=p.matchFragment(i)}let d=l.size+t-(o.content.size-o.openEnd);for(;h<l.childCount;){let t=l.child(h),e=p.matchType(t.type);if(!e)break;(++h>1||0==a||t.content.size)&&(p=e,c.push(function t(e,n,i){if(n<=0)return e;let s=e.content;return n>1&&(s=s.replaceChild(0,t(s.firstChild,n-1,1==s.childCount?i-1:0))),n>0&&(s=e.type.contentMatch.fillBefore(s).append(s),i<=0&&(s=s.append(e.type.contentMatch.matchFragment(s).fillBefore(r.FK.empty,!0)))),e.copy(s)}(t.mark(u.allowedMarks(t.marks)),1==h?a:0,h==l.childCount?d:-1)))}let f=h==l.childCount;f||(d=-1),this.placed=L(this.placed,e,r.FK.from(c)),this.frontier[e].match=p,f&&d<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let t=0,e=l;t<d;t++){let t=e.lastChild;this.frontier.push({type:t.type,match:t.contentMatchAt(t.childCount)}),e=t.content}this.unplaced=f?0==t?r.Ji.empty:new r.Ji(N(o.content,t-1,1),t-1,d<0?o.openEnd:t-1):new r.Ji(N(o.content,t,h),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let t=this.frontier[this.depth],e;if(!t.type.isTextblock||!F(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(t){e:for(let e=Math.min(this.depth,t.depth);e>=0;e--){let{match:n,type:r}=this.frontier[e],i=e<t.depth&&t.end(e+1)==t.pos+(t.depth-(e+1)),s=F(t,e,r,n,i);if(s){for(let n=e-1;n>=0;n--){let{match:e,type:r}=this.frontier[n],i=F(t,n,r,e,!0);if(!i||i.childCount)continue e}return{depth:e,fit:s,move:i?t.doc.resolve(t.after(e+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=L(this.placed,e.depth,e.fit)),t=e.move;for(let n=e.depth+1;n<=t.depth;n++){let e=t.node(n),r=e.type.contentMatch.fillBefore(e.content,!0,t.index(n));this.openFrontierNode(e.type,e.attrs,r)}return t}openFrontierNode(t,e=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(t),this.placed=L(this.placed,this.depth,r.FK.from(t.create(e,n))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(r.FK.empty,!0);t.childCount&&(this.placed=L(this.placed,this.frontier.length,t))}}function N(t,e,n){return 0==e?t.cutByIndex(n,t.childCount):t.replaceChild(0,t.firstChild.copy(N(t.firstChild.content,e-1,n)))}function L(t,e,n){return 0==e?t.append(n):t.replaceChild(t.childCount-1,t.lastChild.copy(L(t.lastChild.content,e-1,n)))}function z(t,e){for(let n=0;n<e;n++)t=t.firstChild.content;return t}function F(t,e,n,r,i){let s=t.node(e),o=i?t.indexAfter(e):t.index(e);if(o==s.childCount&&!n.compatibleContent(s.type))return null;let l=r.fillBefore(s.content,!0,o);return l&&!function(t,e,n){for(let r=n;r<e.childCount;r++)if(!t.allowsMarks(e.child(r).marks))return!0;return!1}(n,s.content,o)?l:null}function D(t,e){let n=[],r=Math.min(t.depth,e.depth);for(let i=r;i>=0;i--){let r=t.start(i);if(r<t.pos-(t.depth-i)||e.end(i)>e.pos+(e.depth-i)||t.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(r==e.start(i)||i==t.depth&&i==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==r-1)&&n.push(i)}return n}class P extends a{constructor(t,e,n){super(),this.pos=t,this.attr=e,this.value=n}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at attribute step's position");let n=Object.create(null);for(let t in e.attrs)n[t]=e.attrs[t];n[this.attr]=this.value;let i=e.type.create(n,null,e.marks);return h.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(i),0,+!e.isLeaf))}getMap(){return s.empty}invert(t){return new P(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new P(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if("number"!=typeof e.pos||"string"!=typeof e.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new P(e.pos,e.attr,e.value)}}a.jsonID("attr",P);class H extends a{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let n in t.attrs)e[n]=t.attrs[n];e[this.attr]=this.value;let n=t.type.create(e,t.content,t.marks);return h.ok(n)}getMap(){return s.empty}invert(t){return new H(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if("string"!=typeof e.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new H(e.attr,e.value)}}a.jsonID("docAttr",H);let B=class extends Error{};(B=function t(e){let n=Error.call(this,e);return n.__proto__=t.prototype,n}).prototype=Object.create(Error.prototype),B.prototype.constructor=B,B.prototype.name="TransformError";class j{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new o}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new B(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,n=r.Ji.empty){let i=O(this.doc,t,e,n);return i&&this.step(i),this}replaceWith(t,e,n){return this.replace(t,e,new r.Ji(r.FK.from(n),0,0))}delete(t,e){return this.replace(t,e,r.Ji.empty)}insert(t,e){return this.replaceWith(t,t,e)}replaceRange(t,e,n){return!function(t,e,n,i){if(!i.size)return t.deleteRange(e,n);let s=t.doc.resolve(e),o=t.doc.resolve(n);if(R(s,o,i))return t.step(new m(e,n,i));let l=D(s,t.doc.resolve(n));0==l[l.length-1]&&l.pop();let a=-(s.depth+1);l.unshift(a);for(let t=s.depth,e=s.pos-1;t>0;t--,e--){let n=s.node(t).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;l.indexOf(t)>-1?a=t:s.before(t)==e&&l.splice(1,0,-t)}let h=l.indexOf(a),c=[],p=i.openStart;for(let t=i.content,e=0;;e++){let n=t.firstChild;if(c.push(n),e==i.openStart)break;t=n.content}for(let t=p-1;t>=0;t--){var u;let e=c[t],n=(u=e.type).spec.defining||u.spec.definingForContent;if(n&&!e.sameMarkup(s.node(Math.abs(a)-1)))p=t;else if(n||!e.type.isTextblock)break}for(let e=i.openStart;e>=0;e--){let a=(e+p+1)%(i.openStart+1),u=c[a];if(u)for(let e=0;e<l.length;e++){let c=l[(e+h)%l.length],p=!0;c<0&&(p=!1,c=-c);let d=s.node(c-1),f=s.index(c-1);if(d.canReplaceWith(f,f,u.type,u.marks))return t.replace(s.before(c),p?o.after(c):n,new r.Ji(function t(e,n,i,s,o){if(n<i){let r=e.firstChild;e=e.replaceChild(0,r.copy(t(r.content,n+1,i,s,r)))}if(n>s){let t=o.contentMatchAt(0),n=t.fillBefore(e).append(e);e=n.append(t.matchFragment(n).fillBefore(r.FK.empty,!0))}return e}(i.content,0,i.openStart,a),a,i.openEnd))}}let d=t.steps.length;for(let r=l.length-1;r>=0&&(t.replace(e,n,i),!(t.steps.length>d));r--){let t=l[r];t<0||(e=s.before(t),n=o.after(t))}}(this,t,e,n),this}replaceRangeWith(t,e,n){var i=t,s=e;if(!n.isInline&&i==s&&this.doc.resolve(i).parent.content.size){let t=function(t,e,n){let r=t.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),n))return e;if(0==r.parentOffset)for(let t=r.depth-1;t>=0;t--){let e=r.index(t);if(r.node(t).canReplaceWith(e,e,n))return r.before(t+1);if(e>0)return null}if(r.parentOffset==r.parent.content.size)for(let t=r.depth-1;t>=0;t--){let e=r.indexAfter(t);if(r.node(t).canReplaceWith(e,e,n))return r.after(t+1);if(e<r.node(t).childCount)break}return null}(this.doc,i,n.type);null!=t&&(i=s=t)}return this.replaceRange(i,s,new r.Ji(r.FK.from(n),0,0)),this}deleteRange(t,e){return!function(t,e,n){let r=t.doc.resolve(e),i=t.doc.resolve(n),s=D(r,i);for(let e=0;e<s.length;e++){let n=s[e],o=e==s.length-1;if(o&&0==n||r.node(n).type.contentMatch.validEnd)return t.delete(r.start(n),i.end(n));if(n>0&&(o||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return t.delete(r.before(n),i.after(n))}for(let s=1;s<=r.depth&&s<=i.depth;s++)if(e-r.start(s)==r.depth-s&&n>r.end(s)&&i.end(s)-n!=i.depth-s&&r.start(s-1)==i.start(s-1)&&r.node(s-1).canReplace(r.index(s-1),i.index(s-1)))return t.delete(r.before(s),n);t.delete(e,n)}(this,t,e),this}lift(t,e){return!function(t,e,n){let{$from:i,$to:s,depth:o}=e,l=i.before(o+1),a=s.after(o+1),h=l,c=a,p=r.FK.empty,u=0;for(let t=o,e=!1;t>n;t--)e||i.index(t)>0?(e=!0,p=r.FK.from(i.node(t).copy(p)),u++):h--;let d=r.FK.empty,f=0;for(let t=o,e=!1;t>n;t--)e||s.after(t+1)<s.end(t)?(e=!0,d=r.FK.from(s.node(t).copy(d)),f++):c++;t.step(new g(h,c,l,a,new r.Ji(p.append(d),u,f),p.size-u,!0))}(this,t,e),this}join(t,e=1){return!function(t,e,n){let i=null,{linebreakReplacement:s}=t.doc.type.schema,o=t.doc.resolve(e-n),l=o.node().type;if(s&&l.inlineContent){let t="pre"==l.whitespace,e=!!l.contentMatch.matchType(s);t&&!e?i=!1:!t&&e&&(i=!0)}let a=t.steps.length;if(!1===i){let r=t.doc.resolve(e+n);S(t,r.node(),r.before(),a)}l.inlineContent&&k(t,e+n-1,l,o.node().contentMatchAt(o.index()),null==i);let h=t.mapping.slice(a),c=h.map(e-n);if(t.step(new m(c,h.map(e+n,-1),r.Ji.empty,!0)),!0===i){let e=t.doc.resolve(c);x(t,e.node(),e.before(),t.steps.length)}}(this,t,e),this}wrap(t,e){return!function(t,e,n){let i=r.FK.empty;for(let t=n.length-1;t>=0;t--){if(i.size){let e=n[t].type.contentMatch.matchFragment(i);if(!e||!e.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=r.FK.from(n[t].type.create(n[t].attrs,i))}let s=e.start,o=e.end;t.step(new g(s,o,s,o,new r.Ji(i,0,0),n.length,!0))}(this,t,e),this}setBlockType(t,e=t,n,i=null){var s=this;if(!n.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let o=s.steps.length;return s.doc.nodesBetween(t,e,(t,e)=>{var l,a,h;let c,p,u="function"==typeof i?i(t):i;if(t.isTextblock&&!t.hasMarkup(n,u)&&(l=s.doc,a=s.mapping.slice(o).map(e),h=n,p=(c=l.resolve(a)).index(),c.parent.canReplaceWith(p,p+1,h))){let i=null;if(n.schema.linebreakReplacement){let t="pre"==n.whitespace,e=!!n.contentMatch.matchType(n.schema.linebreakReplacement);t&&!e?i=!1:!t&&e&&(i=!0)}!1===i&&S(s,t,e,o),k(s,s.mapping.slice(o).map(e,1),n,void 0,null===i);let l=s.mapping.slice(o),a=l.map(e,1),h=l.map(e+t.nodeSize,1);return s.step(new g(a,h,a+1,h-1,new r.Ji(r.FK.from(n.create(u,null,t.marks)),0,0),1,!0)),!0===i&&x(s,t,e,o),!1}}),this}setNodeMarkup(t,e,n=null,i){return!function(t,e,n,i,s){let o=t.doc.nodeAt(e);if(!o)throw RangeError("No node at given position");n||(n=o.type);let l=n.create(i,null,s||o.marks);if(o.isLeaf)return t.replaceWith(e,e+o.nodeSize,l);if(!n.validContent(o.content))throw RangeError("Invalid content for node type "+n.name);t.step(new g(e,e+o.nodeSize,e+1,e+o.nodeSize-1,new r.Ji(r.FK.from(l),0,0),1,!0))}(this,t,e,n,i),this}setNodeAttribute(t,e,n){return this.step(new P(t,e,n)),this}setDocAttribute(t,e){return this.step(new H(t,e)),this}addNodeMark(t,e){return this.step(new d(t,e)),this}removeNodeMark(t,e){let n=this.doc.nodeAt(t);if(!n)throw RangeError("No node at position "+t);if(e instanceof r.CU)e.isInSet(n.marks)&&this.step(new f(t,e));else{let r=n.marks,i,s=[];for(;i=e.isInSet(r);)s.push(new f(t,i)),r=i.removeFromSet(r);for(let t=s.length-1;t>=0;t--)this.step(s[t])}return this}split(t,e=1,n){return!function(t,e,n=1,i){let s=t.doc.resolve(e),o=r.FK.empty,l=r.FK.empty;for(let t=s.depth,e=s.depth-n,a=n-1;t>e;t--,a--){o=r.FK.from(s.node(t).copy(o));let e=i&&i[a];l=r.FK.from(e?e.type.create(e.attrs,l):s.node(t).copy(l))}t.step(new m(e,e,new r.Ji(o.append(l),n,n),!0))}(this,t,e,n),this}addMark(t,e,n){var r;let i,s,o,l;return r=this,o=[],l=[],r.doc.nodesBetween(t,e,(r,a,h)=>{if(!r.isInline)return;let c=r.marks;if(!n.isInSet(c)&&h.type.allowsMarkType(n.type)){let h=Math.max(a,t),d=Math.min(a+r.nodeSize,e),f=n.addToSet(c);for(let t=0;t<c.length;t++)c[t].isInSet(f)||(i&&i.to==h&&i.mark.eq(c[t])?i.to=d:o.push(i=new u(h,d,c[t])));s&&s.to==h?s.to=d:l.push(s=new p(h,d,n))}}),o.forEach(t=>r.step(t)),l.forEach(t=>r.step(t)),this}removeMark(t,e,n){var i;let s,o;return i=this,s=[],o=0,i.doc.nodesBetween(t,e,(i,l)=>{if(!i.isInline)return;o++;let a=null;if(n instanceof r.sX){let t=i.marks,e;for(;e=n.isInSet(t);)(a||(a=[])).push(e),t=e.removeFromSet(t)}else n?n.isInSet(i.marks)&&(a=[n]):a=i.marks;if(a&&a.length){let n=Math.min(l+i.nodeSize,e);for(let e=0;e<a.length;e++){let r=a[e],i;for(let t=0;t<s.length;t++){let e=s[t];e.step==o-1&&r.eq(s[t].style)&&(i=e)}i?(i.to=n,i.step=o):s.push({style:r,from:Math.max(l,t),to:n,step:o})}}}),s.forEach(t=>i.step(new u(t.from,t.to,t.style))),this}clearIncompatible(t,e,n){return k(this,t,e,n),this}}},968:(t,e,n)=>{n.d(e,{b:()=>l});var r=n(2115),i=n(3655),s=n(5155),o=r.forwardRef((t,e)=>(0,s.jsx)(i.sG.label,{...t,ref:e,onMouseDown:e=>{var n;e.target.closest("button, input, select, textarea")||(null==(n=t.onMouseDown)||n.call(t,e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));o.displayName="Label";var l=o},1154:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1514:(t,e,n)=>{n.d(e,{$B:()=>c,Sd:()=>a,T2:()=>h});var r=n(808),i=n(156);let s=["ol",0],o=["ul",0],l=["li",0];function a(t,e=null){return function(n,s){let{$from:o,$to:l}=n.selection,a=o.blockRange(l);if(!a)return!1;let h=s?n.tr:null;return!!function(t,e,n,s=null){let o=!1,l=e,a=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(n)&&0==e.startIndex){if(0==e.$from.index(e.depth-1))return!1;let t=a.resolve(e.start-2);l=new i.u$(t,t,e.depth),e.endIndex<e.parent.childCount&&(e=new i.u$(e.$from,a.resolve(e.$to.end(e.depth)),e.depth)),o=!0}let h=(0,r.oM)(l,n,s,e);return!!h&&(t&&function(t,e,n,s,o){let l=i.FK.empty;for(let t=n.length-1;t>=0;t--)l=i.FK.from(n[t].type.create(n[t].attrs,l));t.step(new r.Wg(e.start-2*!!s,e.end,e.start,e.end,new i.Ji(l,0,0),n.length,!0));let a=0;for(let t=0;t<n.length;t++)n[t].type==o&&(a=t+1);let h=n.length-a,c=e.start+n.length-2*!!s,p=e.parent;for(let n=e.startIndex,i=e.endIndex,s=!0;n<i;n++,s=!1)!s&&(0,r.zy)(t.doc,c,h)&&(t.split(c,h),c+=2*h),c+=p.child(n).nodeSize}(t,e,h,o,n),!0)}(h,a,t,e)&&(s&&s(h.scrollIntoView()),!0)}}function h(t){return function(e,n){let{$from:s,$to:o}=e.selection,l=s.blockRange(o,e=>e.childCount>0&&e.firstChild.type==t);return!!l&&(!n||(s.node(l.depth-1).type==t?function(t,e,n,s){let o=t.tr,l=s.end,a=s.$to.end(s.depth);l<a&&(o.step(new r.Wg(l-1,a,l,a,new i.Ji(i.FK.from(n.create(null,s.parent.copy())),1,0),1,!0)),s=new i.u$(o.doc.resolve(s.$from.pos),o.doc.resolve(a),s.depth));let h=(0,r.jP)(s);if(null==h)return!1;o.lift(s,h);let c=o.doc.resolve(o.mapping.map(l,-1)-1);return(0,r.n9)(o.doc,c.pos)&&c.nodeBefore.type==c.nodeAfter.type&&o.join(c.pos),e(o.scrollIntoView()),!0}(e,n,t,l):function(t,e,n){let s=t.tr,o=n.parent;for(let t=n.end,e=n.endIndex-1,r=n.startIndex;e>r;e--)t-=o.child(e).nodeSize,s.delete(t-1,t+1);let l=s.doc.resolve(n.start),a=l.nodeAfter;if(s.mapping.map(n.end)!=n.start+l.nodeAfter.nodeSize)return!1;let h=0==n.startIndex,c=n.endIndex==o.childCount,p=l.node(-1),u=l.index(-1);if(!p.canReplace(u+ +!h,u+1,a.content.append(c?i.FK.empty:i.FK.from(o))))return!1;let d=l.pos,f=d+a.nodeSize;return s.step(new r.Wg(d-!!h,f+ +!!c,d+1,f-1,new i.Ji((h?i.FK.empty:i.FK.from(o.copy(i.FK.empty))).append(c?i.FK.empty:i.FK.from(o.copy(i.FK.empty))),+!h,+!c),+!h)),e(s.scrollIntoView()),!0}(e,n,l)))}}function c(t){return function(e,n){let{$from:s,$to:o}=e.selection,l=s.blockRange(o,e=>e.childCount>0&&e.firstChild.type==t);if(!l)return!1;let a=l.startIndex;if(0==a)return!1;let h=l.parent,c=h.child(a-1);if(c.type!=t)return!1;if(n){let s=c.lastChild&&c.lastChild.type==h.type,o=i.FK.from(s?t.create():null),a=new i.Ji(i.FK.from(t.create(null,i.FK.from(h.type.create(null,o)))),s?3:1,0),p=l.start,u=l.end;n(e.tr.step(new r.Wg(p-(s?3:1),u,p,u,a,1,!0)).scrollIntoView())}return!0}}},1891:(t,e,n)=>{n.d(e,{Ay:()=>s});var r=n(4701);let i=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,s=r.bP.create({name:"image",addOptions:()=>({inline:!1,allowBase64:!1,HTMLAttributes:{}}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes:()=>({src:{default:null},alt:{default:null},title:{default:null}}),parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:t}){return["img",(0,r.KV)(this.options.HTMLAttributes,t)]},addCommands(){return{setImage:t=>({commands:e})=>e.insertContent({type:this.name,attrs:t})}},addInputRules(){return[(0,r.jT)({find:i,type:this.type,getAttributes:t=>{let[,,e,n,r]=t;return{src:n,alt:e,title:r}}})]}})},2406:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("heading-3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},2571:(t,e,n)=>{n.d(e,{$t:()=>S,LN:()=>o,U3:()=>c,hs:()=>T,i5:()=>f,k_:()=>M,nh:()=>u});var r=n(156),i=n(808);let s=Object.create(null);class o{constructor(t,e,n){this.$anchor=t,this.$head=e,this.ranges=n||[new l(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let e=0;e<t.length;e++)if(t[e].$from.pos!=t[e].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(t,e=r.Ji.empty){let n=e.content.lastChild,i=null;for(let t=0;t<e.openEnd;t++)i=n,n=n.lastChild;let s=t.steps.length,o=this.ranges;for(let l=0;l<o.length;l++){let{$from:a,$to:h}=o[l],c=t.mapping.slice(s);t.replaceRange(c.map(a.pos),c.map(h.pos),l?r.Ji.empty:e),0==l&&y(t,s,(n?n.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(t,e){let n=t.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:s,$to:o}=r[i],l=t.mapping.slice(n),a=l.map(s.pos),h=l.map(o.pos);i?t.deleteRange(a,h):(t.replaceRangeWith(a,h,e),y(t,n,e.isInline?-1:1))}}static findFrom(t,e,n=!1){let r=t.parent.inlineContent?new c(t):g(t.node(0),t.parent,t.pos,t.index(),e,n);if(r)return r;for(let r=t.depth-1;r>=0;r--){let i=e<0?g(t.node(0),t.node(r),t.before(r+1),t.index(r),e,n):g(t.node(0),t.node(r),t.after(r+1),t.index(r)+1,e,n);if(i)return i}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new f(t.node(0))}static atStart(t){return g(t,t,0,0,1)||new f(t)}static atEnd(t){return g(t,t,t.content.size,t.childCount,-1)||new f(t)}static fromJSON(t,e){if(!e||!e.type)throw RangeError("Invalid input for Selection.fromJSON");let n=s[e.type];if(!n)throw RangeError(`No selection type ${e.type} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in s)throw RangeError("Duplicate use of selection JSON ID "+t);return s[t]=e,e.prototype.jsonID=t,e}getBookmark(){return c.between(this.$anchor,this.$head).getBookmark()}}o.prototype.visible=!0;class l{constructor(t,e){this.$from=t,this.$to=e}}let a=!1;function h(t){a||t.parent.inlineContent||(a=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}class c extends o{constructor(t,e=t){h(t),h(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let n=t.resolve(e.map(this.head));if(!n.parent.inlineContent)return o.near(n);let r=t.resolve(e.map(this.anchor));return new c(r.parent.inlineContent?r:n,n)}replace(t,e=r.Ji.empty){if(super.replace(t,e),e==r.Ji.empty){let e=this.$from.marksAcross(this.$to);e&&t.ensureMarks(e)}}eq(t){return t instanceof c&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new p(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if("number"!=typeof e.anchor||"number"!=typeof e.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new c(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,n=e){let r=t.resolve(e);return new this(r,n==e?r:t.resolve(n))}static between(t,e,n){let r=t.pos-e.pos;if((!n||r)&&(n=r>=0?1:-1),!e.parent.inlineContent){let t=o.findFrom(e,n,!0)||o.findFrom(e,-n,!0);if(!t)return o.near(e,n);e=t.$head}return t.parent.inlineContent||(0==r?t=e:(t=(o.findFrom(t,-n,!0)||o.findFrom(t,n,!0)).$anchor).pos<e.pos!=r<0&&(t=e)),new c(t,e)}}o.jsonID("text",c);class p{constructor(t,e){this.anchor=t,this.head=e}map(t){return new p(t.map(this.anchor),t.map(this.head))}resolve(t){return c.between(t.resolve(this.anchor),t.resolve(this.head))}}class u extends o{constructor(t){let e=t.nodeAfter;super(t,t.node(0).resolve(t.pos+e.nodeSize)),this.node=e}map(t,e){let{deleted:n,pos:r}=e.mapResult(this.anchor),i=t.resolve(r);return n?o.near(i):new u(i)}content(){return new r.Ji(r.FK.from(this.node),0,0)}eq(t){return t instanceof u&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new d(this.anchor)}static fromJSON(t,e){if("number"!=typeof e.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new u(t.resolve(e.anchor))}static create(t,e){return new u(t.resolve(e))}static isSelectable(t){return!t.isText&&!1!==t.type.spec.selectable}}u.prototype.visible=!1,o.jsonID("node",u);class d{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:n}=t.mapResult(this.anchor);return e?new p(n,n):new d(n)}resolve(t){let e=t.resolve(this.anchor),n=e.nodeAfter;return n&&u.isSelectable(n)?new u(e):o.near(e)}}class f extends o{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(t,e=r.Ji.empty){if(e==r.Ji.empty){t.delete(0,t.doc.content.size);let e=o.atStart(t.doc);e.eq(t.selection)||t.setSelection(e)}else super.replace(t,e)}toJSON(){return{type:"all"}}static fromJSON(t){return new f(t)}map(t){return new f(t)}eq(t){return t instanceof f}getBookmark(){return m}}o.jsonID("all",f);let m={map(){return this},resolve:t=>new f(t)};function g(t,e,n,r,i,s=!1){if(e.inlineContent)return c.create(t,n);for(let o=r-(i>0?0:1);i>0?o<e.childCount:o>=0;o+=i){let r=e.child(o);if(r.isAtom){if(!s&&u.isSelectable(r))return u.create(t,n-(i<0?r.nodeSize:0))}else{let e=g(t,r,n+i,i<0?r.childCount:0,i,s);if(e)return e}n+=r.nodeSize*i}return null}function y(t,e,n){let r,s=t.steps.length-1;if(s<e)return;let l=t.steps[s];(l instanceof i.Ln||l instanceof i.Wg)&&(t.mapping.maps[s].forEach((t,e,n,i)=>{null==r&&(r=i)}),t.setSelection(o.near(t.doc.resolve(r),n)))}class k extends i.dL{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=2,this}ensureMarks(t){return r.CU.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(t){return this.ensureMarks(t.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(t){return this.ensureMarks(t.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(t,e){super.addStep(t,e),this.updated=-3&this.updated,this.storedMarks=null}setTime(t){return this.time=t,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let n=this.selection;return e&&(t=t.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.CU.none))),n.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(t,e,n){let r=this.doc.type.schema;if(null==e)return t?this.replaceSelectionWith(r.text(t),!0):this.deleteSelection();{if(null==n&&(n=e),n=null==n?e:n,!t)return this.deleteRange(e,n);let i=this.storedMarks;if(!i){let t=this.doc.resolve(e);i=n==e?t.marks():t.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(e,n,r.text(t,i)),this.selection.empty||this.setSelection(o.near(this.selection.$to)),this}}setMeta(t,e){return this.meta["string"==typeof t?t:t.key]=e,this}getMeta(t){return this.meta["string"==typeof t?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function v(t,e){return e&&t?t.bind(e):t}class w{constructor(t,e,n){this.name=t,this.init=v(e.init,n),this.apply=v(e.apply,n)}}let b=[new w("doc",{init:t=>t.doc||t.schema.topNodeType.createAndFill(),apply:t=>t.doc}),new w("selection",{init:(t,e)=>t.selection||o.atStart(e.doc),apply:t=>t.selection}),new w("storedMarks",{init:t=>t.storedMarks||null,apply:(t,e,n,r)=>r.selection.$cursor?t.storedMarks:null}),new w("scrollToSelection",{init:()=>0,apply:(t,e)=>t.scrolledIntoView?e+1:e})];class x{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=b.slice(),e&&e.forEach(t=>{if(this.pluginsByKey[t.key])throw RangeError("Adding different instances of a keyed plugin ("+t.key+")");this.plugins.push(t),this.pluginsByKey[t.key]=t,t.spec.state&&this.fields.push(new w(t.key,t.spec.state,t))})}}class S{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(t){return this.applyTransaction(t).state}filterTransaction(t,e=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=e){let e=this.config.plugins[n];if(e.spec.filterTransaction&&!e.spec.filterTransaction.call(e,t,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],n=this.applyInner(t),r=null;for(;;){let i=!1;for(let s=0;s<this.config.plugins.length;s++){let o=this.config.plugins[s];if(o.spec.appendTransaction){let l=r?r[s].n:0,a=r?r[s].state:this,h=l<e.length&&o.spec.appendTransaction.call(o,l?e.slice(l):e,a,n);if(h&&n.filterTransaction(h,s)){if(h.setMeta("appendedTransaction",t),!r){r=[];for(let t=0;t<this.config.plugins.length;t++)r.push(t<s?{state:n,n:e.length}:{state:this,n:0})}e.push(h),n=n.applyInner(h),i=!0}r&&(r[s]={state:n,n:e.length})}}if(!i)return{state:n,transactions:e}}}applyInner(t){if(!t.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let e=new S(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];e[i.name]=i.apply(t,this[i.name],this,e)}return e}get tr(){return new k(this)}static create(t){let e=new x(t.doc?t.doc.type.schema:t.schema,t.plugins),n=new S(e);for(let r=0;r<e.fields.length;r++)n[e.fields[r].name]=e.fields[r].init(t,n);return n}reconfigure(t){let e=new x(this.schema,t.plugins),n=e.fields,r=new S(e);for(let e=0;e<n.length;e++){let i=n[e].name;r[i]=this.hasOwnProperty(i)?this[i]:n[e].init(t,r)}return r}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map(t=>t.toJSON())),t&&"object"==typeof t)for(let n in t){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=t[n],i=r.spec.state;i&&i.toJSON&&(e[n]=i.toJSON.call(r,this[r.key]))}return e}static fromJSON(t,e,n){if(!e)throw RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw RangeError("Required config field 'schema' missing");let i=new x(t.schema,t.plugins),s=new S(i);return i.fields.forEach(i=>{if("doc"==i.name)s.doc=r.bP.fromJSON(t.schema,e.doc);else if("selection"==i.name)s.selection=o.fromJSON(s.doc,e.selection);else if("storedMarks"==i.name)e.storedMarks&&(s.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(n)for(let r in n){let o=n[r],l=o.spec.state;if(o.key==i.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(e,r)){s[i.name]=l.fromJSON.call(o,t,e[r],s);return}}s[i.name]=i.init(t,s)}}),s}}class M{constructor(t){this.spec=t,this.props={},t.props&&function t(e,n,r){for(let i in e){let s=e[i];s instanceof Function?s=s.bind(n):"handleDOMEvents"==i&&(s=t(s,n,{})),r[i]=s}return r}(t.props,this,this.props),this.key=t.key?t.key.key:C("plugin")}getState(t){return t[this.key]}}let A=Object.create(null);function C(t){return t in A?t+"$"+ ++A[t]:(A[t]=0,t+"$")}class T{constructor(t="key"){this.key=C(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}},2643:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},2705:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("heading-2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},3654:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},4109:(t,e,n)=>{n.d(e,{A:()=>r});let r=n(4701).YY.create({name:"textAlign",addOptions:()=>({types:[],alignments:["left","center","right","justify"],defaultAlignment:null}),addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:t=>{let e=t.style.textAlign;return this.options.alignments.includes(e)?e:this.options.defaultAlignment},renderHTML:t=>t.textAlign?{style:`text-align: ${t.textAlign}`}:{}}}}]},addCommands(){return{setTextAlign:t=>({commands:e})=>!!this.options.alignments.includes(t)&&this.options.types.map(n=>e.updateAttributes(n,{textAlign:t})).every(t=>t),unsetTextAlign:()=>({commands:t})=>this.options.types.map(e=>t.resetAttributes(e,"textAlign")).every(t=>t),toggleTextAlign:t=>({editor:e,commands:n})=>!!this.options.alignments.includes(t)&&(e.isActive({textAlign:t})?n.unsetTextAlign():n.setTextAlign(t))}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}})},4229:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4589:(t,e,n)=>{n.d(e,{A:()=>r}),n(6377);let r=n(4701).YY.create({name:"color",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:t=>{var e;return null==(e=t.style.color)?void 0:e.replace(/['"]+/g,"")},renderHTML:t=>t.color?{style:`color: ${t.color}`}:{}}}}]},addCommands:()=>({setColor:t=>({chain:e})=>e().setMark("textStyle",{color:t}).run(),unsetColor:()=>({chain:t})=>t().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()})})},4652:(t,e,n)=>{n.d(e,{Ay:()=>o});var r=n(4701);let i=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,s=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,o=r.CU.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:t}){return["mark",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[(0,r.OX)({find:i,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:s,type:this.type})]}})},5109:(t,e,n)=>{n.d(e,{$Z:()=>m,hG:()=>A});var r,i,s=n(2115),o=n(7650),l=n(4701),a={exports:{}},h={};a.exports=function(){if(r)return h;r=1;var t="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},e=s.useState,n=s.useEffect,i=s.useLayoutEffect,o=s.useDebugValue;function l(e){var n=e.getSnapshot;e=e.value;try{var r=n();return!t(e,r)}catch(t){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,r){var s=r(),a=e({inst:{value:s,getSnapshot:r}}),h=a[0].inst,c=a[1];return i(function(){h.value=s,h.getSnapshot=r,l(h)&&c({inst:h})},[t,s,r]),n(function(){return l(h)&&c({inst:h}),t(function(){l(h)&&c({inst:h})})},[t]),o(s),s};return h.useSyncExternalStore=void 0!==s.useSyncExternalStore?s.useSyncExternalStore:a,h}();var c=a.exports;let p=(...t)=>e=>{t.forEach(t=>{"function"==typeof t?t(e):t&&(t.current=e)})},u=({contentComponent:t})=>{let e=c.useSyncExternalStore(t.subscribe,t.getSnapshot,t.getServerSnapshot);return s.createElement(s.Fragment,null,Object.values(e))};class d extends s.Component{constructor(t){var e;super(t),this.editorContentRef=s.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(e=t.editor)?void 0:e.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let t=this.props.editor;if(t&&!t.isDestroyed&&t.options.element){if(t.contentComponent)return;let e=this.editorContentRef.current;e.append(...t.options.element.childNodes),t.setOptions({element:e}),t.contentComponent=function(){let t=new Set,e={};return{subscribe:e=>(t.add(e),()=>{t.delete(e)}),getSnapshot:()=>e,getServerSnapshot:()=>e,setRenderer(n,r){e={...e,[n]:o.createPortal(r.reactElement,r.element,n)},t.forEach(t=>t())},removeRenderer(n){let r={...e};delete r[n],e=r,t.forEach(t=>t())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=t.contentComponent.subscribe(()=>{this.setState(t=>t.hasContentComponentInitialized?t:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),t.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let t=this.props.editor;if(!t||(this.initialized=!1,t.isDestroyed||t.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),t.contentComponent=null,!t.options.element.firstChild))return;let e=document.createElement("div");e.append(...t.options.element.childNodes),t.setOptions({element:e})}render(){let{editor:t,innerRef:e,...n}=this.props;return s.createElement(s.Fragment,null,s.createElement("div",{ref:p(e,this.editorContentRef),...n}),(null==t?void 0:t.contentComponent)&&s.createElement(u,{contentComponent:t.contentComponent}))}}let f=(0,s.forwardRef)((t,e)=>{let n=s.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[t.editor]);return s.createElement(d,{key:n,innerRef:e,...t})}),m=s.memo(f);var g=function(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}(function t(e,n){if(e===n)return!0;if(e&&n&&"object"==typeof e&&"object"==typeof n){if(e.constructor!==n.constructor)return!1;if(Array.isArray(e)){if((r=e.length)!=n.length)return!1;for(i=r;0!=i--;)if(!t(e[i],n[i]))return!1;return!0}if(e instanceof Map&&n instanceof Map){if(e.size!==n.size)return!1;for(i of e.entries())if(!n.has(i[0]))return!1;for(i of e.entries())if(!t(i[1],n.get(i[0])))return!1;return!0}if(e instanceof Set&&n instanceof Set){if(e.size!==n.size)return!1;for(i of e.entries())if(!n.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(n)){if((r=e.length)!=n.length)return!1;for(i=r;0!=i--;)if(e[i]!==n[i])return!1;return!0}if(e.constructor===RegExp)return e.source===n.source&&e.flags===n.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===n.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===n.toString();if((r=(s=Object.keys(e)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,s[i]))return!1;for(i=r;0!=i--;){var r,i,s,o=s[i];if(("_owner"!==o||!e.$$typeof)&&!t(e[o],n[o]))return!1}return!0}return e!=e&&n!=n}),y={exports:{}},k={};y.exports=function(){if(i)return k;i=1;var t="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},e=c.useSyncExternalStore,n=s.useRef,r=s.useEffect,o=s.useMemo,l=s.useDebugValue;return k.useSyncExternalStoreWithSelector=function(i,s,a,h,c){var p=n(null);if(null===p.current){var u={hasValue:!1,value:null};p.current=u}else u=p.current;var d=e(i,(p=o(function(){function e(e){if(!i){if(i=!0,n=e,e=h(e),void 0!==c&&u.hasValue){var s=u.value;if(c(s,e))return r=s}return r=e}if(s=r,t(n,e))return s;var o=h(e);return void 0!==c&&c(s,o)?s:(n=e,r=o)}var n,r,i=!1,o=void 0===a?null:a;return[function(){return e(s())},null===o?void 0:function(){return e(o())}]},[s,a,h,c]))[0],p[1]);return r(function(){u.hasValue=!0,u.value=d},[d]),l(d),d},k}();var v=y.exports;let w="undefined"!=typeof window?s.useLayoutEffect:s.useEffect;class b{constructor(t){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=t,this.lastSnapshot={editor:t,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(t){return this.subscribers.add(t),()=>{this.subscribers.delete(t)}}watch(t){if(this.editor=t,this.editor){let t=()=>{this.transactionNumber+=1,this.subscribers.forEach(t=>t())},e=this.editor;return e.on("transaction",t),()=>{e.off("transaction",t)}}}}let x="undefined"==typeof window,S=x||!!("undefined"!=typeof window&&window.next);class M{constructor(t){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=t,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(t){this.editor=t,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(t=>t())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?x||S?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){let t={...this.options.current,onBeforeCreate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onBeforeCreate)?void 0:n.call(e,...t)},onBlur:(...t)=>{var e,n;return null==(n=(e=this.options.current).onBlur)?void 0:n.call(e,...t)},onCreate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onCreate)?void 0:n.call(e,...t)},onDestroy:(...t)=>{var e,n;return null==(n=(e=this.options.current).onDestroy)?void 0:n.call(e,...t)},onFocus:(...t)=>{var e,n;return null==(n=(e=this.options.current).onFocus)?void 0:n.call(e,...t)},onSelectionUpdate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onSelectionUpdate)?void 0:n.call(e,...t)},onTransaction:(...t)=>{var e,n;return null==(n=(e=this.options.current).onTransaction)?void 0:n.call(e,...t)},onUpdate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onUpdate)?void 0:n.call(e,...t)},onContentError:(...t)=>{var e,n;return null==(n=(e=this.options.current).onContentError)?void 0:n.call(e,...t)},onDrop:(...t)=>{var e,n;return null==(n=(e=this.options.current).onDrop)?void 0:n.call(e,...t)},onPaste:(...t)=>{var e,n;return null==(n=(e=this.options.current).onPaste)?void 0:n.call(e,...t)}};return new l.KE(t)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(t){return this.subscriptions.add(t),()=>{this.subscriptions.delete(t)}}static compareOptions(t,e){return Object.keys(t).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&t.extensions&&e.extensions?t.extensions.length===e.extensions.length&&t.extensions.every((t,n)=>{var r;return t===(null==(r=e.extensions)?void 0:r[n])}):t[n]===e[n]))}onRender(t){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===t.length?M.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(t),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(t){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=t;return}if(this.previousDeps.length===t.length&&this.previousDeps.every((e,n)=>e===t[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=t}scheduleDestroy(){let t=this.instanceId,e=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===t){e&&e.setOptions(this.options.current);return}e&&!e.isDestroyed&&(e.destroy(),this.instanceId===t&&this.setEditor(null))},1)}}function A(t={},e=[]){let n=(0,s.useRef)(t);n.current=t;let[r]=(0,s.useState)(()=>new M(n)),i=c.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return(0,s.useDebugValue)(i),(0,s.useEffect)(r.onRender(e)),!function(t){var e;let[n]=(0,s.useState)(()=>new b(t.editor)),r=v.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,t.selector,null!=(e=t.equalityFn)?e:g);w(()=>n.watch(t.editor),[t.editor,n]),(0,s.useDebugValue)(r)}({editor:i,selector:({transactionNumber:e})=>!1===t.shouldRerenderOnTransaction?null:t.immediatelyRender&&0===e?0:e+1}),i}let C=((0,s.createContext)({editor:null}).Consumer,(0,s.createContext)({onDragStart:void 0})),T=()=>(0,s.useContext)(C);s.forwardRef((t,e)=>{let{onDragStart:n}=T(),r=t.as||"div";return s.createElement(r,{...t,ref:e,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...t.style}})});class E{constructor(t,{editor:e,props:n={},as:r="div",className:i=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=t,this.editor=e,this.props=n,this.element=document.createElement(r),this.element.classList.add("react-renderer"),i&&this.element.classList.add(...i.split(" ")),this.editor.isInitialized?flushSync(()=>{this.render()}):this.render()}render(){var t,e;let n=this.component,r=this.props,i=this.editor;("function"==typeof n&&n.prototype&&n.prototype.isReactComponent||"object"==typeof n&&(null==(e=n.$$typeof)?void 0:e.toString())==="Symbol(react.forward_ref)")&&(r.ref=t=>{this.ref=t}),this.reactElement=React.createElement(n,{...r}),null==(t=null==i?void 0:i.contentComponent)||t.setRenderer(this.id,this)}updateProps(t={}){this.props={...this.props,...t},this.render()}destroy(){var t;let e=this.editor;null==(t=null==e?void 0:e.contentComponent)||t.removeRenderer(this.id)}updateAttributes(t){Object.keys(t).forEach(e=>{this.element.setAttribute(e,t[e])})}}},5968:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6377:(t,e,n)=>{n.d(e,{A:()=>s});var r=n(4701);let i=t=>{if(!t.children.length)return;let e=t.querySelectorAll("span");e&&e.forEach(t=>{var e,n;let r=t.getAttribute("style"),i=null==(n=null==(e=t.parentElement)?void 0:e.closest("span"))?void 0:n.getAttribute("style");t.setAttribute("style",`${i};${r}`)})},s=r.CU.create({name:"textStyle",priority:101,addOptions:()=>({HTMLAttributes:{},mergeNestedSpanStyles:!1}),parseHTML(){return[{tag:"span",getAttrs:t=>!!t.hasAttribute("style")&&(this.options.mergeNestedSpanStyles&&i(t),{})}]},renderHTML({HTMLAttributes:t}){return["span",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{removeEmptyTextStyle:()=>({tr:t})=>{let{selection:e}=t;return t.doc.nodesBetween(e.from,e.to,(e,n)=>{if(e.isTextblock)return!0;e.marks.filter(t=>t.type===this.type).some(t=>Object.values(t.attrs).some(t=>!!t))||t.removeMark(n,n+e.nodeSize,this.type)}),!0}}}})},6761:(t,e,n)=>{n.d(e,{Ay:()=>tK});var r=n(4701);let i=(t,e)=>{for(let n in e)t[n]=e[n];return t},s="numeric",o="ascii",l="alpha",a="asciinumeric",h="alphanumeric",c="domain",p="emoji",u="whitespace";function d(t,e,n){for(let r in e[s]&&(e[a]=!0,e[h]=!0),e[o]&&(e[a]=!0,e[l]=!0),e[a]&&(e[h]=!0),e[l]&&(e[h]=!0),e[h]&&(e[c]=!0),e[p]&&(e[c]=!0),e){let e=(r in n||(n[r]=[]),n[r]);0>e.indexOf(t)&&e.push(t)}}function f(t=null){this.j={},this.jr=[],this.jd=null,this.t=t}f.groups={},f.prototype={accepts(){return!!this.t},go(t){let e=this.j[t];if(e)return e;for(let e=0;e<this.jr.length;e++){let n=this.jr[e][0],r=this.jr[e][1];if(r&&n.test(t))return r}return this.jd},has(t,e=!1){return e?t in this.j:!!this.go(t)},ta(t,e,n,r){for(let i=0;i<t.length;i++)this.tt(t[i],e,n,r)},tr(t,e,n,r){let i;return r=r||f.groups,e&&e.j?i=e:(i=new f(e),n&&r&&d(e,n,r)),this.jr.push([t,i]),i},ts(t,e,n,r){let i=this,s=t.length;if(!s)return i;for(let e=0;e<s-1;e++)i=i.tt(t[e]);return i.tt(t[s-1],e,n,r)},tt(t,e,n,r){if(r=r||f.groups,e&&e.j)return this.j[t]=e,e;let s,o=this.go(t);return o?(i((s=new f).j,o.j),s.jr.push.apply(s.jr,o.jr),s.jd=o.jd,s.t=o.t):s=new f,e&&(r&&(s.t&&"string"==typeof s.t?d(e,i(function(t,e){let n={};for(let r in e)e[r].indexOf(t)>=0&&(n[r]=!0);return n}(s.t,r),n),r):n&&d(e,n,r)),s.t=e),this.j[t]=s,s}};let m=(t,e,n,r,i)=>t.ta(e,n,r,i),g=(t,e,n,r,i)=>t.tr(e,n,r,i),y=(t,e,n,r,i)=>t.ts(e,n,r,i),k=(t,e,n,r,i)=>t.tt(e,n,r,i),v="WORD",w="UWORD",b="ASCIINUMERICAL",x="ALPHANUMERICAL",S="LOCALHOST",M="UTLD",A="SCHEME",C="SLASH_SCHEME",T="OPENBRACE",E="CLOSEBRACE",O="OPENBRACKET",R="CLOSEBRACKET",I="OPENPAREN",N="CLOSEPAREN",L="OPENANGLEBRACKET",z="CLOSEANGLEBRACKET",F="FULLWIDTHLEFTPAREN",D="FULLWIDTHRIGHTPAREN",P="LEFTCORNERBRACKET",H="RIGHTCORNERBRACKET",B="LEFTWHITECORNERBRACKET",j="RIGHTWHITECORNERBRACKET",J="FULLWIDTHLESSTHAN",$="FULLWIDTHGREATERTHAN",K="AMPERSAND",V="APOSTROPHE",_="ASTERISK",W="BACKSLASH",U="BACKTICK",q="CARET",G="COLON",Y="COMMA",X="DOLLAR",Q="EQUALS",Z="EXCLAMATION",tt="HYPHEN",te="PERCENT",tn="PIPE",tr="PLUS",ti="POUND",ts="QUERY",to="QUOTE",tl="FULLWIDTHMIDDLEDOT",ta="SEMI",th="SLASH",tc="TILDE",tp="UNDERSCORE",tu="EMOJI";var td=Object.freeze({__proto__:null,ALPHANUMERICAL:x,AMPERSAND:K,APOSTROPHE:V,ASCIINUMERICAL:b,ASTERISK:_,AT:"AT",BACKSLASH:W,BACKTICK:U,CARET:q,CLOSEANGLEBRACKET:z,CLOSEBRACE:E,CLOSEBRACKET:R,CLOSEPAREN:N,COLON:G,COMMA:Y,DOLLAR:X,DOT:"DOT",EMOJI:tu,EQUALS:Q,EXCLAMATION:Z,FULLWIDTHGREATERTHAN:$,FULLWIDTHLEFTPAREN:F,FULLWIDTHLESSTHAN:J,FULLWIDTHMIDDLEDOT:tl,FULLWIDTHRIGHTPAREN:D,HYPHEN:tt,LEFTCORNERBRACKET:P,LEFTWHITECORNERBRACKET:B,LOCALHOST:S,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:L,OPENBRACE:T,OPENBRACKET:O,OPENPAREN:I,PERCENT:te,PIPE:tn,PLUS:tr,POUND:ti,QUERY:ts,QUOTE:to,RIGHTCORNERBRACKET:H,RIGHTWHITECORNERBRACKET:j,SCHEME:A,SEMI:ta,SLASH:th,SLASH_SCHEME:C,SYM:"SYM",TILDE:tc,TLD:"TLD",UNDERSCORE:tp,UTLD:M,UWORD:w,WORD:v,WS:"WS"});let tf=/[a-z]/,tm=/\p{L}/u,tg=/\p{Emoji}/u,ty=/\d/,tk=/\s/,tv=null,tw=null;function tb(t,e){let n=function(t){let e=[],n=t.length,r=0;for(;r<n;){let i,s=t.charCodeAt(r),o=s<55296||s>56319||r+1===n||(i=t.charCodeAt(r+1))<56320||i>57343?t[r]:t.slice(r,r+2);e.push(o),r+=o.length}return e}(e.replace(/[A-Z]/g,t=>t.toLowerCase())),r=n.length,i=[],s=0,o=0;for(;o<r;){let l=t,a=null,h=0,c=null,p=-1,u=-1;for(;o<r&&(a=l.go(n[o]));)(l=a).accepts()?(p=0,u=0,c=l):p>=0&&(p+=n[o].length,u++),h+=n[o].length,s+=n[o].length,o++;s-=p,o-=u,h-=p,i.push({t:c.t,v:e.slice(s-h,s),s:s-h,e:s})}return i}function tx(t,e,n,r,i){let s,o=e.length;for(let n=0;n<o-1;n++){let o=e[n];t.j[o]?s=t.j[o]:((s=new f(r)).jr=i.slice(),t.j[o]=s),t=s}return(s=new f(n)).jr=i.slice(),t.j[e[o-1]]=s,s}function tS(t){let e=[],n=[],r=0;for(;r<t.length;){let i=0;for(;"0123456789".indexOf(t[r+i])>=0;)i++;if(i>0){e.push(n.join(""));for(let e=parseInt(t.substring(r,r+i),10);e>0;e--)n.pop();r+=i}else n.push(t[r]),r++}return e}let tM={defaultProtocol:"http",events:null,format:tC,formatHref:tC,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function tA(t,e=null){let n=i({},tM);t&&(n=i(n,t instanceof tA?t.o:t));let r=n.ignoreTags,s=[];for(let t=0;t<r.length;t++)s.push(r[t].toUpperCase());this.o=n,e&&(this.defaultRender=e),this.ignoreTags=s}function tC(t){return t}function tT(t,e){this.t="token",this.v=t,this.tk=e}function tE(t,e){class n extends tT{constructor(e,n){super(e,n),this.t=t}}for(let t in e)n.prototype[t]=e[t];return n.t=t,n}tA.prototype={o:tM,ignoreTags:[],defaultRender:t=>t,check(t){return this.get("validate",t.toString(),t)},get(t,e,n){let r=null!=e,i=this.o[t];return i&&("object"==typeof i?"function"==typeof(i=n.t in i?i[n.t]:tM[t])&&r&&(i=i(e,n)):"function"==typeof i&&r&&(i=i(e,n.t,n))),i},getObj(t,e,n){let r=this.o[t];return"function"==typeof r&&null!=e&&(r=r(e,n.t,n)),r},render(t){let e=t.render(this);return(this.get("render",null,t)||this.defaultRender)(e,t.t,t)}},tT.prototype={isLink:!1,toString(){return this.v},toHref(t){return this.toString()},toFormattedString(t){let e=this.toString(),n=t.get("truncate",e,this),r=t.get("format",e,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(t){return t.get("formatHref",this.toHref(t.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(t=tM.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(t),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(t){return{type:this.t,value:this.toFormattedString(t),isLink:this.isLink,href:this.toFormattedHref(t),start:this.startIndex(),end:this.endIndex()}},validate(t){return t.get("validate",this.toString(),this)},render(t){let e=this.toHref(t.get("defaultProtocol")),n=t.get("formatHref",e,this),r=t.get("tagName",e,this),s=this.toFormattedString(t),o={},l=t.get("className",e,this),a=t.get("target",e,this),h=t.get("rel",e,this),c=t.getObj("attributes",e,this),p=t.getObj("events",e,this);return o.href=n,l&&(o.class=l),a&&(o.target=a),h&&(o.rel=h),c&&i(o,c),{tagName:r,attributes:o,content:s,eventListeners:p}}};let tO=tE("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),tR=tE("text"),tI=tE("nl"),tN=tE("url",{isLink:!0,toHref(t=tM.defaultProtocol){return this.hasProtocol()?this.v:`${t}://${this.v}`},hasProtocol(){let t=this.tk;return t.length>=2&&t[0].t!==S&&t[1].t===G}}),tL=t=>new f(t);function tz(t,e,n){let r=n[0].s,i=n[n.length-1].e;return new t(e.slice(r,i),n)}let tF="undefined"!=typeof console&&console&&console.warn||(()=>{}),tD={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function tP(t,e=!1){if(tD.initialized&&tF(`linkifyjs: already initialized - will not register custom scheme "${t}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(t))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);tD.customSchemes.push([t,e])}function tH(t){return tD.initialized||function(){tD.scanner=function(t=[]){let e={};f.groups=e;let n=new f;null==tv&&(tv=tS("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==tw&&(tw=tS("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),k(n,"'",V),k(n,"{",T),k(n,"}",E),k(n,"[",O),k(n,"]",R),k(n,"(",I),k(n,")",N),k(n,"<",L),k(n,">",z),k(n,"（",F),k(n,"）",D),k(n,"「",P),k(n,"」",H),k(n,"『",B),k(n,"』",j),k(n,"＜",J),k(n,"＞",$),k(n,"&",K),k(n,"*",_),k(n,"@","AT"),k(n,"`",U),k(n,"^",q),k(n,":",G),k(n,",",Y),k(n,"$",X),k(n,".","DOT"),k(n,"=",Q),k(n,"!",Z),k(n,"-",tt),k(n,"%",te),k(n,"|",tn),k(n,"+",tr),k(n,"#",ti),k(n,"?",ts),k(n,'"',to),k(n,"/",th),k(n,";",ta),k(n,"~",tc),k(n,"_",tp),k(n,"\\",W),k(n,"・",tl);let r=g(n,ty,"NUM",{[s]:!0});g(r,ty,r);let m=g(r,tf,b,{[a]:!0}),tb=g(r,tm,x,{[h]:!0}),tM=g(n,tf,v,{[o]:!0});g(tM,ty,m),g(tM,tf,tM),g(m,ty,m),g(m,tf,m);let tA=g(n,tm,w,{[l]:!0});g(tA,tf),g(tA,ty,tb),g(tA,tm,tA),g(tb,ty,tb),g(tb,tf),g(tb,tm,tb);let tC=k(n,"\n","NL",{[u]:!0}),tT=k(n,"\r","WS",{[u]:!0}),tE=g(n,tk,"WS",{[u]:!0});k(n,"￼",tE),k(tT,"\n",tC),k(tT,"￼",tE),g(tT,tk,tE),k(tE,"\r"),k(tE,"\n"),g(tE,tk,tE),k(tE,"￼",tE);let tO=g(n,tg,tu,{[p]:!0});k(tO,"#"),g(tO,tg,tO),k(tO,"️",tO);let tR=k(tO,"‍");k(tR,"#"),g(tR,tg,tO);let tI=[[tf,tM],[ty,m]],tN=[[tf,null],[tm,tA],[ty,tb]];for(let t=0;t<tv.length;t++)tx(n,tv[t],"TLD",v,tI);for(let t=0;t<tw.length;t++)tx(n,tw[t],M,w,tN);d("TLD",{tld:!0,ascii:!0},e),d(M,{utld:!0,alpha:!0},e),tx(n,"file",A,v,tI),tx(n,"mailto",A,v,tI),tx(n,"http",C,v,tI),tx(n,"https",C,v,tI),tx(n,"ftp",C,v,tI),tx(n,"ftps",C,v,tI),d(A,{scheme:!0,ascii:!0},e),d(C,{slashscheme:!0,ascii:!0},e),t=t.sort((t,e)=>t[0]>e[0]?1:-1);for(let e=0;e<t.length;e++){let r=t[e][0],i=t[e][1]?{scheme:!0}:{slashscheme:!0};r.indexOf("-")>=0?i[c]=!0:tf.test(r)?ty.test(r)?i[a]=!0:i[o]=!0:i[s]=!0,y(n,r,r,i)}return y(n,"localhost",S,{ascii:!0}),n.jd=new f("SYM"),{start:n,tokens:i({groups:e},td)}}(tD.customSchemes);for(let t=0;t<tD.tokenQueue.length;t++)tD.tokenQueue[t][1]({scanner:tD.scanner});tD.parser=function({groups:t}){let e=t.domain.concat([K,_,"AT",W,U,q,X,Q,tt,"NUM",te,tn,tr,ti,th,"SYM",tc,tp]),n=[V,G,Y,"DOT",Z,te,ts,to,ta,L,z,T,E,R,O,I,N,F,D,P,H,B,j,J,$],r=[K,V,_,W,U,q,X,Q,tt,T,E,te,tn,tr,ti,ts,th,"SYM",tc,tp],i=tL(),s=k(i,tc);m(s,r,s),m(s,t.domain,s);let o=tL(),l=tL(),a=tL();m(i,t.domain,o),m(i,t.scheme,l),m(i,t.slashscheme,a),m(o,r,s),m(o,t.domain,o);let h=k(o,"AT");k(s,"AT",h),k(l,"AT",h),k(a,"AT",h);let c=k(s,"DOT");m(c,r,s),m(c,t.domain,s);let p=tL();m(h,t.domain,p),m(p,t.domain,p);let u=k(p,"DOT");m(u,t.domain,p);let d=tL(tO);m(u,t.tld,d),m(u,t.utld,d),k(h,S,d);let f=k(p,tt);k(f,tt,f),m(f,t.domain,p),m(d,t.domain,p),k(d,"DOT",u),k(d,tt,f),m(k(d,G),t.numeric,tO);let g=k(o,tt),y=k(o,"DOT");k(g,tt,g),m(g,t.domain,o),m(y,r,s),m(y,t.domain,o);let v=tL(tN);m(y,t.tld,v),m(y,t.utld,v),m(v,t.domain,o),m(v,r,s),k(v,"DOT",y),k(v,tt,g),k(v,"AT",h);let w=k(v,G),b=tL(tN);m(w,t.numeric,b);let x=tL(tN),M=tL();m(x,e,x),m(x,n,M),m(M,e,x),m(M,n,M),k(v,th,x),k(b,th,x);let A=k(l,G),C=k(a,G),tl=k(C,th),tu=k(tl,th);m(l,t.domain,o),k(l,"DOT",y),k(l,tt,g),m(a,t.domain,o),k(a,"DOT",y),k(a,tt,g),m(A,t.domain,x),k(A,th,x),k(A,ts,x),m(tu,t.domain,x),m(tu,e,x),k(tu,th,x);let tf=[[T,E],[O,R],[I,N],[L,z],[F,D],[P,H],[B,j],[J,$]];for(let t=0;t<tf.length;t++){let[r,i]=tf[t],s=k(x,r);k(M,r,s),k(s,i,x);let o=tL(tN);m(s,e,o);let l=tL();m(s,n),m(o,e,o),m(o,n,l),m(l,e,o),m(l,n,l),k(o,i,x),k(l,i,x)}return k(i,S,v),k(i,"NL",tI),{start:i,tokens:td}}(tD.scanner.tokens);for(let t=0;t<tD.pluginQueue.length;t++)tD.pluginQueue[t][1]({scanner:tD.scanner,parser:tD.parser});tD.initialized=!0}(),function(t,e,n){let r=n.length,i=0,s=[],o=[];for(;i<r;){let l=t,a=null,h=null,c=0,p=null,u=-1;for(;i<r&&!(a=l.go(n[i].t));)o.push(n[i++]);for(;i<r&&(h=a||l.go(n[i].t));)a=null,(l=h).accepts()?(u=0,p=l):u>=0&&u++,i++,c++;if(u<0)(i-=c)<r&&(o.push(n[i]),i++);else{o.length>0&&(s.push(tz(tR,e,o)),o=[]),i-=u,c-=u;let t=p.t,r=n.slice(i-c,i);s.push(tz(t,e,r))}}return o.length>0&&s.push(tz(tR,e,o)),s}(tD.parser.start,t,tb(tD.scanner.start,t))}function tB(t,e=null,n=null){if(e&&"object"==typeof e){if(n)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);n=e,e=null}let r=new tA(n),i=tH(t),s=[];for(let t=0;t<i.length;t++){let n=i[t];n.isLink&&(!e||n.t===e)&&r.check(n)&&s.push(n.toFormattedObject(r))}return s}tH.scan=tb;var tj=n(2571);let tJ=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function t$(t,e){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach(t=>{let e="string"==typeof t?t:t.scheme;e&&n.push(e)}),!t||t.replace(tJ,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}let tK=r.CU.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(t=>{if("string"==typeof t)return void tP(t);tP(t.scheme,t.optionalSlashes)})},onDestroy(){f.groups={},tD.scanner=null,tD.parser=null,tD.tokenQueue=[],tD.pluginQueue=[],tD.customSchemes=[],tD.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(t,e)=>!!t$(t,e.protocols),validate:t=>!!t,shouldAutoLink:t=>!!t}),addAttributes(){return{href:{default:null,parseHTML:t=>t.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:t=>{let e=t.getAttribute("href");return!!e&&!!this.options.isAllowedUri(e,{defaultValidate:t=>!!t$(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:t}){return this.options.isAllowedUri(t.href,{defaultValidate:t=>!!t$(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,r.KV)(this.options.HTMLAttributes,t),0]:["a",(0,r.KV)(this.options.HTMLAttributes,{...t,href:""}),0]},addCommands(){return{setLink:t=>({chain:e})=>{let{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!t$(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().setMark(this.name,t).setMeta("preventAutolink",!0).run()},toggleLink:t=>({chain:e})=>{let{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!t$(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().toggleMark(this.name,t,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:t})=>t().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,r.Zc)({find:t=>{let e=[];if(t){let{protocols:n,defaultProtocol:r}=this.options,i=tB(t).filter(t=>t.isLink&&this.options.isAllowedUri(t.value,{defaultValidate:t=>!!t$(t,n),protocols:n,defaultProtocol:r}));i.length&&i.forEach(t=>e.push({text:t.value,data:{href:t.href},index:t.start}))}return e},type:this.type,getAttributes:t=>{var e;return{href:null==(e=t.data)?void 0:e.href}}})]},addProseMirrorPlugins(){var t,e,n;let i=[],{protocols:s,defaultProtocol:o}=this.options;return this.options.autolink&&i.push((t={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:t=>this.options.isAllowedUri(t,{defaultValidate:t=>!!t$(t,s),protocols:s,defaultProtocol:o}),shouldAutoLink:this.options.shouldAutoLink},new tj.k_({key:new tj.hs("autolink"),appendTransaction:(e,n,i)=>{let s=e.some(t=>t.docChanged)&&!n.doc.eq(i.doc),o=e.some(t=>t.getMeta("preventAutolink"));if(!s||o)return;let{tr:l}=i,a=(0,r.T7)(n.doc,[...e]);if((0,r.FF)(a).forEach(({newRange:e})=>{let n,s,o=(0,r.Nx)(i.doc,e,t=>t.isTextblock);if(o.length>1?(n=o[0],s=i.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ")):o.length&&i.doc.textBetween(e.from,e.to," "," ").endsWith(" ")&&(n=o[0],s=i.doc.textBetween(n.pos,e.to,void 0," ")),n&&s){let e=s.split(" ").filter(t=>""!==t);if(e.length<=0)return!1;let o=e[e.length-1],a=n.pos+s.lastIndexOf(o);if(!o)return!1;let h=tH(o).map(e=>e.toObject(t.defaultProtocol));if(!(1===h.length?h[0].isLink:3===h.length&&!!h[1].isLink&&["()","[]"].includes(h[0].value+h[2].value)))return!1;h.filter(t=>t.isLink).map(t=>({...t,from:a+t.start+1,to:a+t.end+1})).filter(t=>!i.schema.marks.code||!i.doc.rangeHasMark(t.from,t.to,i.schema.marks.code)).filter(e=>t.validate(e.value)).filter(e=>t.shouldAutoLink(e.value)).forEach(e=>{(0,r.hO)(e.from,e.to,i.doc).some(e=>e.mark.type===t.type)||l.addMark(e.from,e.to,t.type.create({href:e.href}))})}}),l.steps.length)return l}}))),!0===this.options.openOnClick&&i.push((e={type:this.type},new tj.k_({key:new tj.hs("handleClickLink"),props:{handleClick:(t,n,i)=>{var s,o;if(0!==i.button||!t.editable)return!1;let l=i.target,a=[];for(;"DIV"!==l.nodeName;)a.push(l),l=l.parentNode;if(!a.find(t=>"A"===t.nodeName))return!1;let h=(0,r.gu)(t.state,e.type.name),c=i.target,p=null!=(s=null==c?void 0:c.href)?s:h.href,u=null!=(o=null==c?void 0:c.target)?o:h.target;return!!c&&!!p&&(window.open(p,u),!0)}}}))),this.options.linkOnPaste&&i.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new tj.k_({key:new tj.hs("handlePasteLink"),props:{handlePaste:(t,e,r)=>{let{state:i}=t,{selection:s}=i,{empty:o}=s;if(o)return!1;let l="";r.content.forEach(t=>{l+=t.textContent});let a=tB(l,{defaultProtocol:n.defaultProtocol}).find(t=>t.isLink&&t.value===l);return!!l&&!!a&&n.editor.commands.setMark(n.type,{href:a.href})}}}))),i}})},6770:(t,e,n)=>{n.d(e,{K:()=>f,w:()=>d});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},i={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},s="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),o="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),l=0;l<10;l++)r[48+l]=r[96+l]=String(l);for(var l=1;l<=24;l++)r[l+111]="F"+l;for(var l=65;l<=90;l++)r[l]=String.fromCharCode(l+32),i[l]=String.fromCharCode(l);for(var a in r)i.hasOwnProperty(a)||(i[a]=r[a]);var h=n(2571);let c="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),p="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function u(t,e,n=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),n&&e.shiftKey&&(t="Shift-"+t),t}function d(t){return new h.k_({props:{handleKeyDown:f(t)}})}function f(t){let e=function(t){let e=Object.create(null);for(let n in t)e[function(t){let e,n,r,i,s=t.split(/-(?!$)/),o=s[s.length-1];"Space"==o&&(o=" ");for(let t=0;t<s.length-1;t++){let o=s[t];if(/^(cmd|meta|m)$/i.test(o))i=!0;else if(/^a(lt)?$/i.test(o))e=!0;else if(/^(c|ctrl|control)$/i.test(o))n=!0;else if(/^s(hift)?$/i.test(o))r=!0;else if(/^mod$/i.test(o))c?i=!0:n=!0;else throw Error("Unrecognized modifier name: "+o)}return e&&(o="Alt-"+o),n&&(o="Ctrl-"+o),i&&(o="Meta-"+o),r&&(o="Shift-"+o),o}(n)]=t[n];return e}(t);return function(t,n){var l;let a=("Esc"==(l=!(s&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||o&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?i:r)[n.keyCode]||n.key||"Unidentified")&&(l="Escape"),"Del"==l&&(l="Delete"),"Left"==l&&(l="ArrowLeft"),"Up"==l&&(l="ArrowUp"),"Right"==l&&(l="ArrowRight"),"Down"==l&&(l="ArrowDown"),l),h,c=e[u(a,n)];if(c&&c(t.state,t.dispatch,t))return!0;if(1==a.length&&" "!=a){if(n.shiftKey){let r=e[u(a,n,!1)];if(r&&r(t.state,t.dispatch,t))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(p&&n.ctrlKey&&n.altKey)&&(h=r[n.keyCode])&&h!=a){let r=e[u(h,n)];if(r&&r(t.state,t.dispatch,t))return!0}}return!1}}},7213:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7325:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("align-left",[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},7550:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7733:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("align-center",[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},8164:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},8292:(t,e,n)=>{n.d(e,{A:()=>ty});var r=n(4701);let i=/^\s*>\s$/,s=r.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:t}){return["blockquote",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,r.tG)({find:i,type:this.type})]}}),o=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,l=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,a=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,h=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,c=r.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>"normal"!==t.style.fontWeight&&null},{style:"font-weight=400",clearMark:t=>t.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return["strong",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,r.OX)({find:o,type:this.type}),(0,r.OX)({find:a,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:l,type:this.type}),(0,r.Zc)({find:h,type:this.type})]}}),p="textStyle",u=/^\s*([-+*])\s$/,d=r.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(p)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=(0,r.tG)({find:u,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,r.tG)({find:u,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(p),editor:this.editor})),[t]}}),f=/(^|[^`])`([^`]+)`(?!`)/,m=/(^|[^`])`([^`]+)`(?!`)/g,g=r.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:t}){return["code",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,r.OX)({find:f,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:m,type:this.type})]}});var y=n(2571);let k=/^```([a-z]+)?[\s\n]$/,v=/^~~~([a-z]+)?[\s\n]$/,w=r.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:t=>{var e;let{languageClassPrefix:n}=this.options,r=[...(null==(e=t.firstElementChild)?void 0:e.classList)||[]].filter(t=>t.startsWith(n)).map(t=>t.replace(n,""))[0];return r||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:t,HTMLAttributes:e}){return["pre",(0,r.KV)(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:t,$anchor:e}=this.editor.state.selection,n=1===e.pos;return!!t&&e.parent.type.name===this.name&&(!!n||!e.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:e}=t,{selection:n}=e,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;let s=r.parentOffset===r.parent.nodeSize-2,o=r.parent.textContent.endsWith("\n\n");return!!s&&!!o&&t.chain().command(({tr:t})=>(t.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;let{state:e}=t,{selection:n,doc:r}=e,{$from:i,empty:s}=n;if(!s||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let o=i.after();return void 0!==o&&(r.nodeAt(o)?t.commands.command(({tr:t})=>(t.setSelection(y.LN.near(r.resolve(o))),!0)):t.commands.exitCode())}}},addInputRules(){return[(0,r.JJ)({find:k,type:this.type,getAttributes:t=>({language:t[1]})}),(0,r.JJ)({find:v,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new y.k_({key:new y.hs("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;let n=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,s=null==i?void 0:i.mode;if(!n||!s)return!1;let{tr:o,schema:l}=t.state,a=l.text(n.replace(/\r\n?/g,"\n"));return o.replaceSelectionWith(this.type.create({language:s},a)),o.selection.$from.parent.type!==this.type&&o.setSelection(y.U3.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.setMeta("paste",!0),t.dispatch(o),!0}}})]}}),b=r.bP.create({name:"doc",topNode:!0,content:"block+"});var x=n(808);class S{constructor(t,e){var n;this.editorView=t,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(n=e.width)?n:1,this.color=!1===e.color?void 0:e.color||"black",this.class=e.class,this.handlers=["dragover","dragend","drop","dragleave"].map(e=>{let n=t=>{this[e](t)};return t.dom.addEventListener(e,n),{name:e,handler:n}})}destroy(){this.handlers.forEach(({name:t,handler:e})=>this.editorView.dom.removeEventListener(t,e))}update(t,e){null!=this.cursorPos&&e.doc!=t.state.doc&&(this.cursorPos>t.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(t){t!=this.cursorPos&&(this.cursorPos=t,null==t?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let t,e,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,i,s=this.editorView.dom,o=s.getBoundingClientRect(),l=o.width/s.offsetWidth,a=o.height/s.offsetHeight;if(r){let t=n.nodeBefore,e=n.nodeAfter;if(t||e){let n=this.editorView.nodeDOM(this.cursorPos-(t?t.nodeSize:0));if(n){let r=n.getBoundingClientRect(),s=t?r.bottom:r.top;t&&e&&(s=(s+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let o=this.width/2*a;i={left:r.left,right:r.right,top:s-o,bottom:s+o}}}}if(!i){let t=this.editorView.coordsAtPos(this.cursorPos),e=this.width/2*l;i={left:t.left-e,right:t.left+e,top:t.top,bottom:t.bottom}}let h=this.editorView.dom.offsetParent;if(!this.element&&(this.element=h.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),h&&(h!=document.body||"static"!=getComputedStyle(h).position)){let n=h.getBoundingClientRect(),r=n.width/h.offsetWidth,i=n.height/h.offsetHeight;t=n.left-h.scrollLeft*r,e=n.top-h.scrollTop*i}else t=-pageXOffset,e=-pageYOffset;this.element.style.left=(i.left-t)/l+"px",this.element.style.top=(i.top-e)/a+"px",this.element.style.width=(i.right-i.left)/l+"px",this.element.style.height=(i.bottom-i.top)/a+"px"}scheduleRemoval(t){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),t)}dragover(t){if(!this.editorView.editable)return;let e=this.editorView.posAtCoords({left:t.clientX,top:t.clientY}),n=e&&e.inside>=0&&this.editorView.state.doc.nodeAt(e.inside),r=n&&n.type.spec.disableDropCursor,i="function"==typeof r?r(this.editorView,e,t):r;if(e&&!i){let t=e.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let e=(0,x.Um)(this.editorView.state.doc,t,this.editorView.dragging.slice);null!=e&&(t=e)}this.setCursor(t),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(t){this.editorView.dom.contains(t.relatedTarget)||this.setCursor(null)}}let M=r.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(t={}){return new y.k_({view:e=>new S(e,t)})}(this.options)]}});var A=n(6770),C=n(156),T=n(2695);class E extends y.LN{constructor(t){super(t,t)}map(t,e){let n=t.resolve(e.map(this.head));return E.valid(n)?new E(n):y.LN.near(n)}content(){return C.Ji.empty}eq(t){return t instanceof E&&t.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new E(t.resolve(e.pos))}getBookmark(){return new O(this.anchor)}static valid(t){let e=t.parent;if(e.isTextblock||!function(t){for(let e=t.depth;e>=0;e--){let n=t.index(e),r=t.node(e);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let t=r.child(n-1);;t=t.lastChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}}return!0}(t)||!function(t){for(let e=t.depth;e>=0;e--){let n=t.indexAfter(e),r=t.node(e);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let t=r.child(n);;t=t.firstChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}}return!0}(t))return!1;let n=e.type.spec.allowGapCursor;if(null!=n)return n;let r=e.contentMatchAt(t.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(t,e,n=!1){n:for(;;){if(!n&&E.valid(t))return t;let r=t.pos,i=null;for(let n=t.depth;;n--){let s=t.node(n);if(e>0?t.indexAfter(n)<s.childCount:t.index(n)>0){i=s.child(e>0?t.indexAfter(n):t.index(n)-1);break}if(0==n)return null;r+=e;let o=t.doc.resolve(r);if(E.valid(o))return o}for(;;){let s=e>0?i.firstChild:i.lastChild;if(!s){if(i.isAtom&&!i.isText&&!y.nh.isSelectable(i)){t=t.doc.resolve(r+i.nodeSize*e),n=!1;continue n}break}i=s,r+=e;let o=t.doc.resolve(r);if(E.valid(o))return o}return null}}}E.prototype.visible=!1,E.findFrom=E.findGapCursorFrom,y.LN.jsonID("gapcursor",E);class O{constructor(t){this.pos=t}map(t){return new O(t.map(this.pos))}resolve(t){let e=t.resolve(this.pos);return E.valid(e)?new E(e):y.LN.near(e)}}let R=(0,A.K)({ArrowLeft:I("horiz",-1),ArrowRight:I("horiz",1),ArrowUp:I("vert",-1),ArrowDown:I("vert",1)});function I(t,e){let n="vert"==t?e>0?"down":"up":e>0?"right":"left";return function(t,r,i){let s=t.selection,o=e>0?s.$to:s.$from,l=s.empty;if(s instanceof y.U3){if(!i.endOfTextblock(n)||0==o.depth)return!1;l=!1,o=t.doc.resolve(e>0?o.after():o.before())}let a=E.findGapCursorFrom(o,e,l);return!!a&&(r&&r(t.tr.setSelection(new E(a))),!0)}}function N(t,e,n){if(!t||!t.editable)return!1;let r=t.state.doc.resolve(e);if(!E.valid(r))return!1;let i=t.posAtCoords({left:n.clientX,top:n.clientY});return!(i&&i.inside>-1&&y.nh.isSelectable(t.state.doc.nodeAt(i.inside)))&&(t.dispatch(t.state.tr.setSelection(new E(r))),!0)}function L(t,e){if("insertCompositionText"!=e.inputType||!(t.state.selection instanceof E))return!1;let{$from:n}=t.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(t.state.schema.nodes.text);if(!r)return!1;let i=C.FK.empty;for(let t=r.length-1;t>=0;t--)i=C.FK.from(r[t].createAndFill(null,i));let s=t.state.tr.replace(n.pos,n.pos,new C.Ji(i,0,0));return s.setSelection(y.U3.near(s.doc.resolve(n.pos+1))),t.dispatch(s),!1}function z(t){if(!(t.selection instanceof E))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",T.zF.create(t.doc,[T.NZ.widget(t.selection.head,e,{key:"gapcursor"})])}let F=r.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new y.k_({props:{decorations:z,createSelectionBetween:(t,e,n)=>e.pos==n.pos&&E.valid(n)?new E(n):null,handleClick:N,handleKeyDown:R,handleDOMEvents:{beforeinput:L}}})],extendNodeSchema(t){var e;let n={name:t.name,options:t.options,storage:t.storage};return{allowGapCursor:null!=(e=(0,r.gk)((0,r.iI)(t,"allowGapCursor",n)))?e:null}}}),D=r.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:t}){return["br",(0,r.KV)(this.options.HTMLAttributes,t)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:t,chain:e,state:n,editor:r})=>t.first([()=>t.exitCode(),()=>t.command(()=>{let{selection:t,storedMarks:i}=n;if(t.$from.parent.type.spec.isolating)return!1;let{keepMarks:s}=this.options,{splittableMarks:o}=r.extensionManager,l=i||t.$to.parentOffset&&t.$from.marks();return e().insertContent({type:this.name}).command(({tr:t,dispatch:e})=>{if(e&&l&&s){let e=l.filter(t=>o.includes(t.type.name));t.ensureMarks(e)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),P=r.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(t=>({tag:`h${t}`,attrs:{level:t}}))},renderHTML({node:t,HTMLAttributes:e}){let n=this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0];return[`h${n}`,(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.setNode(this.name,t),toggleHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return this.options.levels.reduce((t,e)=>({...t,...{[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}}),{})},addInputRules(){return this.options.levels.map(t=>(0,r.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}}))}});var H=function(){};H.prototype.append=function(t){return t.length?(t=H.from(t),!this.length&&t||t.length<200&&this.leafAppend(t)||this.length<200&&t.leafPrepend(this)||this.appendInner(t)):this},H.prototype.prepend=function(t){return t.length?H.from(t).append(this):this},H.prototype.appendInner=function(t){return new j(this,t)},H.prototype.slice=function(t,e){return(void 0===t&&(t=0),void 0===e&&(e=this.length),t>=e)?H.empty:this.sliceInner(Math.max(0,t),Math.min(this.length,e))},H.prototype.get=function(t){if(!(t<0)&&!(t>=this.length))return this.getInner(t)},H.prototype.forEach=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length),e<=n?this.forEachInner(t,e,n,0):this.forEachInvertedInner(t,e,n,0)},H.prototype.map=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(e,n){return r.push(t(e,n))},e,n),r},H.from=function(t){return t instanceof H?t:t&&t.length?new B(t):H.empty};var B=function(t){function e(e){t.call(this),this.values=e}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var n={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(t,n){return 0==t&&n==this.length?this:new e(this.values.slice(t,n))},e.prototype.getInner=function(t){return this.values[t]},e.prototype.forEachInner=function(t,e,n,r){for(var i=e;i<n;i++)if(!1===t(this.values[i],r+i))return!1},e.prototype.forEachInvertedInner=function(t,e,n,r){for(var i=e-1;i>=n;i--)if(!1===t(this.values[i],r+i))return!1},e.prototype.leafAppend=function(t){if(this.length+t.length<=200)return new e(this.values.concat(t.flatten()))},e.prototype.leafPrepend=function(t){if(this.length+t.length<=200)return new e(t.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(e.prototype,n),e}(H);H.empty=new B([]);var j=function(t){function e(e,n){t.call(this),this.left=e,this.right=n,this.length=e.length+n.length,this.depth=Math.max(e.depth,n.depth)+1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,e,n,r){var i=this.left.length;if(e<i&&!1===this.left.forEachInner(t,e,Math.min(n,i),r)||n>i&&!1===this.right.forEachInner(t,Math.max(e-i,0),Math.min(this.length,n)-i,r+i))return!1},e.prototype.forEachInvertedInner=function(t,e,n,r){var i=this.left.length;if(e>i&&!1===this.right.forEachInvertedInner(t,e-i,Math.max(n,i)-i,r+i)||n<i&&!1===this.left.forEachInvertedInner(t,Math.min(e,i),n,r))return!1},e.prototype.sliceInner=function(t,e){if(0==t&&e==this.length)return this;var n=this.left.length;return e<=n?this.left.slice(t,e):t>=n?this.right.slice(t-n,e-n):this.left.slice(t,n).append(this.right.slice(0,e-n))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(H);class J{constructor(t,e){this.items=t,this.eventCount=e}popEvent(t,e){let n,r,i,s;if(0==this.eventCount)return null;let o=this.items.length;for(;;o--)if(this.items.get(o-1).selection){--o;break}e&&(r=(n=this.remapping(o,this.items.length)).maps.length);let l=t.tr,a=[],h=[];return this.items.forEach((t,e)=>{if(!t.step){n||(r=(n=this.remapping(o,e+1)).maps.length),r--,h.push(t);return}if(n){h.push(new $(t.map));let e=t.step.map(n.slice(r)),i;e&&l.maybeStep(e).doc&&(i=l.mapping.maps[l.mapping.maps.length-1],a.push(new $(i,void 0,void 0,a.length+h.length))),r--,i&&n.appendMap(i,r)}else l.maybeStep(t.step);if(t.selection)return i=n?t.selection.map(n.slice(r)):t.selection,s=new J(this.items.slice(0,o).append(h.reverse().concat(a)),this.eventCount-1),!1},this.items.length,0),{remaining:s,transform:l,selection:i}}addTransform(t,e,n,r){var i,s;let o,l=[],a=this.eventCount,h=this.items,c=!r&&h.length?h.get(h.length-1):null;for(let n=0;n<t.steps.length;n++){let i=t.steps[n].invert(t.docs[n]),s=new $(t.mapping.maps[n],i,e),o;(o=c&&c.merge(s))&&(s=o,n?l.pop():h=h.slice(0,h.length-1)),l.push(s),e&&(a++,e=void 0),r||(c=s)}let p=a-n.depth;return p>V&&(i=h,s=p,i.forEach((t,e)=>{if(t.selection&&0==s--)return o=e,!1}),h=i.slice(o),a-=p),new J(h.append(l),a)}remapping(t,e){let n=new x.X9;return this.items.forEach((e,r)=>{let i=null!=e.mirrorOffset&&r-e.mirrorOffset>=t?n.maps.length-e.mirrorOffset:void 0;n.appendMap(e.map,i)},t,e),n}addMaps(t){return 0==this.eventCount?this:new J(this.items.append(t.map(t=>new $(t))),this.eventCount)}rebased(t,e){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-e),i=t.mapping,s=t.steps.length,o=this.eventCount;this.items.forEach(t=>{t.selection&&o--},r);let l=e;this.items.forEach(e=>{let r=i.getMirror(--l);if(null==r)return;s=Math.min(s,r);let a=i.maps[r];if(e.step){let s=t.steps[r].invert(t.docs[r]),h=e.selection&&e.selection.map(i.slice(l+1,r));h&&o++,n.push(new $(a,s,h))}else n.push(new $(a))},r);let a=[];for(let t=e;t<s;t++)a.push(new $(i.maps[t]));let h=new J(this.items.slice(0,r).append(a).append(n),o);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-n.length)),h}emptyItemCount(){let t=0;return this.items.forEach(e=>{!e.step&&t++}),t}compress(t=this.items.length){let e=this.remapping(0,t),n=e.maps.length,r=[],i=0;return this.items.forEach((s,o)=>{if(o>=t)r.push(s),s.selection&&i++;else if(s.step){let t=s.step.map(e.slice(n)),o=t&&t.getMap();if(n--,o&&e.appendMap(o,n),t){let l=s.selection&&s.selection.map(e.slice(n));l&&i++;let a=new $(o.invert(),t,l),h,c=r.length-1;(h=r.length&&r[c].merge(a))?r[c]=h:r.push(a)}}else s.map&&n--},this.items.length,0),new J(H.from(r.reverse()),i)}}J.empty=new J(H.empty,0);class ${constructor(t,e,n,r){this.map=t,this.step=e,this.selection=n,this.mirrorOffset=r}merge(t){if(this.step&&t.step&&!t.selection){let e=t.step.merge(this.step);if(e)return new $(e.getMap().invert(),e,this.selection)}}}class K{constructor(t,e,n,r,i){this.done=t,this.undone=e,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}let V=20;function _(t){let e=[];for(let n=t.length-1;n>=0&&0==e.length;n--)t[n].forEach((t,n,r,i)=>e.push(r,i));return e}function W(t,e){if(!t)return null;let n=[];for(let r=0;r<t.length;r+=2){let i=e.map(t[r],1),s=e.map(t[r+1],-1);i<=s&&n.push(i,s)}return n}let U=!1,q=null;function G(t){let e=t.plugins;if(q!=e){U=!1,q=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){U=!0;break}}return U}let Y=new y.hs("history"),X=new y.hs("closeHistory");function Q(t,e){return(n,r)=>{let i=Y.getState(n);if(!i||0==(t?i.undone:i.done).eventCount)return!1;if(r){let s=function(t,e,n){let r=G(e),i=Y.get(e).spec.config,s=(n?t.undone:t.done).popEvent(e,r);if(!s)return null;let o=s.selection.resolve(s.transform.doc),l=(n?t.done:t.undone).addTransform(s.transform,e.selection.getBookmark(),i,r),a=new K(n?l:s.remaining,n?s.remaining:l,null,0,-1);return s.transform.setSelection(o).setMeta(Y,{redo:n,historyState:a})}(i,n,t);s&&r(e?s.scrollIntoView():s)}return!0}}let Z=Q(!1,!0),tt=Q(!0,!0);Q(!1,!1),Q(!0,!1);let te=r.YY.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:t,dispatch:e})=>Z(t,e),redo:()=>({state:t,dispatch:e})=>tt(t,e)}),addProseMirrorPlugins(){return[function(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new y.k_({key:Y,state:{init:()=>new K(J.empty,J.empty,null,0,-1),apply:(e,n,r)=>(function(t,e,n,r){let i=n.getMeta(Y),s;if(i)return i.historyState;n.getMeta(X)&&(t=new K(t.done,t.undone,null,0,-1));let o=n.getMeta("appendedTransaction");if(0==n.steps.length)return t;if(o&&o.getMeta(Y))if(o.getMeta(Y).redo)return new K(t.done.addTransform(n,void 0,r,G(e)),t.undone,_(n.mapping.maps),t.prevTime,t.prevComposition);else return new K(t.done,t.undone.addTransform(n,void 0,r,G(e)),null,t.prevTime,t.prevComposition);if(!1===n.getMeta("addToHistory")||o&&!1===o.getMeta("addToHistory"))if(s=n.getMeta("rebased"))return new K(t.done.rebased(n,s),t.undone.rebased(n,s),W(t.prevRanges,n.mapping),t.prevTime,t.prevComposition);else return new K(t.done.addMaps(n.mapping.maps),t.undone.addMaps(n.mapping.maps),W(t.prevRanges,n.mapping),t.prevTime,t.prevComposition);{let i=n.getMeta("composition"),s=0==t.prevTime||!o&&t.prevComposition!=i&&(t.prevTime<(n.time||0)-r.newGroupDelay||!function(t,e){if(!e)return!1;if(!t.docChanged)return!0;let n=!1;return t.mapping.maps[0].forEach((t,r)=>{for(let i=0;i<e.length;i+=2)t<=e[i+1]&&r>=e[i]&&(n=!0)}),n}(n,t.prevRanges)),l=o?W(t.prevRanges,n.mapping):_(n.mapping.maps);return new K(t.done.addTransform(n,s?e.selection.getBookmark():void 0,r,G(e)),J.empty,l,n.time,null==i?t.prevComposition:i)}})(n,r,e,t)},config:t,props:{handleDOMEvents:{beforeinput(t,e){let n=e.inputType,r="historyUndo"==n?Z:"historyRedo"==n?tt:null;return!!r&&(e.preventDefault(),r(t.state,t.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),tn=r.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:t}){return["hr",(0,r.KV)(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{let{selection:n}=e,{$from:i,$to:s}=n,o=t();return 0===i.parentOffset?o.insertContentAt({from:Math.max(i.pos-1,0),to:s.pos},{type:this.name}):(0,r.BQ)(n)?o.insertContentAt(s.pos,{type:this.name}):o.insertContent({type:this.name}),o.command(({tr:t,dispatch:e})=>{var n;if(e){let{$to:e}=t.selection,r=e.end();if(e.nodeAfter)e.nodeAfter.isTextblock?t.setSelection(y.U3.create(t.doc,e.pos+1)):e.nodeAfter.isBlock?t.setSelection(y.nh.create(t.doc,e.pos)):t.setSelection(y.U3.create(t.doc,e.pos));else{let i=null==(n=e.parent.type.contentMatch.defaultType)?void 0:n.create();i&&(t.insert(r,i),t.setSelection(y.U3.create(t.doc,r+1)))}t.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,r.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),tr=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,ti=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,ts=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,to=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,tl=r.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>"normal"!==t.style.fontStyle&&null},{style:"font-style=normal",clearMark:t=>t.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,r.OX)({find:tr,type:this.type}),(0,r.OX)({find:ts,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:ti,type:this.type}),(0,r.Zc)({find:to,type:this.type})]}}),ta=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",(0,r.KV)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),th="textStyle",tc=/^(\d+)\.\s$/,tp=r.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1},type:{default:null,parseHTML:t=>t.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:t}){let{start:e,...n}=t;return 1===e?["ol",(0,r.KV)(this.options.HTMLAttributes,n),0]:["ol",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(th)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=(0,r.tG)({find:tc,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,r.tG)({find:tc,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(th)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[t]}}),tu=r.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),td=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,tf=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,tm=r.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:t=>!!t.includes("line-through")&&{}}],renderHTML({HTMLAttributes:t}){return["s",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,r.OX)({find:td,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:tf,type:this.type})]}}),tg=r.bP.create({name:"text",group:"inline"}),ty=r.YY.create({name:"starterKit",addExtensions(){let t=[];return!1!==this.options.bold&&t.push(c.configure(this.options.bold)),!1!==this.options.blockquote&&t.push(s.configure(this.options.blockquote)),!1!==this.options.bulletList&&t.push(d.configure(this.options.bulletList)),!1!==this.options.code&&t.push(g.configure(this.options.code)),!1!==this.options.codeBlock&&t.push(w.configure(this.options.codeBlock)),!1!==this.options.document&&t.push(b.configure(this.options.document)),!1!==this.options.dropcursor&&t.push(M.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&t.push(F.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&t.push(D.configure(this.options.hardBreak)),!1!==this.options.heading&&t.push(P.configure(this.options.heading)),!1!==this.options.history&&t.push(te.configure(this.options.history)),!1!==this.options.horizontalRule&&t.push(tn.configure(this.options.horizontalRule)),!1!==this.options.italic&&t.push(tl.configure(this.options.italic)),!1!==this.options.listItem&&t.push(ta.configure(this.options.listItem)),!1!==this.options.orderedList&&t.push(tp.configure(this.options.orderedList)),!1!==this.options.paragraph&&t.push(tu.configure(this.options.paragraph)),!1!==this.options.strike&&t.push(tm.configure(this.options.strike)),!1!==this.options.text&&t.push(tg.configure(this.options.text)),t}})},8440:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("heading-1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},8702:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("align-right",[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},8932:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},9140:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},9144:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},9621:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},9727:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])}}]);