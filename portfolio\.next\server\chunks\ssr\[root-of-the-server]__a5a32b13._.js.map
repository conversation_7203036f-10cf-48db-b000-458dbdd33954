{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { ChevronDown, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function Hero() {\n  const typedTextRef = useRef<HTMLSpanElement>(null);\n\n  useEffect(() => {\n    // Simple typing animation\n    const texts = ['Video Editor', 'Storyteller', 'Motion Graphics Artist', 'Visual Craftsman'];\n    let textIndex = 0;\n    let charIndex = 0;\n    let isDeleting = false;\n    let currentText = '';\n\n    const typeText = () => {\n      const fullText = texts[textIndex];\n\n      if (isDeleting) {\n        currentText = fullText.substring(0, charIndex - 1);\n        charIndex--;\n      } else {\n        currentText = fullText.substring(0, charIndex + 1);\n        charIndex++;\n      }\n\n      if (typedTextRef.current) {\n        typedTextRef.current.textContent = currentText;\n      }\n\n      let typeSpeed = isDeleting ? 40 : 60;\n\n      if (!isDeleting && charIndex === fullText.length) {\n        typeSpeed = 1500; // Pause at end\n        isDeleting = true;\n      } else if (isDeleting && charIndex === 0) {\n        isDeleting = false;\n        textIndex = (textIndex + 1) % texts.length;\n        typeSpeed = 500; // Pause before next word\n      }\n\n      setTimeout(typeText, typeSpeed);\n    };\n\n    typeText();\n  }, []);\n\n  const handleScrollToWork = () => {\n    const element = document.getElementById('featured-work');\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  const handleScrollToContact = () => {\n    const element = document.getElementById('contact');\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  return (\n    <section\n      id=\"home\"\n      className=\"relative min-h-screen flex items-center bg-background overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-accent/20 to-transparent transform skew-x-12\"></div>\n      </div>\n\n      <div className=\"container mx-auto container-padding section-padding relative z-10\">\n        <div className=\"flex flex-col lg:flex-row items-center justify-between gap-12\">\n          {/* Text Content */}\n          <div className=\"flex-1 max-w-2xl text-center lg:text-left\">\n            <h1 className=\"heading-primary mb-4 animate-in slide-in-from-left duration-1000\">\n              Uttam Rimal\n            </h1>\n            <h2 className=\"heading-tertiary text-accent mb-6 animate-in slide-in-from-left duration-1000 delay-200\">\n              Creative Video Editor & Storyteller\n            </h2>\n            <p className=\"text-body-large mb-8 animate-in slide-in-from-left duration-1000 delay-400\">\n              Transforming footage into compelling narratives. I&apos;m a{' '}\n              <span ref={typedTextRef} className=\"text-accent font-semibold\"></span>\n              <span className=\"animate-pulse\">_</span>\n              {' '}with over a year of experience crafting visually stunning videos that engage and inspire.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 mb-8 animate-in slide-in-from-left duration-1000 delay-600\">\n              <Button\n                onClick={handleScrollToWork}\n                variant=\"secondary\"\n                className=\"btn-secondary\"\n              >\n                View My Work\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleScrollToContact}\n                className=\"hover-lift\"\n              >\n                Get a Quote\n              </Button>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex justify-center lg:justify-start gap-4 animate-in slide-in-from-left duration-1000 delay-800\">\n              {[\n                { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn', color: 'hover:text-[#0077B5]' },\n                { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram', color: 'hover:text-[#E4405F]' },\n                { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube', color: 'hover:text-[#FF0000]' },\n                { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook', color: 'hover:text-[#1877F2]' },\n              ].map(({ icon: Icon, href, label, color }) => (\n                <Link\n                  key={label}\n                  href={href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className={`w-12 h-12 bg-foreground/10 hover:bg-accent text-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg ${color}`}\n                  aria-label={label}\n                >\n                  <Icon size={20} />\n                </Link>\n              ))}\n            </div>\n\n          </div>\n\n          {/* Profile Image */}\n          <div className=\"flex-1 max-w-md animate-in slide-in-from-right duration-1000 delay-300\">\n            <div className=\"relative\">\n              <div className=\"w-80 h-80 mx-auto relative\">\n                <Image\n                  src=\"/images/uttam_rimal.jpg\"\n                  alt=\"Uttam Rimal - Professional Video Editor\"\n                  fill\n                  className=\"rounded-full object-cover border-4 border-foreground/20 shadow-2xl animate-float\"\n                  priority\n                />\n              </div>\n              {/* Decorative elements */}\n              <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-accent/20 rounded-full blur-xl animate-pulse-slow\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-accent/30 rounded-full blur-xl animate-glow\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Down Arrow */}\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        onClick={handleScrollToWork}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-foreground/80 hover:text-foreground transition-colors duration-300 animate-bounce-slow w-auto h-auto p-2\"\n        aria-label=\"Scroll down\"\n      >\n        <ChevronDown size={32} />\n      </Button>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,MAAM,QAAQ;YAAC;YAAgB;YAAe;YAA0B;SAAmB;QAC3F,IAAI,YAAY;QAChB,IAAI,YAAY;QAChB,IAAI,aAAa;QACjB,IAAI,cAAc;QAElB,MAAM,WAAW;YACf,MAAM,WAAW,KAAK,CAAC,UAAU;YAEjC,IAAI,YAAY;gBACd,cAAc,SAAS,SAAS,CAAC,GAAG,YAAY;gBAChD;YACF,OAAO;gBACL,cAAc,SAAS,SAAS,CAAC,GAAG,YAAY;gBAChD;YACF;YAEA,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,WAAW,GAAG;YACrC;YAEA,IAAI,YAAY,aAAa,KAAK;YAElC,IAAI,CAAC,cAAc,cAAc,SAAS,MAAM,EAAE;gBAChD,YAAY,MAAM,eAAe;gBACjC,aAAa;YACf,OAAO,IAAI,cAAc,cAAc,GAAG;gBACxC,aAAa;gBACb,YAAY,CAAC,YAAY,CAAC,IAAI,MAAM,MAAM;gBAC1C,YAAY,KAAK,yBAAyB;YAC5C;YAEA,WAAW,UAAU;QACvB;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAG,WAAU;8CAA0F;;;;;;8CAGxG,8OAAC;oCAAE,WAAU;;wCAA6E;wCAC5B;sDAC5D,8OAAC;4CAAK,KAAK;4CAAc,WAAU;;;;;;sDACnC,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAC/B;wCAAI;;;;;;;8CAIP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM,0MAAA,CAAA,WAAQ;4CAAE,MAAM;4CAA0C,OAAO;4CAAY,OAAO;wCAAuB;wCACnH;4CAAE,MAAM,4MAAA,CAAA,YAAS;4CAAE,MAAM;4CAAwC,OAAO;4CAAa,OAAO;wCAAuB;wCACnH;4CAAE,MAAM,wMAAA,CAAA,UAAO;4CAAE,MAAM;4CAAwC,OAAO;4CAAW,OAAO;wCAAuB;wCAC/G;4CAAE,MAAM,0MAAA,CAAA,WAAQ;4CAAE,MAAM;4CAAuC,OAAO;4CAAY,OAAO;wCAAuB;qCACjH,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACvC,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM;4CACN,QAAO;4CACP,KAAI;4CACJ,WAAW,CAAC,qKAAqK,EAAE,OAAO;4CAC1L,cAAY;sDAEZ,cAAA,8OAAC;gDAAK,MAAM;;;;;;2CAPP;;;;;;;;;;;;;;;;sCAeb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,MAAM;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40\",\n        info:\n          \"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/api.ts"], "sourcesContent": ["// API integration for fetching content from CMS\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || \"http://localhost:3002\";\n\n// Types for API responses\nexport interface BlogPost {\n  _id: string;\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  thumbnail: string;\n  category: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  readTime: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Video {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  description?: string;\n  category?: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reel {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  thumbnail?: string; // Generated dynamically from YouTube API\n  description?: string;\n  platform: \"youtube\";\n  embedUrl?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Testimonial {\n  _id: string;\n  name: string;\n  slug: string;\n  avatar?: string;\n  role: string;\n  company?: string;\n  email?: string;\n  linkedinUrl?: string;\n  content: string;\n  rating: number;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Client {\n  _id: string;\n  name: string;\n  slug: string;\n  logo: string;\n  description: string;\n  website?: string;\n  industry?: string;\n  projectType?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n}\n\n// Generic fetch function with error handling\nasync function fetchFromCMS<T>(endpoint: string): Promise<T[]> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api${endpoint}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      // Add cache control for better performance\n      next: { revalidate: 300 }, // Revalidate every 5 minutes\n    });\n\n    if (!response.ok) {\n      console.error(\n        `Failed to fetch ${endpoint}:`,\n        response.status,\n        response.statusText\n      );\n      return [];\n    }\n\n    const result: ApiResponse<T[]> = await response.json();\n\n    if (!result.success) {\n      console.error(`API error for ${endpoint}:`, result.message);\n      return [];\n    }\n\n    return result.data || [];\n  } catch (error) {\n    console.error(`Network error fetching ${endpoint}:`, error);\n    return [];\n  }\n}\n\n// Blog API functions\nexport async function getBlogPosts(): Promise<BlogPost[]> {\n  const posts = await fetchFromCMS<BlogPost>(\"/blog\");\n  // Only return published posts, sorted by creation date\n  return posts\n    .filter((post) => post.status === \"published\")\n    .sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n}\n\nexport async function getFeaturedBlogPosts(): Promise<BlogPost[]> {\n  const posts = await getBlogPosts();\n  return posts.filter((post) => post.featured);\n}\n\nexport async function getBlogPostBySlug(\n  slug: string\n): Promise<BlogPost | null> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/slug/${slug}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      next: { revalidate: 300 },\n    });\n\n    if (!response.ok) {\n      return null;\n    }\n\n    const result: ApiResponse<BlogPost> = await response.json();\n    return result.success ? result.data : null;\n  } catch (error) {\n    console.error(\"Error fetching blog post by slug:\", error);\n    return null;\n  }\n}\n\n// Videos API functions\nexport async function getVideos(): Promise<Video[]> {\n  const videos = await fetchFromCMS<Video>(\"/videos\");\n  return videos\n    .filter((video) => video.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedVideos(): Promise<Video[]> {\n  const videos = await getVideos();\n  return videos.filter((video) => video.featured);\n}\n\n// Reels API functions\nexport async function getReels(): Promise<Reel[]> {\n  const reels = await fetchFromCMS<Reel>(\"/reels\");\n  return reels\n    .filter((reel) => reel.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedReels(): Promise<Reel[]> {\n  const reels = await getReels();\n  return reels.filter((reel) => reel.featured);\n}\n\n// Testimonials API functions\nexport async function getTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await fetchFromCMS<Testimonial>(\"/testimonials\");\n\n  // If CMS returns empty data, use mock data as fallback\n  if (testimonials.length === 0) {\n    const { testimonials: mockTestimonials } = await import('../data/testimonials');\n    return mockTestimonials.map((testimonial, index) => ({\n      ...testimonial,\n      _id: testimonial.id.toString(),\n      slug: testimonial.name.toLowerCase().replace(/\\s+/g, '-'),\n      rating: testimonial.rating || 5,\n      status: 'published' as const,\n      order: index + 1,\n      featured: index < 3, // First 3 are featured\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }));\n  }\n\n  return testimonials\n    .filter((testimonial) => testimonial.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await getTestimonials();\n  return testimonials.filter((testimonial) => testimonial.featured);\n}\n\n// Clients API functions\nexport async function getClients(): Promise<Client[]> {\n  const clients = await fetchFromCMS<Client>(\"/clients\");\n  return clients\n    .filter((client) => client.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedClients(): Promise<Client[]> {\n  const clients = await getClients();\n  return clients.filter((client) => client.featured);\n}\n\n// Utility functions\nexport function getYouTubeThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\nexport function generateEmbedUrl(id: string): string {\n  // Only YouTube Shorts are supported now\n  return `https://www.youtube.com/embed/${id}`;\n}\n\nexport function getYouTubeShortsThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\n// Helper function to ensure reels have thumbnails\nexport function ensureReelThumbnail(reel: Reel): Reel {\n  return {\n    ...reel,\n    thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id),\n  };\n}\n\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  });\n}\n\nexport function calculateReadTime(content: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = content.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;;;;;;;;;;;;;;;AAChD,MAAM,eAAe,wEAAmC;AA2FxD,6CAA6C;AAC7C,eAAe,aAAgB,QAAgB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,2CAA2C;YAC3C,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CACX,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAC9B,SAAS,MAAM,EACf,SAAS,UAAU;YAErB,OAAO,EAAE;QACX;QAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;QAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,OAAO;YAC1D,OAAO,EAAE;QACX;QAEA,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAuB;IAC3C,uDAAuD;IACvD,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CACH,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;AAEvE;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAEO,eAAe,kBACpB,IAAY;IAEZ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,EAAE,MAAM,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,SAAgC,MAAM,SAAS,IAAI;QACzD,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,GAAG;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM,aAAoB;IACzC,OAAO,OACJ,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK,aACnC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,QAAQ;AAChD;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAmB;IACvC,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAGO,eAAe;IACpB,MAAM,eAAe,MAAM,aAA0B;IAErD,uDAAuD;IACvD,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,MAAM,EAAE,cAAc,gBAAgB,EAAE,GAAG;QAC3C,OAAO,iBAAiB,GAAG,CAAC,CAAC,aAAa,QAAU,CAAC;gBACnD,GAAG,WAAW;gBACd,KAAK,YAAY,EAAE,CAAC,QAAQ;gBAC5B,MAAM,YAAY,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;gBACrD,QAAQ,YAAY,MAAM,IAAI;gBAC9B,QAAQ;gBACR,OAAO,QAAQ;gBACf,UAAU,QAAQ;gBAClB,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEA,OAAO,aACJ,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK,aAC/C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,eAAe,MAAM;IAC3B,OAAO,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,QAAQ;AAClE;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM,aAAqB;IAC3C,OAAO,QACJ,MAAM,CAAC,CAAC,SAAW,OAAO,MAAM,KAAK,aACrC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,OAAO,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,QAAQ;AACnD;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAEO,SAAS,iBAAiB,EAAU;IACzC,wCAAwC;IACxC,OAAO,CAAC,8BAA8B,EAAE,IAAI;AAC9C;AAEO,SAAS,0BAA0B,OAAe;IACvD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAGO,SAAS,oBAAoB,IAAU;IAC5C,OAAO;QACL,GAAG,IAAI;QACP,WAAW,KAAK,SAAS,IAAI,0BAA0B,KAAK,EAAE;IAChE;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,iBAAiB;IACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/hooks/useScrollAnimation.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nexport function useScrollAnimation(threshold = 0.1) {\n  const elementRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const element = elementRef.current;\n    if (!element) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            entry.target.classList.add('animate');\n          }\n        });\n      },\n      {\n        threshold,\n        rootMargin: '0px 0px -50px 0px',\n      }\n    );\n\n    observer.observe(element);\n\n    return () => {\n      observer.unobserve(element);\n    };\n  }, [threshold]);\n\n  return elementRef;\n}\n\nexport function useStaggeredScrollAnimation(threshold = 0.1) {\n  const containerRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            const children = entry.target.querySelectorAll('.animate-on-scroll');\n            children.forEach((child, index) => {\n              setTimeout(() => {\n                child.classList.add('animate');\n              }, index * 100);\n            });\n          }\n        });\n      },\n      {\n        threshold,\n        rootMargin: '0px 0px -50px 0px',\n      }\n    );\n\n    observer.observe(container);\n\n    return () => {\n      observer.unobserve(container);\n    };\n  }, [threshold]);\n\n  return containerRef;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIO,SAAS,mBAAmB,YAAY,GAAG;IAChD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,WAAW,OAAO;QAClC,IAAI,CAAC,SAAS;QAEd,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;gBAC7B;YACF;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,SAAS,OAAO,CAAC;QAEjB,OAAO;YACL,SAAS,SAAS,CAAC;QACrB;IACF,GAAG;QAAC;KAAU;IAEd,OAAO;AACT;AAEO,SAAS,4BAA4B,YAAY,GAAG;IACzD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,WAAW;QAEhB,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,CAAC;oBAC/C,SAAS,OAAO,CAAC,CAAC,OAAO;wBACvB,WAAW;4BACT,MAAM,SAAS,CAAC,GAAG,CAAC;wBACtB,GAAG,QAAQ;oBACb;gBACF;YACF;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,SAAS,OAAO,CAAC;QAEjB,OAAO;YACL,SAAS,SAAS,CAAC;QACrB;IACF,GAAG;QAAC;KAAU;IAEd,OAAO;AACT", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Play, X } from 'lucide-react';\nimport { Dialog, DialogContent, DialogClose } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { getFeaturedVideos, getYouTubeThumbnail, type Video } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function FeaturedWork() {\n  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);\n  const [videos, setVideos] = useState<Video[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchVideos = async () => {\n      try {\n        const featuredVideos = await getFeaturedVideos();\n        // Show only first 6 videos for featured section\n        setVideos(featuredVideos.slice(0, 6));\n      } catch (error) {\n        console.error('Error fetching featured videos:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchVideos();\n  }, []);\n\n  const getYouTubeEmbedUrl = (videoId: string) => {\n    return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;\n  };\n\n  return (\n    <section id=\"featured-work\" className=\"section-padding bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto container-padding\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"heading-secondary mb-4\">\n            Featured Work\n          </h2>\n          <p className=\"text-body-large max-w-2xl mx-auto\">\n            A selection of projects showcasing diverse editing styles and creative storytelling techniques.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <div key={index} className=\"bg-card rounded-xl overflow-hidden shadow-lg\">\n                <div className=\"aspect-video bg-gray-200 animate-pulse\"></div>\n                <div className=\"p-6\">\n                  <div className=\"h-6 bg-gray-200 rounded animate-pulse mb-2\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : videos.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No featured videos available at the moment.</p>\n          </div>\n        ) : (\n          <>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {videos.map((video, index) => (\n                <div\n                  key={video._id}\n                  className={`group card-interactive hover-lift h-[380px] flex flex-col animate-on-scroll stagger-${Math.min(index + 1, 6)}`}\n                >\n                  <div className=\"relative aspect-video overflow-hidden flex-shrink-0\">\n                    <Image\n                      src={getYouTubeThumbnail(video.id)}\n                      alt={video.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Play Button Overlay */}\n                    <Button\n                      variant=\"ghost\"\n                      className=\"absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 h-full w-full rounded-none hover:bg-black/40 hover:cursor-pointer\"\n                      onClick={() => setSelectedVideo(video.id)}\n                    >\n                      <div className=\"w-16 h-16 bg-secondary hover:bg-secondary/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg\">\n                        <Play size={24} className=\"text-secondary-foreground ml-1\" fill=\"currentColor\" />\n                      </div>\n                    </Button>\n\n                    {/* Category Badge */}\n                    {video.category && (\n                      <div className=\"absolute top-4 left-4\">\n                        <Badge variant=\"secondary\" className=\"bg-accent/90 text-accent-foreground\">\n                          {video.category}\n                        </Badge>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"p-6 flex-1 flex flex-col\">\n                    <h3 className=\"text-xl font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 line-clamp-2\">\n                      {video.title}\n                    </h3>\n                    {video.description && (\n                      <p className=\"text-muted-foreground text-sm leading-relaxed line-clamp-3 flex-1\">\n                        {video.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Single Dialog for all videos */}\n            <Dialog\n              open={selectedVideo !== null}\n              onOpenChange={(open) => {\n                if (!open) {\n                  setSelectedVideo(null);\n                }\n              }}\n            >\n              <DialogContent className=\"w-[95vw] sm:max-w-2xl md:max-w-4xl lg:max-w-6xl p-0 bg-black border-0\">\n                <div className=\"relative w-full aspect-video\">\n                  <DialogClose asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"icon\"\n                      className=\"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2\"\n                    >\n                      <X size={24} />\n                    </Button>\n                  </DialogClose>\n                  {selectedVideo && (\n                    <iframe\n                      src={getYouTubeEmbedUrl(selectedVideo)}\n                      title={videos.find(v => v.id === selectedVideo)?.title || 'Video'}\n                      className=\"w-full h-full\"\n                      allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                      allowFullScreen\n                    />\n                  )}\n                </div>\n              </DialogContent>\n            </Dialog>\n          </>\n        )}\n\n        {/* View More Button */}\n        <div className=\"text-center mt-12\">\n          <Button asChild className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n            <Link href=\"/videos\">\n              View All Videos\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,iHAAA,CAAA,oBAAiB,AAAD;gBAC7C,gDAAgD;gBAChD,UAAU,eAAe,KAAK,CAAC,GAAG;YACpC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,OAAO,CAAC,8BAA8B,EAAE,QAAQ,kCAAkC,CAAC;IACrF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAgB,WAAU;QAAgC,KAAK;kBACzE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyB;;;;;;sCAGvC,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;gBAKlD,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJT;;;;;;;;;2BASZ,OAAO,MAAM,KAAK,kBACpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C;;sCACE,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oCAEC,WAAW,CAAC,oFAAoF,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI;;sDAE1H,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,EAAE;oDACjC,KAAK,MAAM,KAAK;oDAChB,IAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,iBAAiB,MAAM,EAAE;8DAExC,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;4DAAiC,MAAK;;;;;;;;;;;;;;;;gDAKnE,MAAM,QAAQ,kBACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,MAAM,QAAQ;;;;;;;;;;;;;;;;;sDAMvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,MAAM,KAAK;;;;;;gDAEb,MAAM,WAAW,kBAChB,8OAAC;oDAAE,WAAU;8DACV,MAAM,WAAW;;;;;;;;;;;;;mCAtCnB,MAAM,GAAG;;;;;;;;;;sCA+CpB,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAM,kBAAkB;4BACxB,cAAc,CAAC;gCACb,IAAI,CAAC,MAAM;oCACT,iBAAiB;gCACnB;4BACF;sCAEA,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,cAAW;4CAAC,OAAO;sDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,MAAM;;;;;;;;;;;;;;;;wCAGZ,+BACC,8OAAC;4CACC,KAAK,mBAAmB;4CACxB,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,SAAS;4CAC1D,WAAU;4CACV,OAAM;4CACN,eAAe;;;;;;;;;;;;;;;;;;;;;;;;8BAU7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientCard.tsx"], "sourcesContent": ["import Image from 'next/image';\nimport { ExternalLink } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { type Client } from '@/lib/api';\nimport clsx from 'clsx';\n\ninterface ClientCardProps {\n  client: Client;\n  variant?: 'logo' | 'detailed' | 'compact';\n  onClick?: () => void;\n  showWebsite?: boolean;\n  className?: string;\n}\n\nconst cardStyles = {\n  logo: 'group relative bg-white p-4 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-full h-full flex items-center justify-center',\n  detailed: 'bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',\n  compact: 'bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'\n};\n\nconst imageSizes = {\n  logo: { className: 'relative h-16 w-full max-w-[120px] mx-auto', size: '16vw' },\n  detailed: { className: 'relative h-24 w-full max-w-[200px] mx-auto mb-6', size: '33vw' },\n  compact: { className: 'relative h-12 w-full max-w-[100px] mx-auto mb-3', size: '33vw' }\n};\n\nconst contentPadding = {\n  detailed: 'p-6 text-center',\n  compact: 'p-4'\n};\n\nfunction ClientImage({ client, variant }: { client: Client; variant: keyof typeof imageSizes }) {\n  return (\n    <div className={imageSizes[variant].className}>\n      <Image\n        src={client.logo}\n        alt={client.name}\n        fill\n        className={clsx('object-contain', variant === 'detailed' && 'rounded-md')}\n        sizes={imageSizes[variant].size}\n      />\n    </div>\n  );\n}\n\nfunction ClientBadges({ client }: { client: Client }) {\n  return (\n    <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n      {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n      {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n    </div>\n  );\n}\n\nexport default function ClientCard({\n  client,\n  variant = 'logo',\n  onClick,\n  showWebsite = false,\n  className = ''\n}: ClientCardProps) {\n  if (variant === 'logo') {\n    return (\n      <button\n        onClick={onClick}\n        className={clsx(\n          'group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-40 h-40 flex flex-col items-center justify-center text-center',\n          className\n        )}\n        title={client.name}\n        aria-label={`View ${client.name}`}\n      >\n        <div className=\"relative w-24 h-24 mb-4\">\n          <Image\n            src={client.logo}\n            alt={client.name}\n            fill\n            className=\"object-cover rounded-full border border-gray-200 shadow-sm\"\n            sizes=\"96px\"\n          />\n        </div>\n        <p className=\"text-sm font-medium text-gray-700\">{client.name}</p>\n      </button>\n    );\n  }\n  if (variant === 'detailed') {\n    return (\n      <article\n        className={clsx(\n          ' transition-all duration-300 w-full max-w-md overflow-hidden',\n          className\n        )}\n      >\n        <div className=\"p-6 flex flex-col items-center text-center\">\n          <div className=\"relative w-28 h-28 mb-4\">\n            <Image\n              src={client.logo}\n              alt={client.name}\n              fill\n              className=\"object-cover rounded-xl border border-gray-200 shadow-sm\"\n              sizes=\"112px\"\n            />\n          </div>\n\n          <h3 className=\"text-2xl font-heading font-bold text-portfolio-primary mb-2\">\n            {client.name}\n          </h3>\n\n          <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n            {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n            {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n          </div>\n\n          <p className=\"text-muted-foreground leading-relaxed mb-6\">\n            {client.description}\n          </p>\n\n          {showWebsite && client.website && (\n            <Button variant=\"outline\" asChild>\n              <a\n                href={client.website}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center gap-2\"\n              >\n                Visit Website <ExternalLink size={16} />\n              </a>\n            </Button>\n          )}\n        </div>\n      </article>\n    );\n  }\n\n\n  // Compact\n  return (\n    <article className={clsx(cardStyles.compact, className)}>\n      <div className={contentPadding.compact}>\n        <ClientImage client={client} variant=\"compact\" />\n        <h4 className=\"text-lg font-heading font-semibold text-foreground mb-2\">{client.name}</h4>\n        <ClientBadges client={client} />\n        <p className=\"text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3\">\n          {client.description}\n        </p>\n        <div className=\"flex items-center justify-between\">\n          {onClick && (\n            <button\n              onClick={onClick}\n              className=\"text-foreground font-semibold text-sm hover:text-accent transition-colors duration-300\"\n              aria-label={`Learn more about ${client.name}`}\n            >\n              Learn More →\n            </button>\n          )}\n          {showWebsite && client.website && (\n            <a\n              href={client.website}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-portfolio-secondary hover:text-portfolio-primary transition-colors duration-300\"\n              aria-label={`Visit ${client.name} website`}\n            >\n              <ExternalLink size={16} />\n            </a>\n          )}\n        </div>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAUA,MAAM,aAAa;IACjB,MAAM;IACN,UAAU;IACV,SAAS;AACX;AAEA,MAAM,aAAa;IACjB,MAAM;QAAE,WAAW;QAA8C,MAAM;IAAO;IAC9E,UAAU;QAAE,WAAW;QAAmD,MAAM;IAAO;IACvF,SAAS;QAAE,WAAW;QAAmD,MAAM;IAAO;AACxF;AAEA,MAAM,iBAAiB;IACrB,UAAU;IACV,SAAS;AACX;AAEA,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAwD;IAC5F,qBACE,8OAAC;QAAI,WAAW,UAAU,CAAC,QAAQ,CAAC,SAAS;kBAC3C,cAAA,8OAAC,6HAAA,CAAA,UAAK;YACJ,KAAK,OAAO,IAAI;YAChB,KAAK,OAAO,IAAI;YAChB,IAAI;YACJ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,kBAAkB,YAAY,cAAc;YAC5D,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI;;;;;;;;;;;AAIvC;AAEA,SAAS,aAAa,EAAE,MAAM,EAAsB;IAClD,qBACE,8OAAC;QAAI,WAAU;;YACZ,OAAO,QAAQ,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAW,OAAO,QAAQ;;;;;;YAC5D,OAAO,WAAW,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAa,OAAO,WAAW;;;;;;;;;;;;AAG3E;AAEe,SAAS,WAAW,EACjC,MAAM,EACN,UAAU,MAAM,EAChB,OAAO,EACP,cAAc,KAAK,EACnB,YAAY,EAAE,EACE;IAChB,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YACC,SAAS;YACT,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACZ,kLACA;YAEF,OAAO,OAAO,IAAI;YAClB,cAAY,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE;;8BAEjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,OAAO,IAAI;wBAChB,KAAK,OAAO,IAAI;wBAChB,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;;;;;;8BAGV,8OAAC;oBAAE,WAAU;8BAAqC,OAAO,IAAI;;;;;;;;;;;;IAGnE;IACA,IAAI,YAAY,YAAY;QAC1B,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACZ,gEACA;sBAGF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,OAAO,IAAI;4BAChB,KAAK,OAAO,IAAI;4BAChB,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;;;;;;;kCAIV,8OAAC;wBAAG,WAAU;kCACX,OAAO,IAAI;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,QAAQ,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW,OAAO,QAAQ;;;;;;4BAC5D,OAAO,WAAW,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAa,OAAO,WAAW;;;;;;;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCACV,OAAO,WAAW;;;;;;oBAGpB,eAAe,OAAO,OAAO,kBAC5B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,OAAO;kCAC/B,cAAA,8OAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;;gCACX;8CACe,8OAAC,sNAAA,CAAA,eAAY;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhD;IAGA,UAAU;IACV,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,OAAO,EAAE;kBAC3C,cAAA,8OAAC;YAAI,WAAW,eAAe,OAAO;;8BACpC,8OAAC;oBAAY,QAAQ;oBAAQ,SAAQ;;;;;;8BACrC,8OAAC;oBAAG,WAAU;8BAA2D,OAAO,IAAI;;;;;;8BACpF,8OAAC;oBAAa,QAAQ;;;;;;8BACtB,8OAAC;oBAAE,WAAU;8BACV,OAAO,WAAW;;;;;;8BAErB,8OAAC;oBAAI,WAAU;;wBACZ,yBACC,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAY,CAAC,iBAAiB,EAAE,OAAO,IAAI,EAAE;sCAC9C;;;;;;wBAIF,eAAe,OAAO,OAAO,kBAC5B,8OAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;4BACV,cAAY,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC;sCAE1C,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 1479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientsList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport ClientCard from './ClientCard';\nimport { getFeaturedClients, type Client } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function Clients() {\n  const [selectedClient, setSelectedClient] = useState<string | null>(null);\n  const [clients, setClients] = useState<Client[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchClients = async () => {\n      try {\n        const featuredClients = await getFeaturedClients();\n        setClients(featuredClients);\n      } catch (error) {\n        console.error('Error fetching clients:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchClients();\n  }, []);\n\n  const openClientModal = (clientId: string) => {\n    setSelectedClient(clientId);\n  };\n\n  const navigateClient = (direction: 'prev' | 'next') => {\n    if (selectedClient === null) return;\n\n    const currentIndex = clients.findIndex(client => client._id === selectedClient);\n    let newIndex;\n\n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : clients.length - 1;\n    } else {\n      newIndex = currentIndex < clients.length - 1 ? currentIndex + 1 : 0;\n    }\n\n    setSelectedClient(clients[newIndex]._id);\n  };\n\n  const selectedClientData = clients.find(client => client._id === selectedClient);\n\n  return (\n    <section id=\"clients\" className=\"py-20 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n            Trusted By\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Proud to have collaborated with diverse clients across various industries.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch\">\n            {[...Array(12)].map((_, index) => (\n              <div key={index} className=\"h-24 flex items-center justify-center\">\n                <div className=\"bg-card p-4 rounded-xl shadow-md w-full h-full flex items-center justify-center min-h-[80px]\">\n                  <div className=\"h-12 w-20 bg-gray-200 rounded animate-pulse\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : clients.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No clients available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch\">\n            {clients.map((client, index) => (\n              <Dialog key={client._id}>\n                <DialogTrigger asChild>\n                  <div className={`h-24 flex items-center justify-center animate-on-scroll stagger-${Math.min(index % 6 + 1, 6)}`}>\n                    <ClientCard\n                      client={client}\n                      variant=\"logo\"\n                      onClick={() => openClientModal(client._id)}\n                      className=\"min-h-[80px] hover:shadow-lg transition-all duration-300 hover:scale-105\"\n                    />\n                  </div>\n                </DialogTrigger>\n              <DialogContent className=\"max-w-2xl\">\n                <div className=\"relative\">\n                  {/* Navigation Arrows */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -left-12 top-1/2 -translate-y-1/2 z-10\"\n                    onClick={() => navigateClient('prev')}\n                  >\n                    <ChevronLeft size={20} />\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -right-12 top-1/2 -translate-y-1/2 z-10\"\n                    onClick={() => navigateClient('next')}\n                  >\n                    <ChevronRight size={20} />\n                  </Button>\n\n                  {selectedClientData && (\n                    <ClientCard\n                      client={selectedClientData}\n                      variant=\"detailed\"\n                      showWebsite={true}\n                    />\n                  )}\n                </div>\n              </DialogContent>\n            </Dialog>\n            ))}\n          </div>\n        )}\n\n        {/* View All Clients Button */}\n        <div className=\"text-center mt-12\">\n          <Link href=\"/clients\">\n            <Button className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n              View All Clients\n            </Button>\n          </Link>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8\">\n          {[\n            { number: '50+', label: 'Projects Completed' },\n            { number: '25+', label: 'Happy Clients' },\n            { number: '1M+', label: 'Views Generated' },\n            { number: '98%', label: 'Client Satisfaction' },\n          ].map((stat, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-heading font-bold text-secondary mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-muted-foreground font-medium\">\n                {stat.label}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,kBAAkB,MAAM,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD;gBAC/C,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,mBAAmB,MAAM;QAE7B,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;QAChE,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,QAAQ,MAAM,GAAG;QACpE,OAAO;YACL,WAAW,eAAe,QAAQ,MAAM,GAAG,IAAI,eAAe,IAAI;QACpE;QAEA,kBAAkB,OAAO,CAAC,SAAS,CAAC,GAAG;IACzC;IAEA,MAAM,qBAAqB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;IAEjE,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;QAAsB,KAAK;kBACzD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;gBAKhE,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;2BAFT;;;;;;;;;2BAOZ,QAAQ,MAAM,KAAK,kBACrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,kIAAA,CAAA,SAAM;;8CACL,8OAAC,kIAAA,CAAA,gBAAa;oCAAC,OAAO;8CACpB,cAAA,8OAAC;wCAAI,WAAW,CAAC,gEAAgE,EAAE,KAAK,GAAG,CAAC,QAAQ,IAAI,GAAG,IAAI;kDAC7G,cAAA,8OAAC,uJAAA,CAAA,UAAU;4CACT,QAAQ;4CACR,SAAQ;4CACR,SAAS,IAAM,gBAAgB,OAAO,GAAG;4CACzC,WAAU;;;;;;;;;;;;;;;;8CAIlB,8OAAC,kIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;;;;;;0DAErB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,MAAM;;;;;;;;;;;4CAGrB,oCACC,8OAAC,uJAAA,CAAA,UAAU;gDACT,QAAQ;gDACR,SAAQ;gDACR,aAAa;;;;;;;;;;;;;;;;;;2BAnCR,OAAO,GAAG;;;;;;;;;;8BA8C7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;sCAA0J;;;;;;;;;;;;;;;;8BAOhL,8OAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,QAAQ;4BAAO,OAAO;wBAAqB;wBAC7C;4BAAE,QAAQ;4BAAO,OAAO;wBAAgB;wBACxC;4BAAE,QAAQ;4BAAO,OAAO;wBAAkB;wBAC1C;4BAAE,QAAQ;4BAAO,OAAO;wBAAsB;qBAC/C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,8OAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BALL;;;;;;;;;;;;;;;;;;;;;AAatB", "debugId": null}}, {"offset": {"line": 1797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/reels/ReelsList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Play, X, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Dialog, DialogContent, DialogClose } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { getFeaturedReels, type Reel, getYouTubeShortsThumbnail } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function Reels() {\n  const [selectedReel, setSelectedReel] = useState<string | null>(null);\n  const [reels, setReels] = useState<Reel[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchReels = async () => {\n      try {\n        const featuredReels = await getFeaturedReels();\n        console.log(featuredReels);\n        // Show only first 12 shorts for featured section\n        const shortsWithThumbnails = featuredReels.slice(0, 12).map(reel => ({\n          ...reel,\n          thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id)\n        }));\n        setReels(shortsWithThumbnails);\n      } catch (error) {\n        console.error('Error fetching featured shorts:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchReels();\n  }, []);\n\n  const openReelModal = (reelId: string) => {\n    setSelectedReel(reelId);\n  };\n\n  const navigateReel = (direction: 'prev' | 'next') => {\n    if (!selectedReel) return;\n\n    const currentIndex = reels.findIndex(reel => reel.id === selectedReel);\n    let newIndex;\n\n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : reels.length - 1;\n    } else {\n      newIndex = currentIndex < reels.length - 1 ? currentIndex + 1 : 0;\n    }\n\n    setSelectedReel(reels[newIndex].id);\n  };\n\n  const selectedReelData = reels.find(reel => reel.id === selectedReel);\n\n  // if(!loading && reels.length !== 0) {\n  //   return console.log(reels);\n  // }\n\n  return (\n    <section id=\"shorts\" className=\"py-20 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-foreground mb-4\">\n            YouTube Shorts\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto font-body\">\n            Creative short-form content showcasing dynamic editing and storytelling.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {[...Array(12)].map((_, index) => (\n              <div key={index} className=\"bg-card rounded-xl overflow-hidden shadow-lg\">\n                <div className=\"aspect-[9/16] bg-gray-200 animate-pulse\"></div>\n                <div className=\"p-3\">\n                  <div className=\"h-4 bg-gray-200 rounded animate-pulse mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : reels.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground font-body\">No YouTube Shorts available at the moment.</p>\n          </div>\n        ) : (\n          <>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n              {reels.map((reel, index) => (\n                <div\n                  key={reel._id}\n                  className={`group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 animate-on-scroll animate-scale-in stagger-${Math.min(index % 6 + 1, 6)}`}\n                >\n                  <div className=\"relative aspect-[9/16] overflow-hidden\">\n                    <Image\n                      src={reel.thumbnail || ''}\n                      alt={reel.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Play Button Overlay */}\n                    <Button\n                      variant=\"ghost\"\n                      className=\"absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 h-full w-full rounded-none hover:bg-black/40\"\n                      onClick={() => openReelModal(reel.id)}\n                    >\n                      <div className=\"w-12 h-12 bg-accent hover:bg-accent/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg\">\n                        <Play size={16} className=\"text-accent-foreground ml-0.5\" fill=\"currentColor\" />\n                      </div>\n                    </Button>\n\n                    {/* Platform Badge */}\n                    <div className=\"absolute top-2 left-2\">\n                      <Badge variant=\"secondary\" className=\"bg-accent/90 text-accent-foreground text-xs font-accent\">\n                        YouTube Shorts\n                      </Badge>\n                    </div>\n                  </div>\n\n                  <div className=\"p-3\">\n                    <h3 className=\"text-sm font-heading font-semibold text-foreground mb-1 group-hover:text-accent transition-colors duration-300 line-clamp-2\">\n                      {reel.title}\n                    </h3>\n                    {reel.description && (\n                      <p className=\"text-muted-foreground text-xs leading-relaxed line-clamp-2 font-body\">\n                        {reel.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Single Dialog for all reels */}\n            <Dialog\n              open={selectedReel !== null}\n              onOpenChange={(open) => {\n                if (!open) {\n                  setSelectedReel(null);\n                }\n              }}\n            >\n              <DialogContent className=\"max-w-lg w-[95vw] p-0 bg-black border-0\">\n                <div className=\"relative\">\n                  {/* Navigation Arrows */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -left-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent\"\n                    onClick={() => navigateReel('prev')}\n                  >\n                    <ChevronLeft size={20} />\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -right-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent\"\n                    onClick={() => navigateReel('next')}\n                  >\n                    <ChevronRight size={20} />\n                  </Button>\n\n                  <DialogClose asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"icon\"\n                      className=\"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2\"\n                    >\n                      <X size={24} />\n                    </Button>\n                  </DialogClose>\n\n                  <div className=\"aspect-[9/16] max-h-[85vh] w-full\">\n                    {selectedReel && selectedReelData?.embedUrl && (\n                      <iframe\n                        src={selectedReelData.embedUrl}\n                        title={selectedReelData.title}\n                        className=\"w-full h-full rounded-lg\"\n                        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                        allowFullScreen\n                      />\n                    )}\n                  </div>\n                </div>\n              </DialogContent>\n            </Dialog>\n          </>\n        )}\n\n        {/* View More Button */}\n        <div className=\"text-center mt-12\">\n          <Button asChild className=\"px-8 py-3 rounded-full font-semibold font-accent transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n            <Link href=\"/reels\">\n              View All YouTube Shorts\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,iHAAA,CAAA,mBAAgB,AAAD;gBAC3C,QAAQ,GAAG,CAAC;gBACZ,iDAAiD;gBACjD,MAAM,uBAAuB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACnE,GAAG,IAAI;wBACP,WAAW,KAAK,SAAS,IAAI,CAAA,GAAA,iHAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,EAAE;oBAChE,CAAC;gBACD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;IAClB;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,cAAc;QAEnB,MAAM,eAAe,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACzD,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,MAAM,MAAM,GAAG;QAClE,OAAO;YACL,WAAW,eAAe,MAAM,MAAM,GAAG,IAAI,eAAe,IAAI;QAClE;QAEA,gBAAgB,KAAK,CAAC,SAAS,CAAC,EAAE;IACpC;IAEA,MAAM,mBAAmB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAExD,uCAAuC;IACvC,+BAA+B;IAC/B,IAAI;IAEJ,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;QAAsB,KAAK;kBACxD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;gBAK1E,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJT;;;;;;;;;2BASZ,MAAM,MAAM,KAAK,kBACnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;yCAGzD;;sCACE,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAEC,WAAW,CAAC,2JAA2J,EAAE,KAAK,GAAG,CAAC,QAAQ,IAAI,GAAG,IAAI;;sDAErM,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,SAAS,IAAI;oDACvB,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,cAAc,KAAK,EAAE;8DAEpC,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;4DAAgC,MAAK;;;;;;;;;;;;;;;;8DAKnE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAA0D;;;;;;;;;;;;;;;;;sDAMnG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;gDAEZ,KAAK,WAAW,kBACf,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;;mCApClB,KAAK,GAAG;;;;;;;;;;sCA6CnB,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAM,iBAAiB;4BACvB,cAAc,CAAC;gCACb,IAAI,CAAC,MAAM;oCACT,gBAAgB;gCAClB;4BACF;sCAEA,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;;;;;;sDAErB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;sDAGtB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,OAAO;sDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,MAAM;;;;;;;;;;;;;;;;sDAIb,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,kBAAkB,0BACjC,8OAAC;gDACC,KAAK,iBAAiB,QAAQ;gDAC9B,OAAO,iBAAiB,KAAK;gDAC7B,WAAU;gDACV,OAAM;gDACN,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAW/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC", "debugId": null}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogCard.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\nimport { Calendar, Clock, User } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { formatDate, type BlogPost } from '@/lib/api';\n\ninterface PostCardProps {\n  post: BlogPost;\n  variant?: 'default' | 'featured' | 'compact';\n  showExcerpt?: boolean;\n  showAuthor?: boolean;\n  className?: string;\n}\n\nexport default function PostCard({\n  post,\n  variant = 'default',\n  showExcerpt = true,\n  showAuthor = true,\n  className = ''\n}: PostCardProps) {\n  const cardClasses = {\n    default: 'bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',\n    featured: 'bg-card rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 border-2 border-accent',\n    compact: 'bg-card rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'\n  };\n\n  const imageClasses = {\n    default: 'aspect-video',\n    featured: 'aspect-video',\n    compact: 'aspect-[4/3]'\n  };\n\n  const contentClasses = {\n    default: 'p-6',\n    featured: 'p-8',\n    compact: 'p-4'\n  };\n\n  const titleClasses = {\n    default: 'text-xl font-heading font-semibold text-primary mb-3',\n    featured: 'text-2xl font-heading font-bold text-primary mb-4',\n    compact: 'text-lg font-heading font-semibold text-primary mb-2'\n  };\n\n  return (\n    <article className={`group ${cardClasses[variant]} ${className}`}>\n      <Link href={`/blog/${post.slug}`}>\n        {/* Featured Image */}\n        <div className={`relative ${imageClasses[variant]} overflow-hidden`}>\n          {post.thumbnail ? (\n            <Image\n              src={post.thumbnail}\n              alt={post.title}\n              fill\n              className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n            />\n          ) : (\n            <div className=\"w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center\">\n              <div className=\"text-primary-foreground text-6xl font-heading font-bold opacity-20\">\n                {post.title.charAt(0)}\n              </div>\n            </div>\n          )}\n\n          {/* Overlay with badges */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n          {/* Category Badge */}\n          <div className=\"absolute top-4 left-4\">\n            <Badge variant=\"secondary\" className=\"bg-secondary/90 text-secondary-foreground backdrop-blur-sm\">\n              {post.category}\n            </Badge>\n          </div>\n\n          {/* Featured Badge */}\n          {post.featured && (\n            <div className=\"absolute top-4 right-4\">\n              <Badge variant=\"default\" className=\"bg-accent text-accent-foreground backdrop-blur-sm\">\n                Featured\n              </Badge>\n            </div>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className={contentClasses[variant]}>\n          {/* Title */}\n          <h3 className={`${titleClasses[variant]} group-hover:text-secondary transition-colors duration-300 line-clamp-2`}>\n            {post.title}\n          </h3>\n\n          {/* Meta Information */}\n          <div className=\"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3\">\n            <div className=\"flex items-center gap-1\">\n              <Calendar size={14} />\n              <span>{formatDate(post.createdAt)}</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Clock size={14} />\n              <span>{post.readTime} min read</span>\n            </div>\n            {showAuthor && (\n              <div className=\"flex items-center gap-1\">\n                <User size={14} />\n                <span>Uttam Rimal</span>\n              </div>\n            )}\n          </div>\n\n          {/* Excerpt */}\n          {showExcerpt && post.excerpt && (\n            <p className={`text-muted-foreground leading-relaxed mb-4 ${variant === 'compact' ? 'text-sm line-clamp-2' : 'line-clamp-3'\n              }`}>\n              {post.excerpt}\n            </p>\n          )}\n\n          {/* Tags */}\n          {post.tags && post.tags.length > 0 && (\n            <div className=\"flex flex-wrap gap-2 mb-4\">\n              {post.tags.slice(0, variant === 'compact' ? 2 : 3).map((tag) => (\n                <Badge key={tag} variant=\"outline\" className=\"text-xs hover:bg-primary hover:text-primary-foreground transition-colors\">\n                  #{tag}\n                </Badge>\n              ))}\n              {post.tags.length > (variant === 'compact' ? 2 : 3) && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{post.tags.length - (variant === 'compact' ? 2 : 3)}\n                </Badge>\n              )}\n            </div>\n          )}\n\n          {/* Read More Link */}\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-primary font-semibold text-sm group-hover:text-secondary transition-colors duration-300\">\n              Read More →\n            </span>\n\n            {/* Status indicator for featured variant */}\n            {variant === 'featured' && (\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-2 h-2 rounded-full ${post.status === 'published' ? 'bg-success' : 'bg-warning'\n                  }`} />\n                <span className=\"text-xs text-muted-foreground capitalize\">\n                  {post.status}\n                </span>\n              </div>\n            )}\n          </div>\n        </div>\n      </Link>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AAUe,SAAS,SAAS,EAC/B,IAAI,EACJ,UAAU,SAAS,EACnB,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,YAAY,EAAE,EACA;IACd,MAAM,cAAc;QAClB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;kBAC9D,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;8BAE9B,8OAAC;oBAAI,WAAW,CAAC,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC;;wBAChE,KAAK,SAAS,iBACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAK,KAAK,KAAK;4BACf,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;iDAGR,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,MAAM,CAAC;;;;;;;;;;;sCAMzB,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,KAAK,QAAQ;;;;;;;;;;;wBAKjB,KAAK,QAAQ,kBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAoD;;;;;;;;;;;;;;;;;8BAQ7F,8OAAC;oBAAI,WAAW,cAAc,CAAC,QAAQ;;sCAErC,8OAAC;4BAAG,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,uEAAuE,CAAC;sCAC7G,KAAK,KAAK;;;;;;sCAIb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,8OAAC;sDAAM,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8CAElC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,8OAAC;;gDAAM,KAAK,QAAQ;gDAAC;;;;;;;;;;;;;gCAEtB,4BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;wBAMX,eAAe,KAAK,OAAO,kBAC1B,8OAAC;4BAAE,WAAW,CAAC,2CAA2C,EAAE,YAAY,YAAY,yBAAyB,gBACzG;sCACD,KAAK,OAAO;;;;;;wBAKhB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC,oBACtD,8OAAC,iIAAA,CAAA,QAAK;wCAAW,SAAQ;wCAAU,WAAU;;4CAA2E;4CACpH;;uCADQ;;;;;gCAIb,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC,mBAChD,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC;;;;;;;;;;;;;sCAO3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA+F;;;;;;gCAK9G,YAAY,4BACX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,MAAM,KAAK,cAAc,eAAe,cACjF;;;;;;sDACJ,8OAAC;4CAAK,WAAU;sDACb,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B", "debugId": null}}, {"offset": {"line": 2522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2619, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport PostCard from './BlogCard';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { getBlogPosts, type BlogPost } from '@/lib/api';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function Blog() {\n  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);\n  const [loading, setLoading] = useState(true);\n  const containerRef = useStaggeredScrollAnimation();\n\n  useEffect(() => {\n    const fetchBlogPosts = async () => {\n      try {\n        const posts = await getBlogPosts();\n        // Show only first 6 posts for homepage\n        setBlogPosts(posts.slice(0, 6));\n      } catch (error) {\n        console.error('Error fetching blog posts:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchBlogPosts();\n  }, []);\n\n  return (\n    <section id=\"blog\" className=\"py-20 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4 animate-slide-up\">\n            Insights & Tips\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto animate-fade-in stagger-2\">\n            Sharing knowledge on video editing, storytelling, and creative techniques.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <Card key={index} className=\"bg-white overflow-hidden\">\n                <CardHeader className=\"p-0\">\n                  <div className=\"aspect-video bg-gray-200 animate-pulse\"></div>\n                </CardHeader>\n                <CardContent className=\"p-6\">\n                  <div className=\"space-y-3\">\n                    <div className=\"h-4 bg-gray-200 rounded animate-pulse\"></div>\n                    <div className=\"h-6 bg-gray-200 rounded animate-pulse\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : blogPosts.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No blog posts available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {blogPosts.map((post, index) => (\n              <div\n                key={post._id}\n                className={`animate-on-scroll animate-scale-in stagger-${Math.min(index % 6 + 1, 6)}`}\n              >\n                <PostCard\n                  post={post}\n                  variant=\"default\"\n                  showExcerpt={true}\n                  showAuthor={false}\n                />\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* View All Posts Button */}\n        <div className=\"text-center mt-12\">\n          <Button asChild className=\"px-8 py-3 rounded-full font-semibold font-accent transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n            <Link href=\"/blog\">View All Posts</Link>\n          </Button>\n          {/* <Link\n            href=\"/blog\"\n            className=\"inline-block bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\"\n          >\n            View All Posts\n          </Link> */}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,QAAQ,MAAM,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD;gBAC/B,uCAAuC;gBACvC,aAAa,MAAM,KAAK,CAAC,GAAG;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;QAAsB,KAAK;kBACtD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiF;;;;;;sCAG/F,8OAAC;4BAAE,WAAU;sCAA4E;;;;;;;;;;;;gBAK1F,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC,gIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;2BARV;;;;;;;;;2BAcb,UAAU,MAAM,KAAK,kBACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;4BAEC,WAAW,CAAC,2CAA2C,EAAE,KAAK,GAAG,CAAC,QAAQ,IAAI,GAAG,IAAI;sCAErF,cAAA,8OAAC,kJAAA,CAAA,UAAQ;gCACP,MAAM;gCACN,SAAQ;gCACR,aAAa;gCACb,YAAY;;;;;;2BAPT,KAAK,GAAG;;;;;;;;;;8BAerB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAO,OAAO;wBAAC,WAAU;kCACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/B", "debugId": null}}, {"offset": {"line": 2834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport { Quote, Star, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { getFeaturedTestimonials, type Testimonial } from '@/lib/api';\n\n\nexport default function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchTestimonials = async () => {\n      try {\n        const featuredTestimonials = await getFeaturedTestimonials();\n\n        // Check if we got testimonials, if not use fallback data\n        if (featuredTestimonials && featuredTestimonials.length > 0) {\n          setTestimonials(featuredTestimonials);\n        } else {\n          // Fallback testimonials data\n          const fallbackTestimonials = [\n            {\n              _id: '1',\n              name: '<PERSON><PERSON>',\n              slug: 'shahid-kathariya',\n              role: 'Vlogger/YouTuber',\n              content: \"Uttam's editing skills brought our project to life. His attention to detail and creative flair exceeded our expectations.\",\n              rating: 5,\n              featured: true,\n              status: 'published' as const,\n              order: 1,\n              createdAt: new Date().toISOString(),\n              updatedAt: new Date().toISOString()\n            },\n            {\n              _id: '2',\n              name: 'Utsab Pandey',\n              slug: 'utsab-pandey',\n              role: 'Professional Designer',\n              content: \"Working with Uttam was a pleasure. He understood our vision from the start and delivered a final product that was both professional and highly engaging.\",\n              rating: 5,\n              featured: true,\n              status: 'published' as const,\n              order: 2,\n              createdAt: new Date().toISOString(),\n              updatedAt: new Date().toISOString()\n            },\n            {\n              _id: '3',\n              name: 'Sarah Johnson',\n              slug: 'sarah-johnson',\n              role: 'Marketing Director',\n              company: 'Creative Solutions Inc.',\n              content: \"Uttam's creativity and technical skills are truly impressive. He transformed raw footage into a compelling narrative that resonated perfectly with our target audience.\",\n              rating: 5,\n              featured: true,\n              status: 'published' as const,\n              order: 3,\n              createdAt: new Date().toISOString(),\n              updatedAt: new Date().toISOString()\n            }\n          ];\n          setTestimonials(fallbackTestimonials);\n        }\n      } catch (error) {\n        console.error('Error fetching testimonials:', error);\n\n        // Use fallback data on error\n        const fallbackTestimonials = [\n          {\n            _id: '1',\n            name: 'Shahid Kathariya',\n            slug: 'shahid-kathariya',\n            role: 'Vlogger/YouTuber',\n            content: \"Uttam's editing skills brought our project to life. His attention to detail and creative flair exceeded our expectations.\",\n            rating: 5,\n            featured: true,\n            status: 'published' as const,\n            order: 1,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          }\n        ];\n        setTestimonials(fallbackTestimonials);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTestimonials();\n  }, []);\n\n  useEffect(() => {\n    if (!isAutoPlaying || testimonials.length === 0) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) =>\n        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n      );\n    }, 6000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, testimonials.length]);\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10 seconds\n  };\n\n  const goToPrevious = () => {\n    const newIndex = currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1;\n    goToSlide(newIndex);\n  };\n\n  const goToNext = () => {\n    const newIndex = currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1;\n    goToSlide(newIndex);\n  };\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  // Add safety check for currentTestimonial\n  if (!currentTestimonial && !loading) {\n    console.log('No current testimonial found. Testimonials:', testimonials, 'Current Index:', currentIndex);\n  }\n\n  if (loading) {\n    return (\n      <section id=\"testimonials\" className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n              What Clients Say\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Feedback from collaborators and clients who trusted me with their vision.\n            </p>\n          </div>\n          <div className=\"max-w-3xl mx-auto\">\n            <Card className=\"bg-card border-border shadow-lg h-[280px] md:h-[240px]\">\n              <CardContent className=\"p-6 md:p-8 h-full\">\n                <div className=\"flex flex-col md:flex-row items-center md:items-start gap-6 h-full\">\n                  <div className=\"flex flex-col items-center md:items-start flex-shrink-0 md:w-32\">\n                    <div className=\"w-16 h-16 bg-muted/20 rounded-full animate-pulse mb-3\"></div>\n                    <div className=\"h-4 bg-muted/20 rounded animate-pulse w-24 mb-1\"></div>\n                    <div className=\"h-3 bg-muted/20 rounded animate-pulse w-20\"></div>\n                  </div>\n                  <div className=\"flex-1 flex flex-col h-full space-y-3\">\n                    <div className=\"h-8 bg-muted/20 rounded animate-pulse w-8 flex-shrink-0\"></div>\n                    <div className=\"flex-1 space-y-3\">\n                      <div className=\"h-4 bg-muted/20 rounded animate-pulse w-full\"></div>\n                      <div className=\"h-4 bg-muted/20 rounded animate-pulse w-4/5\"></div>\n                      <div className=\"h-4 bg-muted/20 rounded animate-pulse w-3/4\"></div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (testimonials.length === 0) {\n    return (\n      <section id=\"testimonials\" className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n              What Clients Say\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Feedback from collaborators and clients who trusted me with their vision.\n            </p>\n          </div>\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No testimonials available at the moment.</p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-background\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4\">\n            What Clients Say\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Feedback from collaborators and clients who trusted me with their vision.\n          </p>\n        </div>\n\n        <div className=\"max-w-3xl mx-auto\">\n          <div className=\"relative\">\n            {/* Navigation Arrows */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute -left-12 top-1/2 -translate-y-1/2 z-10 text-foreground hover:text-accent hover:bg-accent/10 w-10 h-10\"\n              onClick={goToPrevious}\n            >\n              <ChevronLeft size={20} />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute -right-12 top-1/2 -translate-y-1/2 z-10 text-foreground hover:text-accent hover:bg-accent/10 w-10 h-10\"\n              onClick={goToNext}\n            >\n              <ChevronRight size={20} />\n            </Button>\n\n            {/* Testimonial Card */}\n            <Card className=\"bg-card border-border shadow-lg h-[280px] md:h-[240px] hover:shadow-xl transition-all duration-300 opacity-100\">\n              <CardContent className=\"p-6 md:p-8 h-full\">\n                <div className=\"flex flex-col md:flex-row items-center md:items-start gap-6 h-full\">\n                  {/* Left side - Avatar and Info */}\n                  <div className=\"flex flex-col items-center md:items-start flex-shrink-0 md:w-32\">\n                    {/* Avatar */}\n                    <div className=\"relative w-16 h-16 rounded-full overflow-hidden border-3 border-accent/30 mb-3\">\n                      {currentTestimonial?.avatar ? (\n                        <Image\n                          src={currentTestimonial.avatar}\n                          alt={currentTestimonial.name || 'Client'}\n                          fill\n                          className=\"object-cover\"\n                        />\n                      ) : (\n                        <div className=\"w-full h-full bg-accent flex items-center justify-center text-accent-foreground font-heading font-bold text-lg\">\n                          {currentTestimonial?.name?.charAt(0) || 'C'}\n                        </div>\n                      )}\n                    </div>\n\n                    {/* Author Info */}\n                    <div className=\"text-center md:text-left\">\n                      <cite className=\"text-accent font-semibold text-base not-italic block line-clamp-1\">\n                        {currentTestimonial?.name || 'Client Name'}\n                      </cite>\n                      <div className=\"text-muted-foreground text-sm line-clamp-2\">\n                        {currentTestimonial?.role || 'Client'}\n                        {currentTestimonial?.company && (\n                          <span className=\"block md:inline\"> {currentTestimonial.company}</span>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Rating */}\n                    {currentTestimonial?.rating && (\n                      <div className=\"flex gap-1 mt-2\">\n                        {[...Array(5)].map((_, i) => (\n                          <Star\n                            key={i}\n                            size={16}\n                            className={`${i < (currentTestimonial.rating || 0)\n                              ? 'text-warning fill-current'\n                              : 'text-muted-foreground/30'\n                              }`}\n                          />\n                        ))}\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Right side - Content */}\n                  <div className=\"flex-1 flex flex-col h-full md:min-h-0\">\n                    {/* Quote Icon */}\n                    <Quote size={32} className=\"text-accent opacity-40 mb-3 flex-shrink-0\" />\n\n                    {/* Testimonial Content */}\n                    <blockquote className=\"text-base md:text-lg leading-relaxed text-foreground italic flex-1 overflow-hidden\">\n                      <div className=\"line-clamp-4 md:line-clamp-3\">\n                        &ldquo;{currentTestimonial?.content || 'Loading testimonial...'}&rdquo;\n                      </div>\n                    </blockquote>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center gap-3 mt-8\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentIndex\n                  ? 'bg-accent scale-125'\n                  : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'\n                  }`}\n                aria-label={`Go to testimonial ${index + 1}`}\n              />\n            ))}\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 pt-8 border-t border-border/20\">\n            {[\n              { number: '98%', label: 'Client Satisfaction' },\n              { number: '50+', label: 'Projects Delivered' },\n              { number: '25+', label: 'Happy Clients' },\n              { number: '1M+', label: 'Views Generated' },\n            ].map((stat, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-heading font-bold text-accent mb-1\">\n                  {stat.number}\n                </div>\n                <div className=\"text-muted-foreground font-medium text-xs md:text-sm\">\n                  {stat.label}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,uBAAuB,MAAM,CAAA,GAAA,iHAAA,CAAA,0BAAuB,AAAD;gBAEzD,yDAAyD;gBACzD,IAAI,wBAAwB,qBAAqB,MAAM,GAAG,GAAG;oBAC3D,gBAAgB;gBAClB,OAAO;oBACL,6BAA6B;oBAC7B,MAAM,uBAAuB;wBAC3B;4BACE,KAAK;4BACL,MAAM;4BACN,MAAM;4BACN,MAAM;4BACN,SAAS;4BACT,QAAQ;4BACR,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,WAAW,IAAI,OAAO,WAAW;4BACjC,WAAW,IAAI,OAAO,WAAW;wBACnC;wBACA;4BACE,KAAK;4BACL,MAAM;4BACN,MAAM;4BACN,MAAM;4BACN,SAAS;4BACT,QAAQ;4BACR,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,WAAW,IAAI,OAAO,WAAW;4BACjC,WAAW,IAAI,OAAO,WAAW;wBACnC;wBACA;4BACE,KAAK;4BACL,MAAM;4BACN,MAAM;4BACN,MAAM;4BACN,SAAS;4BACT,SAAS;4BACT,QAAQ;4BACR,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,WAAW,IAAI,OAAO,WAAW;4BACjC,WAAW,IAAI,OAAO,WAAW;wBACnC;qBACD;oBACD,gBAAgB;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAE9C,6BAA6B;gBAC7B,MAAM,uBAAuB;oBAC3B;wBACE,KAAK;wBACL,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,QAAQ;wBACR,UAAU;wBACV,QAAQ;wBACR,OAAO;wBACP,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC;iBACD;gBACD,gBAAgB;YAClB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,aAAa,MAAM,KAAK,GAAG;QAEjD,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;QAE5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe,aAAa,MAAM;KAAC;IAEvC,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO,QAAQ,oCAAoC;IACvF;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,iBAAiB,IAAI,aAAa,MAAM,GAAG,IAAI,eAAe;QAC/E,UAAU;IACZ;IAEA,MAAM,WAAW;QACf,MAAM,WAAW,iBAAiB,aAAa,MAAM,GAAG,IAAI,IAAI,eAAe;QAC/E,UAAU;IACZ;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,0CAA0C;IAC1C,IAAI,CAAC,sBAAsB,CAAC,SAAS;QACnC,QAAQ,GAAG,CAAC,+CAA+C,cAAc,kBAAkB;IAC7F;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,IAAG;YAAe,WAAU;sBACnC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgE;;;;;;0CAG9E,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUnC;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,qBACE,8OAAC;YAAQ,IAAG;YAAe,WAAU;sBACnC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgE;;;;;;0CAG9E,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;IAKvD;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,MAAM;;;;;;;;;;;8CAErB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,MAAM;;;;;;;;;;;8CAItB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;sEACZ,oBAAoB,uBACnB,8OAAC,6HAAA,CAAA,UAAK;gEACJ,KAAK,mBAAmB,MAAM;gEAC9B,KAAK,mBAAmB,IAAI,IAAI;gEAChC,IAAI;gEACJ,WAAU;;;;;qFAGZ,8OAAC;gEAAI,WAAU;0EACZ,oBAAoB,MAAM,OAAO,MAAM;;;;;;;;;;;sEAM9C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,oBAAoB,QAAQ;;;;;;8EAE/B,8OAAC;oEAAI,WAAU;;wEACZ,oBAAoB,QAAQ;wEAC5B,oBAAoB,yBACnB,8OAAC;4EAAK,WAAU;;gFAAkB;gFAAE,mBAAmB,OAAO;;;;;;;;;;;;;;;;;;;wDAMnE,oBAAoB,wBACnB,8OAAC;4DAAI,WAAU;sEACZ;mEAAI,MAAM;6DAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oEAEH,MAAM;oEACN,WAAW,GAAG,IAAI,CAAC,mBAAmB,MAAM,IAAI,CAAC,IAC7C,8BACA,4BACA;mEALC;;;;;;;;;;;;;;;;8DAaf,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAG3B,8OAAC;4DAAW,WAAU;sEACpB,cAAA,8OAAC;gEAAI,WAAU;;oEAA+B;oEACpC,oBAAoB,WAAW;oEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU9E,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,iDAAiD,EAAE,UAAU,eACrE,wBACA,uDACA;oCACJ,cAAY,CAAC,kBAAkB,EAAE,QAAQ,GAAG;mCANvC;;;;;;;;;;sCAYX,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,QAAQ;oCAAO,OAAO;gCAAsB;gCAC9C;oCAAE,QAAQ;oCAAO,OAAO;gCAAqB;gCAC7C;oCAAE,QAAQ;oCAAO,OAAO;gCAAgB;gCACxC;oCAAE,QAAQ;oCAAO,OAAO;gCAAkB;6BAC3C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCALL;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcxB", "debugId": null}}, {"offset": {"line": 3488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Send, Mail, Phone, MapPin, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const containerRef = useStaggeredScrollAnimation();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n      setTimeout(() => setSubmitStatus('idle'), 5000);\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: 'Email',\n      value: '<EMAIL>',\n      href: 'mailto:<EMAIL>'\n    },\n    {\n      icon: Phone,\n      label: 'Phone',\n      value: '+977 ************',\n      href: 'tel:+9779840692118'\n    },\n    {\n      icon: MapPin,\n      label: 'Location',\n      value: 'Kathmandu, Nepal',\n      href: '#'\n    }\n  ];\n\n  const socialLinks = [\n    { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },\n    { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },\n    { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },\n    { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-16 bg-background\" ref={containerRef}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12 animate-on-scroll\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-primary mb-4 animate-slide-up\">\n            Let&apos;s Create Together\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto animate-fade-in stagger-2\">\n            Have a project in mind? Let&apos;s discuss how we can bring your vision to life through compelling video content.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n          {/* Contact Form */}\n          <Card className=\"shadow-lg animate-on-scroll animate-slide-in-left stagger-3 hover:shadow-xl transition-all duration-300\">\n            <CardHeader>\n              <CardTitle className=\"text-2xl font-heading text-primary\">\n                Send a Message\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleSubmit} className=\"space-y-5\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"name\">Your Name *</Label>\n                    <Input\n                      id=\"name\"\n                      name=\"name\"\n                      type=\"text\"\n                      placeholder=\"e.g., Jane Doe\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"focus:ring-ring focus:border-ring\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"email\">Your Email *</Label>\n                    <Input\n                      id=\"email\"\n                      name=\"email\"\n                      type=\"email\"\n                      placeholder=\"e.g., <EMAIL>\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"focus:ring-ring focus:border-ring\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"subject\">Subject</Label>\n                  <Input\n                    id=\"subject\"\n                    name=\"subject\"\n                    type=\"text\"\n                    placeholder=\"e.g., Video Editing Inquiry\"\n                    value={formData.subject}\n                    onChange={handleInputChange}\n                    className=\"focus:ring-ring focus:border-ring\"\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"message\">Your Message *</Label>\n                  <Textarea\n                    id=\"message\"\n                    name=\"message\"\n                    placeholder=\"Tell me about your project...\"\n                    value={formData.message}\n                    onChange={handleInputChange}\n                    required\n                    rows={5}\n                    className=\"focus:ring-ring focus:border-ring resize-none\"\n                  />\n                </div>\n\n                <Button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  variant=\"secondary\"\n                  className=\"w-full py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 disabled:scale-100\"\n                >\n                  {isSubmitting ? (\n                    'Sending...'\n                  ) : (\n                    <>\n                      Send Message\n                      <Send size={18} className=\"ml-2\" />\n                    </>\n                  )}\n                </Button>\n\n                {/* Status Messages */}\n                {submitStatus === 'success' && (\n                  <div className=\"text-success text-center font-medium\">\n                    Thank you! Your message has been sent successfully.\n                  </div>\n                )}\n                {submitStatus === 'error' && (\n                  <div className=\"text-destructive text-center font-medium\">\n                    Sorry, there was an error sending your message. Please try again.\n                  </div>\n                )}\n              </form>\n            </CardContent>\n          </Card>\n\n          {/* Contact Information */}\n          <div className=\"space-y-6 animate-on-scroll animate-slide-in-right stagger-4\">\n            <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-heading text-primary\">\n                  Get in Touch\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-5\">\n                {contactInfo.map((info, index) => (\n                  <div key={index} className=\"flex items-center gap-4\">\n                    <div className=\"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center\">\n                      <info.icon size={20} className=\"text-primary\" />\n                    </div>\n                    <div>\n                      <div className=\"font-semibold text-primary\">{info.label}</div>\n                      {info.href !== '#' ? (\n                        <a\n                          href={info.href}\n                          className=\"text-muted-foreground hover:text-secondary transition-colors\"\n                        >\n                          {info.value}\n                        </a>\n                      ) : (\n                        <div className=\"text-muted-foreground\">{info.value}</div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </CardContent>\n            </Card>\n\n            {/* Social Links */}\n            <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in stagger-5\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-heading text-primary\">\n                  Follow Me\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex gap-4\">\n                  {socialLinks.map(({ icon: Icon, href, label }) => (\n                    <a\n                      key={label}\n                      href={href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-12 h-12 bg-primary/10 hover:bg-accent text-primary hover:text-accent-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110\"\n                      aria-label={label}\n                    >\n                      <Icon size={20} />\n                    </a>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* WhatsApp Quick Contact */}\n            <Card className=\"shadow-lg bg-success/10 border-success/20 hover:shadow-xl transition-all duration-300 animate-glow animate-scale-in stagger-6\">\n              <CardContent className=\"p-6\">\n                <div className=\"text-center\">\n                  <h3 className=\"font-heading font-semibold text-success mb-2\">\n                    Quick Chat on WhatsApp\n                  </h3>\n                  <p className=\"text-success/80 text-sm mb-4\">\n                    Need immediate assistance? Let&apos;s chat!\n                  </p>\n                  <Button\n                    asChild\n                    className=\"bg-success hover:bg-success/90 text-success-foreground px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105\"\n                  >\n                    <a\n                      href=\"https://wa.me/9779840692118\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"inline-flex items-center gap-2\"\n                    >\n                      <Phone size={18} />\n                      Chat on WhatsApp\n                    </a>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,gBAAgB;YAChB,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,EAAE,OAAM;YACN,gBAAgB;QAClB,SAAU;YACR,gBAAgB;YAChB,WAAW,IAAM,gBAAgB,SAAS;QAC5C;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAA0C,OAAO;QAAW;QACpF;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAwC,OAAO;QAAY;QACpF;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAwC,OAAO;QAAU;QAChF;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAuC,OAAO;QAAW;KAClF;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;QAAsB,KAAK;kBACzD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiF;;;;;;sCAG/F,8OAAC;4BAAE,WAAU;sCAA4E;;;;;;;;;;;;8BAK3F,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqC;;;;;;;;;;;8CAI5D,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;;;;;;;;;;;;0DAId,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU;gDACV,SAAQ;gDACR,WAAU;0DAET,eACC,6BAEA;;wDAAE;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;;4CAM/B,iBAAiB,2BAChB,8OAAC;gDAAI,WAAU;0DAAuC;;;;;;4CAIvD,iBAAiB,yBAChB,8OAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;sCASlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAqC;;;;;;;;;;;sDAI5D,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,KAAK,IAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;;;;;;sEAEjC,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B,KAAK,KAAK;;;;;;gEACtD,KAAK,IAAI,KAAK,oBACb,8OAAC;oEACC,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;yFAGb,8OAAC;oEAAI,WAAU;8EAAyB,KAAK,KAAK;;;;;;;;;;;;;mDAd9C;;;;;;;;;;;;;;;;8CAuBhB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAqC;;;;;;;;;;;sDAI5D,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC3C,8OAAC;wDAEC,MAAM;wDACN,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,cAAY;kEAEZ,cAAA,8OAAC;4DAAK,MAAM;;;;;;uDAPP;;;;;;;;;;;;;;;;;;;;;8CAef,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+C;;;;;;8DAG7D,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;8DAG5C,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;8DAEV,cAAA,8OAAC;wDACC,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC", "debugId": null}}, {"offset": {"line": 4160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/ScrollToTop.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <Button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n      aria-label=\"Scroll to top\"\n    >\n      <ArrowUp size={20} />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;;;;;;AAGrB", "debugId": null}}]}