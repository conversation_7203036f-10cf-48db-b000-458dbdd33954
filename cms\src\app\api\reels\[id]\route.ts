import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Short from "@/models/Short";
import { requireAuth } from "@/lib/auth";
import { setCorsHeaders } from "@/lib/cors";
import slugify from "slugify";

// GET /api/reels/[id] - Get single reel
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;

    // Find by ID or slug
    const short = await Short.findOne({
      $or: [{ _id: id }, { slug: id }],
    }).lean();

    if (!short) {
      const response = NextResponse.json({ error: "Short not found" }, { status: 404 });
      setCorsHeaders(response, request.headers.get('origin'));
      return response;
    }

    // Add auto-generated thumbnail
    const shortWithThumbnail = {
      ...short,
      thumbnail:
        short.platform === "youtube" && short.id
          ? `https://img.youtube.com/vi/${short.id}/maxresdefault.jpg`
          : "/images/placeholder.svg",
    };

    const response = NextResponse.json({
      success: true,
      data: shortWithThumbnail,
    });
    setCorsHeaders(response, request.headers.get('origin'));
    return response;
  } catch (error) {
    console.error("Get reel error:", error);
    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
    setCorsHeaders(response, request.headers.get('origin'));
    return response;
  }
}

// PUT /api/reels/[id] - Update reel
export const PUT = requireAuth(
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      await connectDB();

      const { id } = await params;
      const data = await request.json();

      const short = await Short.findById(id);
      if (!short) {
        return NextResponse.json({ error: "Short not found" }, { status: 404 });
      }

      // Handle slug update if title changed
      if (data.title && data.title !== short.title) {
        const baseSlug = slugify(data.title, {
          lower: true,
          strict: true,
          remove: /[*+~.()'"!:@]/g,
        });

        // Ensure unique slug (excluding current short)
        let slug = baseSlug;
        let counter = 1;
        while (await Short.findOne({ slug, _id: { $ne: id } })) {
          slug = `${baseSlug}-${counter}`;
          counter++;
        }
        data.slug = slug;
      }

      // Update embed URL for YouTube if id changed
      if (data.id && data.id !== short.id) {
        const reelId = data.id;
        data.embedUrl = `https://www.youtube.com/embed/${reelId}`;
      }

      // Ensure platform is always YouTube
      if (data.platform) {
        data.platform = "youtube";
      }

      // Update short
      const updatedShort = await Short.findByIdAndUpdate(
        id,
        { ...data, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      return NextResponse.json({
        success: true,
        data: updatedShort,
      });
    } catch (error) {
      console.error("Update reel error:", error);
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      );
    }
  }
);

// DELETE /api/reels/[id] - Delete reel
export const DELETE = requireAuth(
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      await connectDB();

      const { id } = await params;

      const short = await Short.findById(id);
      if (!short) {
        return NextResponse.json({ error: "Short not found" }, { status: 404 });
      }

      await Short.findByIdAndDelete(id);

      return NextResponse.json({
        success: true,
        message: "Short deleted successfully",
      });
    } catch (error) {
      console.error("Delete reel error:", error);
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      );
    }
  }
);
