(()=>{var e={};e.id=144,e.ids=[144],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(56037),n=t.n(s);let o=new s.Schema({email:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},password:{type:String,required:!0,minlength:6},name:{type:String,required:!0,trim:!0},role:{type:String,enum:["admin","editor"],default:"editor"},isActive:{type:Boolean,default:!0},lastLogin:{type:Date}},{timestamps:!0});o.index({email:1}),o.index({role:1});let i=n().models.User||n().model("User",o)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(56037),n=t.n(s);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose||{conn:null,promise:null};global.mongoose||(global.mongoose=i);let a=async function(){if(i.conn)return i.conn;i.promise||(i.promise=n().connect(o,{bufferCommands:!1}).then(e=>(console.log("✅ Connected to MongoDB"),e)));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},78335:()=>{},80754:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(75745),c=t(17063);async function p(){try{await (0,u.A)();let e=await c.A.findOne({role:"admin"});return a.NextResponse.json({success:!0,status:{database:"connected",adminUser:e?"exists":"missing",environment:"production",mongoUri:process.env.MONGODB_URI?"configured":"missing"}})}catch(e){return a.NextResponse.json({success:!1,status:{database:"disconnected",adminUser:"unknown",environment:"production",mongoUri:process.env.MONGODB_URI?"configured":"missing"},error:e instanceof Error?e.message:"Unknown error"})}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/status/route",pathname:"/api/status",filename:"route",bundlePath:"app/api/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\status\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=d;function v(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(80754));module.exports=s})();