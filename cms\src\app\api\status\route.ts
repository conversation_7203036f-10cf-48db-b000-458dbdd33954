import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import User from "@/models/User";

export async function GET() {
  try {
    // Test database connection
    await connectDB();

    // Check if admin user exists
    const adminExists = await User.findOne({ role: "admin" });

    return NextResponse.json({
      success: true,
      status: {
        database: "connected",
        adminUser: adminExists ? "exists" : "missing",
        environment: process.env.NODE_ENV || "development",
        mongoUri: process.env.MONGODB_URI ? "configured" : "missing",
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      status: {
        database: "disconnected",
        adminUser: "unknown",
        environment: process.env.NODE_ENV || "development",
        mongoUri: process.env.MONGODB_URI ? "configured" : "missing",
      },
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
