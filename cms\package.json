{"name": "portfolio-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3002", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "init-admin": "tsx src/scripts/init-admin.ts"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongoose": "^8.15.1", "next": "15.3.2", "next-cloudinary": "^6.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "slugify": "^1.6.6", "tailwind-merge": "^3.3.0", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}