"use strict";(()=>{var e={};e.id=6726,e.ids=[6726],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},99836:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>g,POST:()=>v});var o=t(96559),a=t(48088),i=t(37719),n=t(32190),p=t(75745),u=t(35325),d=t(12909),c=t(27746),l=t(67912),x=t.n(l);let g=(0,c.gx)(async e=>{try{await (0,p.A)();let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),o=r.get("status"),a=r.get("category"),i=r.get("featured"),d=r.get("search"),c={};o&&(c.status=o),a&&(c.category=a),null!==i&&(c.featured="true"===i),d&&(c.$or=[{title:{$regex:d,$options:"i"}},{description:{$regex:d,$options:"i"}},{category:{$regex:d,$options:"i"}},{tags:{$in:[RegExp(d,"i")]}}]);let l=(t-1)*s,x=await u.A.find(c).sort({order:1,createdAt:-1}).skip(l).limit(s).lean(),g=await u.A.countDocuments(c);return n.NextResponse.json({success:!0,data:x,pagination:{page:t,limit:s,total:g,pages:Math.ceil(g/s)}})}catch(e){return console.error("Get videos error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}),v=(0,d.oC)(async e=>{try{await (0,p.A)();let{id:r,title:t,description:s,category:o,tags:a=[],featured:i=!1,status:d="draft",order:c=0}=await e.json();if(!r||!t)return n.NextResponse.json({error:"Video ID and title are required"},{status:400});if(await u.A.findOne({id:r}))return n.NextResponse.json({error:"Video with this ID already exists"},{status:400});let l=x()(t,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g}),g=l,v=1;for(;await u.A.findOne({slug:g});)g=`${l}-${v}`,v++;let m=new u.A({id:r,title:t,slug:g,description:s,category:o,tags:a,featured:i,status:d,order:c});return await m.save(),n.NextResponse.json({success:!0,data:m},{status:201})}catch(e){return console.error("Create video error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}),m=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/videos/route",pathname:"/api/videos",filename:"route",bundlePath:"app/api/videos/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\videos\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:h}=m;function y(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,7428],()=>t(99836));module.exports=s})();