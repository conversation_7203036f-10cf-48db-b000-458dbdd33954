"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6823],{283:(e,t,a)=>{a.d(t,{A:()=>l,AuthProvider:()=>n});var r=a(5155),s=a(2115);let i=(0,s.createContext)(void 0);function n(e){let{children:t}=e,[a,n]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0),o=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();n(t.user)}else n(null)}catch(e){console.error("Auth check failed:",e),n(null)}finally{d(!1)}},c=async(e,t)=>{try{let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await a.json();if(a.ok)return n(r.user),{success:!0};return{success:!1,error:r.error||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error"}}},u=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{n(null)}};return(0,s.useEffect)(()=>{o()},[]),(0,r.jsx)(i.Provider,{value:{user:a,loading:l,login:c,logout:u,checkAuth:o},children:t})}function l(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},285:(e,t,a)=>{a.d(t,{$:()=>d});var r=a(5155);a(2115);var s=a(9708),i=a(2085),n=a(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:i,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:a,size:i,className:t})),...o})}},2523:(e,t,a)=>{a.d(t,{p:()=>i});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,type:a,...i}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},6695:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},6913:(e,t,a)=>{a.d(t,{A:()=>em});var r=a(5155),s=a(283),i=a(5695),n=a(2115),l=a(9708),d=a(2085),o=a(2432),c=a(9434),u=a(285);a(2523);var b=a(7489);function f(e){let{className:t,orientation:a="horizontal",decorative:s=!0,...i}=e;return(0,r.jsx)(b.b,{"data-slot":"separator-root",decorative:s,orientation:a,className:(0,c.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...i})}var h=a(5452),x=a(4416);function p(e){let{...t}=e;return(0,r.jsx)(h.bL,{"data-slot":"sheet",...t})}function g(e){let{...t}=e;return(0,r.jsx)(h.ZL,{"data-slot":"sheet-portal",...t})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(h.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function v(e){let{className:t,children:a,side:s="right",...i}=e;return(0,r.jsxs)(g,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(h.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...i,children:[a,(0,r.jsxs)(h.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(x.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function j(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,c.cn)("flex flex-col gap-1.5 p-4",t),...a})}function w(e){let{className:t,...a}=e;return(0,r.jsx)(h.hE,{"data-slot":"sheet-title",className:(0,c.cn)("text-foreground font-semibold",t),...a})}function N(e){let{className:t,...a}=e;return(0,r.jsx)(h.VY,{"data-slot":"sheet-description",className:(0,c.cn)("text-muted-foreground text-sm",t),...a})}var y=a(3921);function k(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(y.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function A(e){let{...t}=e;return(0,r.jsx)(k,{children:(0,r.jsx)(y.bL,{"data-slot":"tooltip",...t})})}function z(e){let{...t}=e;return(0,r.jsx)(y.l9,{"data-slot":"tooltip-trigger",...t})}function C(e){let{className:t,sideOffset:a=0,children:s,...i}=e;return(0,r.jsx)(y.ZL,{children:(0,r.jsxs)(y.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,c.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...i,children:[s,(0,r.jsx)(y.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let _=n.createContext(null);function P(){let e=n.useContext(_);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function S(e){let{defaultOpen:t=!0,open:a,onOpenChange:s,className:i,style:l,children:d,...o}=e,u=function(){let[e,t]=n.useState(void 0);return n.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[b,f]=n.useState(!1),[h,x]=n.useState(t),p=null!=a?a:h,g=n.useCallback(e=>{let t="function"==typeof e?e(p):e;s?s(t):x(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[s,p]),m=n.useCallback(()=>u?f(e=>!e):g(e=>!e),[u,g,f]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),m())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[m]);let v=p?"expanded":"collapsed",j=n.useMemo(()=>({state:v,open:p,setOpen:g,isMobile:u,openMobile:b,setOpenMobile:f,toggleSidebar:m}),[v,p,g,u,b,f,m]);return(0,r.jsx)(_.Provider,{value:j,children:(0,r.jsx)(k,{delayDuration:0,children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...l},className:(0,c.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",i),...o,children:d})})})}function L(e){let{side:t="left",variant:a="sidebar",collapsible:s="offcanvas",className:i,children:n,...l}=e,{isMobile:d,state:o,openMobile:u,setOpenMobile:b}=P();return"none"===s?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,c.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",i),...l,children:n}):d?(0,r.jsx)(p,{open:u,onOpenChange:b,...l,children:(0,r.jsxs)(v,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,r.jsxs)(j,{className:"sr-only",children:[(0,r.jsx)(w,{children:"Sidebar"}),(0,r.jsx)(N,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:n})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":o,"data-collapsible":"collapsed"===o?s:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,c.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,c.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",i),...l,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:n})})]})}function E(e){let{className:t,onClick:a,...s}=e,{toggleSidebar:i}=P();return(0,r.jsxs)(u.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,c.cn)("size-7",t),onClick:e=>{null==a||a(e),i()},...s,children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function T(e){let{className:t,...a}=e,{toggleSidebar:s}=P();return(0,r.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:s,title:"Toggle Sidebar",className:(0,c.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...a})}function D(e){let{className:t,...a}=e;return(0,r.jsx)("main",{"data-slot":"sidebar-inset",className:(0,c.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...a})}function O(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,c.cn)("flex flex-col gap-2 p-2",t),...a})}function R(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,c.cn)("flex flex-col gap-2 p-2",t),...a})}function V(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,c.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function U(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,c.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a})}function B(e){let{className:t,asChild:a=!1,...s}=e,i=a?l.DX:"div";return(0,r.jsx)(i,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,c.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...s})}function M(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,c.cn)("w-full text-sm",t),...a})}function K(e){let{className:t,...a}=e;return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,c.cn)("flex w-full min-w-0 flex-col gap-1",t),...a})}function W(e){let{className:t,...a}=e;return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,c.cn)("group/menu-item relative",t),...a})}let X=(0,d.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Z(e){let{asChild:t=!1,isActive:a=!1,variant:s="default",size:i="default",tooltip:n,className:d,...o}=e,u=t?l.DX:"button",{isMobile:b,state:f}=P(),h=(0,r.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":i,"data-active":a,className:(0,c.cn)(X({variant:s,size:i}),d),...o});return n?("string"==typeof n&&(n={children:n}),(0,r.jsxs)(A,{children:[(0,r.jsx)(z,{asChild:!0,children:h}),(0,r.jsx)(C,{side:"right",align:"center",hidden:"collapsed"!==f||b,...n})]})):h}function $(e){let{className:t,...a}=e;return(0,r.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,c.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...a})}function q(e){let{className:t,...a}=e;return(0,r.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,c.cn)("group/menu-sub-item relative",t),...a})}function F(e){let{asChild:t=!1,size:a="md",isActive:s=!1,className:i,...n}=e,d=t?l.DX:"a";return(0,r.jsx)(d,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":a,"data-active":s,className:(0,c.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===a&&"text-xs","md"===a&&"text-sm","group-data-[collapsible=icon]:hidden",i),...n})}var J=a(6874),H=a.n(J),I=a(4011);function Q(e){let{className:t,...a}=e;return(0,r.jsx)(I.bL,{"data-slot":"avatar",className:(0,c.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function Y(e){let{className:t,...a}=e;return(0,r.jsx)(I.H4,{"data-slot":"avatar-fallback",className:(0,c.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}var G=a(3783),ee=a(7434),et=a(2657),ea=a(4616),er=a(9803),es=a(5690),ei=a(1497),en=a(8564),el=a(7580),ed=a(8136),eo=a(4738),ec=a(3052),eu=a(4835),eb=a(8106);function ef(e){let{...t}=e;return(0,r.jsx)(eb.bL,{"data-slot":"collapsible",...t})}function eh(e){let{...t}=e;return(0,r.jsx)(eb.R6,{"data-slot":"collapsible-trigger",...t})}function ex(e){let{...t}=e;return(0,r.jsx)(eb.Ke,{"data-slot":"collapsible-content",...t})}let ep=[{title:"Overview",items:[{title:"Dashboard",url:"/dashboard",icon:G.A}]},{title:"Content Management",items:[{title:"Blog Posts",url:"/dashboard/blog",icon:ee.A,submenu:[{title:"All Posts",url:"/dashboard/blog",icon:et.A},{title:"New Post",url:"/dashboard/blog/new",icon:ea.A}]},{title:"Videos",url:"/dashboard/videos",icon:er.A,submenu:[{title:"All Videos",url:"/dashboard/videos",icon:et.A},{title:"New Video",url:"/dashboard/videos/new",icon:ea.A}]},{title:"Reels",url:"/dashboard/reels",icon:es.A,submenu:[{title:"All Reels",url:"/dashboard/reels",icon:et.A},{title:"New Reel",url:"/dashboard/reels/new",icon:ea.A}]},{title:"Testimonials",url:"/dashboard/testimonials",icon:ei.A,submenu:[{title:"All Testimonials",url:"/dashboard/testimonials",icon:en.A},{title:"New Testimonial",url:"/dashboard/testimonials/new",icon:ea.A}]},{title:"Clients",url:"/dashboard/clients",icon:el.A,submenu:[{title:"All Clients",url:"/dashboard/clients",icon:ed.A},{title:"New Client",url:"/dashboard/clients/new",icon:ea.A}]}]}];function eg(){var e;let{user:t,logout:a}=(0,s.A)(),n=(0,i.useRouter)(),l=(0,i.usePathname)(),d=async()=>{await a(),n.push("/login")},o=e=>"/dashboard"===e?"/dashboard"===l:l.startsWith(e);return(0,r.jsxs)(L,{variant:"inset",children:[(0,r.jsx)(O,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2",children:[(0,r.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground",children:(0,r.jsx)(eo.A,{className:"size-4"})}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:"Portfolio CMS"}),(0,r.jsx)("span",{className:"truncate text-xs text-muted-foreground",children:"Content Management"})]})]})}),(0,r.jsx)(V,{children:ep.map(e=>(0,r.jsxs)(U,{children:[(0,r.jsx)(B,{children:e.title}),(0,r.jsx)(M,{children:(0,r.jsx)(K,{children:e.items.map(e=>(0,r.jsx)(W,{children:e.submenu?(0,r.jsx)(ef,{asChild:!0,defaultOpen:o(e.url),className:"group/collapsible",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(eh,{asChild:!0,children:(0,r.jsxs)(Z,{tooltip:e.title,isActive:o(e.url),children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title}),(0,r.jsx)(ec.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,r.jsx)(ex,{children:(0,r.jsx)($,{children:e.submenu.map(e=>(0,r.jsx)(q,{children:(0,r.jsx)(F,{asChild:!0,isActive:l===e.url,children:(0,r.jsxs)(H(),{href:e.url,children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})]})}):(0,r.jsx)(Z,{asChild:!0,tooltip:e.title,isActive:o(e.url),children:(0,r.jsxs)(H(),{href:e.url,children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})]},e.title))}),(0,r.jsx)(R,{children:(0,r.jsxs)(K,{children:[(0,r.jsx)(W,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsx)(Q,{className:"h-8 w-8 rounded-lg",children:(0,r.jsx)(Y,{className:"rounded-lg",children:(null==t||null==(e=t.name)?void 0:e.charAt(0).toUpperCase())||"U"})}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:(null==t?void 0:t.name)||"User"}),(0,r.jsx)("span",{className:"truncate text-xs text-muted-foreground capitalize",children:(null==t?void 0:t.role)||"user"})]})]})}),(0,r.jsx)(W,{children:(0,r.jsxs)(Z,{onClick:d,className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,r.jsx)(eu.A,{}),(0,r.jsx)("span",{children:"Logout"})]})})]})}),(0,r.jsx)(T,{})]})}function em(e){let{children:t}=e,{user:a,loading:l}=(0,s.A)(),d=(0,i.useRouter)(),o=(0,i.usePathname)();return((0,n.useEffect)(()=>{l||a||d.push("/login")},[a,l,d]),l)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):a?(0,r.jsxs)(S,{children:[(0,r.jsx)(eg,{}),(0,r.jsxs)(D,{children:[(0,r.jsx)("header",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,r.jsx)(E,{className:"-ml-1"}),(0,r.jsx)(f,{orientation:"vertical",className:"mr-2 h-4"}),(0,r.jsx)("h1",{className:"text-lg font-semibold",children:(()=>{let e=o.split("/").filter(Boolean);if(1===e.length)return"Dashboard";let t=e[e.length-1];return"new"===t?"New":"blog"===t?"Blog Posts":"videos"===t?"Videos":"reels"===t?"Reels":"testimonials"===t?"Testimonials":"clients"===t?"Clients":t.charAt(0).toUpperCase()+t.slice(1)})()})]})}),(0,r.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:t})]})]}):null}},9434:(e,t,a)=>{a.d(t,{cn:()=>i});var r=a(2596),s=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}}]);