(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11649:(e,s,t)=>{Promise.resolve().then(t.bind(t,80559))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24801:(e,s,t)=>{Promise.resolve().then(t.bind(t,58061))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},58061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(60687),a=t(43210),l=t(72455),i=t(44493),o=t(29523),n=t(10022),d=t(2943),c=t(97840),x=t(58887),h=t(41312),m=t(64398),u=t(85814),p=t.n(u);function g(){let[e,s]=(0,a.useState)({totalPosts:0,publishedPosts:0,draftPosts:0,totalVideos:0,totalReels:0,totalTestimonials:0,totalClients:0,featuredContent:0}),[t,u]=(0,a.useState)(!0),g=[{title:"Blog Posts",value:e.totalPosts,icon:n.A,description:"Total articles",color:"text-blue-600",bgColor:"bg-blue-50"},{title:"Videos",value:e.totalVideos,icon:d.A,description:"YouTube videos",color:"text-red-600",bgColor:"bg-red-50"},{title:"Reels",value:e.totalReels,icon:c.A,description:"Social media reels",color:"text-purple-600",bgColor:"bg-purple-50"},{title:"Testimonials",value:e.totalTestimonials,icon:x.A,description:"Client feedback",color:"text-green-600",bgColor:"bg-green-50"},{title:"Clients",value:e.totalClients,icon:h.A,description:"Business clients",color:"text-orange-600",bgColor:"bg-orange-50"},{title:"Featured Content",value:e.featuredContent,icon:m.A,description:"Highlighted items",color:"text-yellow-600",bgColor:"bg-yellow-50"}];return(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Content Dashboard"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"Manage your portfolio content easily"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map((e,s)=>(0,r.jsxs)(i.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,r.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(i.ZB,{className:"text-sm font-medium text-gray-600",children:e.title}),(0,r.jsx)("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:(0,r.jsx)(e.icon,{className:`h-4 w-4 ${e.color}`})})]}),(0,r.jsxs)(i.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:t?(0,r.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-16 rounded"}):e.value.toLocaleString()}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]})]},s))}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{className:"text-xl font-semibold",children:"Create New Content"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add new content to your portfolio"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,r.jsx)(p(),{href:"/dashboard/blog/new",children:(0,r.jsxs)(o.$,{variant:"outline",className:"h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-blue-50 hover:border-blue-300",children:[(0,r.jsx)(n.A,{className:"h-6 w-6 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Blog Post"})]})}),(0,r.jsx)(p(),{href:"/dashboard/videos/new",children:(0,r.jsxs)(o.$,{variant:"outline",className:"h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-red-50 hover:border-red-300",children:[(0,r.jsx)(d.A,{className:"h-6 w-6 text-red-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Video"})]})}),(0,r.jsx)(p(),{href:"/dashboard/reels/new",children:(0,r.jsxs)(o.$,{variant:"outline",className:"h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-purple-50 hover:border-purple-300",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 text-purple-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Reel"})]})}),(0,r.jsx)(p(),{href:"/dashboard/testimonials/new",children:(0,r.jsxs)(o.$,{variant:"outline",className:"h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-green-50 hover:border-green-300",children:[(0,r.jsx)(x.A,{className:"h-6 w-6 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Testimonial"})]})}),(0,r.jsx)(p(),{href:"/dashboard/clients/new",children:(0,r.jsxs)(o.$,{variant:"outline",className:"h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-orange-50 hover:border-orange-300",children:[(0,r.jsx)(h.A,{className:"h-6 w-6 text-orange-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Client"})]})})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{className:"text-xl font-semibold",children:"Manage Content"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"View and edit your existing content"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsx)(p(),{href:"/dashboard/blog",children:(0,r.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start h-12 hover:bg-blue-50",children:[(0,r.jsx)(n.A,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Blog Posts"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.totalPosts," articles"]})]})]})}),(0,r.jsx)(p(),{href:"/dashboard/videos",children:(0,r.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start h-12 hover:bg-red-50",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-red-600 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Videos"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.totalVideos," videos"]})]})]})}),(0,r.jsx)(p(),{href:"/dashboard/reels",children:(0,r.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start h-12 hover:bg-purple-50",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-purple-600 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Reels"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.totalReels," reels"]})]})]})})]})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{className:"text-xl font-semibold",children:"Business Content"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Manage testimonials and clients"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsx)(p(),{href:"/dashboard/testimonials",children:(0,r.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start h-12 hover:bg-green-50",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Testimonials"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.totalTestimonials," reviews"]})]})]})}),(0,r.jsx)(p(),{href:"/dashboard/clients",children:(0,r.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start h-12 hover:bg-orange-50",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-orange-600 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Clients"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.totalClients," clients"]})]})]})}),(0,r.jsx)("div",{className:"pt-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-yellow-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-yellow-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:"Featured Content"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Highlighted across portfolio"})]})]}),(0,r.jsx)("div",{className:"text-lg font-bold text-yellow-600",children:e.featuredContent})]})})]})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\page.tsx","default")},83797:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,1771,1658,9365,4758,9638],()=>t(83797));module.exports=r})();