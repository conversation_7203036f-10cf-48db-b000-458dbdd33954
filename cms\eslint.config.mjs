import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  {
    // Apply rules to all JS/TS files
    files: ["**/*.{js,ts,jsx,tsx}"],
    rules: {
      "@typescript-eslint/no-unused-vars": "off", // disable unused vars error
      "@typescript-eslint/no-explicit-any": "off", // allow use of 'any'
      "react-hooks/exhaustive-deps": "warn", // optionally relax to warn
      "no-console": "warn", // just a warning, not error
    },
  },
];

export default eslintConfig;
