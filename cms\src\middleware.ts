import { NextRequest, NextResponse } from 'next/server';
import { getAllowedOrigins, isOriginAllowed, createCorsResponse } from '@/lib/cors';

export function middleware(request: NextRequest) {
  // Handle CORS for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const origin = request.headers.get('origin');
    const allowedOrigins = getAllowedOrigins();
    const originAllowed = isOriginAllowed(origin || '', allowedOrigins);

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return createCorsResponse(origin);
    }

    // Add CORS headers to all API responses
    const response = NextResponse.next();

    // In development or if no specific origin is configured, be more permissive
    const isDevelopment = process.env.NODE_ENV === 'development';
    const hasProductionConfig = process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_FRONTEND_URL;

    let allowOrigin: string;
    if (originAllowed) {
      allowOrigin = origin || '*';
    } else if (isDevelopment || !hasProductionConfig) {
      allowOrigin = '*';
    } else {
      allowOrigin = 'null';
    }

    response.headers.set('Access-Control-Allow-Origin', allowOrigin);
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    response.headers.set('Access-Control-Allow-Credentials', (originAllowed || allowOrigin === '*') ? 'true' : 'false');

    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: '/api/:path*',
};
