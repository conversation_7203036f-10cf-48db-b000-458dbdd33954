#!/usr/bin/env node

/**
 * CORS Testing Script
 * Tests CORS configuration for the portfolio CMS
 */

const https = require('https');
const http = require('http');

const CMS_BASE_URL = 'https://uttam-backend.vercel.app';
const PORTFOLIO_ORIGIN = 'https://uttamrimal.com.np';

const endpoints = [
  '/api/public/blog',
  '/api/videos',
  '/api/testimonials',
  '/api/clients',
  '/api/reels'
];

function makeRequest(url, origin) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'OPTIONS',
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    };

    const req = (urlObj.protocol === 'https:' ? https : http).request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.end();
  });
}

async function testCORS() {
  console.log('🧪 Testing CORS Configuration');
  console.log('================================');
  console.log(`CMS URL: ${CMS_BASE_URL}`);
  console.log(`Origin: ${PORTFOLIO_ORIGIN}`);
  console.log('');

  for (const endpoint of endpoints) {
    const url = `${CMS_BASE_URL}${endpoint}`;
    console.log(`Testing: ${endpoint}`);
    
    try {
      const response = await makeRequest(url, PORTFOLIO_ORIGIN);
      
      const corsHeaders = {
        'access-control-allow-origin': response.headers['access-control-allow-origin'],
        'access-control-allow-methods': response.headers['access-control-allow-methods'],
        'access-control-allow-headers': response.headers['access-control-allow-headers'],
        'access-control-allow-credentials': response.headers['access-control-allow-credentials']
      };

      console.log(`  Status: ${response.status}`);
      console.log(`  CORS Headers:`);
      Object.entries(corsHeaders).forEach(([key, value]) => {
        if (value) {
          console.log(`    ${key}: ${value}`);
        }
      });

      // Check if CORS is properly configured
      const allowedOrigin = corsHeaders['access-control-allow-origin'];
      const isOriginAllowed = allowedOrigin === PORTFOLIO_ORIGIN || allowedOrigin === '*';
      
      if (response.status === 200 && isOriginAllowed) {
        console.log(`  ✅ CORS OK`);
      } else {
        console.log(`  ❌ CORS Issue - Status: ${response.status}, Origin: ${allowedOrigin}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('🔍 Testing with actual GET request...');
  
  // Test actual GET request
  try {
    const testUrl = `${CMS_BASE_URL}/api/public/blog`;
    const response = await new Promise((resolve, reject) => {
      const urlObj = new URL(testUrl);
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname,
        method: 'GET',
        headers: {
          'Origin': PORTFOLIO_ORIGIN,
          'Content-Type': 'application/json'
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        });
      });

      req.on('error', reject);
      req.end();
    });

    console.log(`GET ${testUrl}`);
    console.log(`Status: ${response.status}`);
    console.log(`CORS Origin: ${response.headers['access-control-allow-origin']}`);
    
    if (response.status === 200) {
      const data = JSON.parse(response.data);
      console.log(`✅ Success - Retrieved ${data.data?.length || 0} items`);
    } else {
      console.log(`❌ Failed - ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ GET Request Error: ${error.message}`);
  }
}

// Run the test
testCORS().catch(console.error);
