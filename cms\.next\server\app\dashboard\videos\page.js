(()=>{var e={};e.id=2618,e.ids=[2618],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>i,BF:()=>d,Hj:()=>o,XI:()=>n,nA:()=>c,nd:()=>l});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function d({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function l({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10292:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),d=s(72455),o=s(29523),l=s(89667),c=s(96834),u=s(44493),x=s(6211),h=s(21342),m=s(96474),p=s(2943),v=s(13861),f=s(64398),j=s(99270),b=s(93661),g=s(63143),w=s(88233),y=s(30474);function N(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[N,A]=(0,a.useState)(""),[_,C]=(0,a.useState)("all"),k=async s=>{if(confirm("Are you sure you want to delete this video?"))try{(await fetch(`/api/videos/${s}`,{method:"DELETE",credentials:"include"})).ok&&t(e.filter(e=>e._id!==s))}catch(e){console.error("Error deleting video:",e)}},P=e.filter(e=>{let t=e.title.toLowerCase().includes(N.toLowerCase())||e.description&&e.description.toLowerCase().includes(N.toLowerCase())||e.category&&e.category.toLowerCase().includes(N.toLowerCase()),s="all"===_||e.status===_;return t&&s}),D=e=>(0,r.jsx)(c.E,{variant:{draft:"secondary",published:"default",archived:"outline"}[e],children:e.charAt(0).toUpperCase()+e.slice(1)}),E=e=>"/images/placeholder.svg",S=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,r.jsx)(d.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Videos"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your video portfolio"})]}),(0,r.jsx)(i(),{href:"/dashboard/videos/new",children:(0,r.jsxs)(o.$,{children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"New Video"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Total Videos"}),(0,r.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.length})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Published"}),(0,r.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"published"===e.status).length})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Featured"}),(0,r.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>e.featured).length})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[(0,r.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(l.p,{placeholder:"Search videos...",value:N,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("select",{value:_,onChange:e=>C(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"published",children:"Published"}),(0,r.jsx)("option",{value:"draft",children:"Draft"}),(0,r.jsx)("option",{value:"archived",children:"Archived"})]})]}),(0,r.jsx)(u.Zp,{children:(0,r.jsxs)(x.XI,{children:[(0,r.jsx)(x.A0,{children:(0,r.jsxs)(x.Hj,{children:[(0,r.jsx)(x.nd,{children:"Video"}),(0,r.jsx)(x.nd,{children:"Category"}),(0,r.jsx)(x.nd,{children:"Status"}),(0,r.jsx)(x.nd,{children:"Featured"}),(0,r.jsx)(x.nd,{children:"Date"}),(0,r.jsx)(x.nd,{className:"w-[70px]",children:"Actions"})]})}),(0,r.jsx)(x.BF,{children:s?(0,r.jsx)(x.Hj,{children:(0,r.jsx)(x.nA,{colSpan:6,className:"text-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"})})}):0===P.length?(0,r.jsx)(x.Hj,{children:(0,r.jsx)(x.nA,{colSpan:6,className:"text-center py-8 text-gray-500",children:"No videos found"})}):P.map(e=>(0,r.jsxs)(x.Hj,{children:[(0,r.jsx)(x.nA,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(y.default,{src:E(e),alt:e.title,width:80,height:45,className:"w-20 h-11 object-cover rounded"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.title}),(0,r.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description||"No description"}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]})]})}),(0,r.jsx)(x.nA,{children:e.category?(0,r.jsx)(c.E,{variant:"outline",children:e.category}):"-"}),(0,r.jsx)(x.nA,{children:D(e.status)}),(0,r.jsx)(x.nA,{children:e.featured&&(0,r.jsx)(f.A,{className:"h-4 w-4 text-yellow-500 fill-current"})}),(0,r.jsx)(x.nA,{className:"text-sm text-gray-500",children:S(e.createdAt)}),(0,r.jsx)(x.nA,{children:(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsx)(o.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(h.SQ,{align:"end",children:[(0,r.jsx)(h._2,{asChild:!0,children:(0,r.jsxs)(i(),{href:`/dashboard/videos/${e._id}`,children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,r.jsxs)(h._2,{onClick:()=>k(e._id),className:"text-red-600",children:[(0,r.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e._id))})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21342:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>o,_2:()=>l,rI:()=>i,ty:()=>d});var r=s(60687);s(43210);var a=s(92930),n=s(4780);function i({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dropdown-menu",...e})}function d({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...e})}function o({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function l({className:e,inset:t,variant:s="default",...i}){return(0,r.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31587:(e,t,s)=>{Promise.resolve().then(s.bind(s,40290))},33873:e=>{"use strict";e.exports=require("path")},40290:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\videos\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\videos\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},91457:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l={children:["",{children:["dashboard",{children:["videos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,40290)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\videos\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\videos\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/videos/page",pathname:"/dashboard/videos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...n}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:t}),e),...n})}},97243:(e,t,s)=>{Promise.resolve().then(s.bind(s,10292))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,1771,1658,9365,4758,474,4883,9638],()=>s(91457));module.exports=r})();