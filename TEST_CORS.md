# CORS Testing Guide

## Quick Test Commands

### 1. Test CORS with curl

```bash
# Test OPTIONS preflight request
curl -i -X OPTIONS \
  -H "Origin: https://uttamrimal.com.np" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type" \
  https://uttam-backend.vercel.app/api/public/blog

# Test actual GET request
curl -i -X GET \
  -H "Origin: https://uttamrimal.com.np" \
  -H "Content-Type: application/json" \
  https://uttam-backend.vercel.app/api/public/blog

# Test CORS test endpoint
curl -i -X GET \
  -H "Origin: https://uttamrimal.com.np" \
  https://uttam-backend.vercel.app/api/cors-test
```

### 2. Test with Node.js script

```bash
node test-cors.js
```

### 3. Browser Console Test

Open your portfolio website and run in browser console:

```javascript
// Test fetch from portfolio to CMS
fetch('https://uttam-backend.vercel.app/api/public/blog', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('Status:', response.status);
  console.log('CORS Headers:', {
    'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
    'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
  });
  return response.json();
})
.then(data => console.log('Data:', data))
.catch(error => console.error('Error:', error));
```

## Expected Results

### Successful CORS Response Headers:
```
Access-Control-Allow-Origin: https://uttamrimal.com.np
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With
Access-Control-Allow-Credentials: true
```

### CORS Test Endpoint Response:
```json
{
  "success": true,
  "cors": {
    "requestOrigin": "https://uttamrimal.com.np",
    "allowedOrigins": [
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:3002",
      "http://localhost:3003",
      "https://uttamrimal.com.np",
      "https://www.uttamrimal.com.np",
      "https://uttam-portfolio.vercel.app",
      "https://uttam-portfolio-git-main.vercel.app"
    ],
    "originAllowed": true,
    "environment": "production",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "message": "CORS is properly configured for this origin"
}
```

## Troubleshooting

### Common CORS Error Messages:

1. **"Access to fetch at '...' from origin '...' has been blocked by CORS policy"**
   - Check if your domain is in the allowed origins list
   - Verify environment variables are set correctly

2. **"Response to preflight request doesn't pass access control check"**
   - OPTIONS method is not properly handled
   - Check middleware configuration

3. **"The request client is not a secure context"**
   - Mixed content issue (HTTP vs HTTPS)
   - Ensure both sites use HTTPS in production

### Debug Steps:

1. **Check Network Tab in Browser DevTools**
   - Look for preflight OPTIONS requests
   - Check response headers for CORS headers

2. **Check CMS Logs**
   - Look for CORS-related console logs
   - Verify environment variables are loaded

3. **Test Different Origins**
   ```bash
   # Test localhost
   curl -H "Origin: http://localhost:3000" https://uttam-backend.vercel.app/api/cors-test
   
   # Test production domain
   curl -H "Origin: https://uttamrimal.com.np" https://uttam-backend.vercel.app/api/cors-test
   
   # Test www subdomain
   curl -H "Origin: https://www.uttamrimal.com.np" https://uttam-backend.vercel.app/api/cors-test
   ```

## Environment Variables Checklist

### CMS Environment Variables:
- [ ] `FRONTEND_URL` set to portfolio domain
- [ ] `NODE_ENV` set to "production"
- [ ] `ADDITIONAL_CORS_ORIGINS` (if needed)

### Portfolio Environment Variables:
- [ ] `NEXT_PUBLIC_CMS_URL` set to CMS domain

## API Endpoints to Test:

- [ ] `/api/public/blog` - Public blog posts
- [ ] `/api/public/blog/[slug]` - Individual blog post
- [ ] `/api/videos` - Video content
- [ ] `/api/testimonials` - Testimonials
- [ ] `/api/clients` - Client information
- [ ] `/api/reels` - Social media reels
- [ ] `/api/cors-test` - CORS configuration test

## Success Criteria:

- [ ] No CORS errors in browser console
- [ ] All API calls from portfolio to CMS work
- [ ] Preflight OPTIONS requests return 200
- [ ] Proper CORS headers in all responses
- [ ] Client components load data successfully
- [ ] No mixed content warnings
