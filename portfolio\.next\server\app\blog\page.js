(()=>{var e={};e.id=831,e.ids=[831],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11109:(e,t,r)=>{Promise.resolve().then(r.bind(r,56888)),Promise.resolve().then(r.bind(r,92830)),Promise.resolve().then(r.bind(r,94743)),Promise.resolve().then(r.bind(r,31646))},11639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),a=r(52343),o=r(68926),i=r(84712),n=r(33484),l=r(36814);let d={title:"Blog - Insights & Tips",description:"Video editing tutorials, tips, and insights from professional video editor <PERSON><PERSON><PERSON> Rimal.",openGraph:{title:"Blog - Uttam Rimal",description:"Video editing tutorials, tips, and insights from professional video editor Uttam Rimal."}};async function c(){let[e,t]=await Promise.all([(0,a.g6)(),(0,a.Sk)()]);return(0,s.jsxs)("main",{children:[(0,s.jsx)(o.default,{}),(0,s.jsx)(l.default,{allPosts:e,featuredPosts:t}),(0,s.jsx)(i.default,{}),(0,s.jsx)(n.default,{})]})}},19093:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(60687),a=r(85814),o=r.n(a),i=r(30474),n=r(40228),l=r(48730),d=r(58869),c=r(96834),u=r(62185);function p({post:e,variant:t="default",showExcerpt:r=!0,showAuthor:a=!0,className:p=""}){return(0,s.jsx)("article",{className:`group ${{default:"bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105",featured:"bg-card rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 border-2 border-accent",compact:"bg-card rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300"}[t]} ${p}`,children:(0,s.jsxs)(o(),{href:`/blog/${e.slug}`,children:[(0,s.jsxs)("div",{className:`relative ${{default:"aspect-video",featured:"aspect-video",compact:"aspect-[4/3]"}[t]} overflow-hidden`,children:[e.thumbnail?(0,s.jsx)(i.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):(0,s.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-primary-foreground text-6xl font-heading font-bold opacity-20",children:e.title.charAt(0)})}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,s.jsx)("div",{className:"absolute top-4 left-4",children:(0,s.jsx)(c.E,{variant:"secondary",className:"bg-secondary/90 text-secondary-foreground backdrop-blur-sm",children:e.category})}),e.featured&&(0,s.jsx)("div",{className:"absolute top-4 right-4",children:(0,s.jsx)(c.E,{variant:"default",className:"bg-accent text-accent-foreground backdrop-blur-sm",children:"Featured"})})]}),(0,s.jsxs)("div",{className:{default:"p-6",featured:"p-8",compact:"p-4"}[t],children:[(0,s.jsx)("h3",{className:`${{default:"text-xl font-heading font-semibold text-primary mb-3",featured:"text-2xl font-heading font-bold text-primary mb-4",compact:"text-lg font-heading font-semibold text-primary mb-2"}[t]} group-hover:text-secondary transition-colors duration-300 line-clamp-2`,children:e.title}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(n.A,{size:14}),(0,s.jsx)("span",{children:(0,u.Yq)(e.createdAt)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(l.A,{size:14}),(0,s.jsxs)("span",{children:[e.readTime," min read"]})]}),a&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(d.A,{size:14}),(0,s.jsx)("span",{children:"Uttam Rimal"})]})]}),r&&e.excerpt&&(0,s.jsx)("p",{className:`text-muted-foreground leading-relaxed mb-4 ${"compact"===t?"text-sm line-clamp-2":"line-clamp-3"}`,children:e.excerpt}),e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,"compact"===t?2:3).map(e=>(0,s.jsxs)(c.E,{variant:"outline",className:"text-xs hover:bg-primary hover:text-primary-foreground transition-colors",children:["#",e]},e)),e.tags.length>("compact"===t?2:3)&&(0,s.jsxs)(c.E,{variant:"outline",className:"text-xs",children:["+",e.tags.length-("compact"===t?2:3)]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-primary font-semibold text-sm group-hover:text-secondary transition-colors duration-300",children:"Read More →"}),"featured"===t&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full ${"published"===e.status?"bg-success":"bg-warning"}`}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.status})]})]})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31646:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(43210),o=r(2975),i=r(29523);function n(){let[e,t]=(0,a.useState)(!1);return e?(0,s.jsx)(i.$,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300","aria-label":"Scroll to top",children:(0,s.jsx)(o.A,{size:20})}):null}},33484:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\components\\\\layout\\\\ScrollToTop.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},36814:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\app\\\\blog\\\\BlogPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\BlogPageClient.tsx","default")},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40868:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11639)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52343:(e,t,r)=>{"use strict";r.d(t,{GB:()=>u,HI:()=>f,Lf:()=>p,OA:()=>c,Sk:()=>i,fY:()=>l,g6:()=>o,rT:()=>d,t1:()=>n});let s="https://uttam-backend.vercel.app";async function a(e){try{let t=await fetch(`${s}/api${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return console.error(`Failed to fetch ${e}:`,t.status,t.statusText),[];let r=await t.json();if(!r.success)return console.error(`API error for ${e}:`,r.message),[];return r.data||[]}catch(t){return console.error(`Network error fetching ${e}:`,t),[]}}async function o(){return(await a("/blog")).filter(e=>"published"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}async function i(){return(await o()).filter(e=>e.featured)}async function n(e){try{let t=await fetch(`${s}/api/blog/slug/${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return null;let r=await t.json();return r.success?r.data:null}catch(e){return console.error("Error fetching blog post by slug:",e),null}}async function l(){return(await a("/videos")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function d(){return(await l()).filter(e=>e.featured)}async function c(){return(await a("/reels")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function u(){return(await c()).filter(e=>e.featured)}async function p(){return(await a("/clients")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function f(){return(await p()).filter(e=>e.featured)}},56888:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(60687),a=r(85814),o=r.n(a),i=r(28559),n=r(19093);function l({allPosts:e,featuredPosts:t}){return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("div",{className:"bg-muted text-foreground py-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)(o(),{href:"/#blog",className:"inline-flex items-center gap-2 text-accent hover:text-foreground transition-colors mb-8",children:[(0,s.jsx)(i.A,{size:20}),"Back to Portfolio"]}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-heading font-bold mb-4",children:"Blog & Insights"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl",children:"Sharing knowledge on video editing, storytelling, and creative techniques."})]})}),(0,s.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[t.length>0&&(0,s.jsxs)("section",{className:"mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-heading font-bold text-primary mb-8",children:"Featured Posts"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:t.map(e=>(0,s.jsx)(n.A,{post:e,variant:"featured",showExcerpt:!0,showAuthor:!0},e._id))})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-heading font-bold text-primary mb-8",children:t.length>0?"All Posts":"Latest Posts"}),0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-lg text-muted-foreground",children:"No blog posts available at the moment."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,s.jsx)(n.A,{post:e,variant:"default",showExcerpt:!0,showAuthor:!1},e._id))})]})]})]})}},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62185:(e,t,r)=>{"use strict";r.d(t,{GB:()=>l,HI:()=>p,I9:()=>f,Yq:()=>m,dS:()=>c,g6:()=>a,lA:()=>h,rT:()=>i});async function s(e){try{let t=await fetch(`https://uttam-backend.vercel.app/api${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return console.error(`Failed to fetch ${e}:`,t.status,t.statusText),[];let r=await t.json();if(!r.success)return console.error(`API error for ${e}:`,r.message),[];return r.data||[]}catch(t){return console.error(`Network error fetching ${e}:`,t),[]}}async function a(){return(await s("/blog")).filter(e=>"published"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}async function o(){return(await s("/videos")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function i(){return(await o()).filter(e=>e.featured)}async function n(){return(await s("/reels")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function l(){return(await n()).filter(e=>e.featured)}async function d(){let e=await s("/testimonials");if(0===e.length){let{testimonials:e}=await r.e(304).then(r.bind(r,15304));return e.map((e,t)=>({...e,_id:e.id.toString(),slug:e.name.toLowerCase().replace(/\s+/g,"-"),rating:e.rating||5,status:"published",order:t+1,featured:t<3,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}))}return e.filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function c(){return(await d()).filter(e=>e.featured)}async function u(){return(await s("/clients")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function p(){return(await u()).filter(e=>e.featured)}function f(e){return`https://img.youtube.com/vi/${e}/maxresdefault.jpg`}function h(e){return`https://img.youtube.com/vi/${e}/maxresdefault.jpg`}function m(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74661:(e,t,r)=>{Promise.resolve().then(r.bind(r,36814)),Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926)),Promise.resolve().then(r.bind(r,33484))},79551:e=>{"use strict";e.exports=require("url")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),o=r(24224),i=r(4780);let n=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",success:"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40",warning:"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40",info:"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...o}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(n({variant:t}),e),...o})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,176,474,849],()=>r(40868));module.exports=s})();