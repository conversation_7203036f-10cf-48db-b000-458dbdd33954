(()=>{var e={};e.id=831,e.ids=[831],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11109:(e,t,r)=>{Promise.resolve().then(r.bind(r,56888)),Promise.resolve().then(r.bind(r,92830)),Promise.resolve().then(r.bind(r,94743)),Promise.resolve().then(r.bind(r,31646))},11639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),o=r(52343),i=r(68926),n=r(84712),a=r(33484),l=r(36814);let d={title:"Blog - Insights & Tips",description:"Video editing tutorials, tips, and insights from professional video editor <PERSON><PERSON><PERSON> Rimal.",openGraph:{title:"Blog - Uttam Rimal",description:"Video editing tutorials, tips, and insights from professional video editor Uttam Rimal."}};async function c(){let[e,t]=await Promise.all([(0,o.g6)(),(0,o.Sk)()]);return(0,s.jsxs)("main",{children:[(0,s.jsx)(i.default,{}),(0,s.jsx)(l.default,{allPosts:e,featuredPosts:t}),(0,s.jsx)(n.default,{}),(0,s.jsx)(a.default,{})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36814:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\app\\\\blog\\\\BlogPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\BlogPageClient.tsx","default")},40868:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),o=r(48088),i=r(88170),n=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11639)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52343:(e,t,r)=>{"use strict";r.d(t,{GB:()=>u,HI:()=>h,Lf:()=>p,OA:()=>c,Sk:()=>n,fY:()=>l,g6:()=>i,rT:()=>d,t1:()=>a});let s="https://uttam-backend.vercel.app";async function o(e){try{let t=await fetch(`${s}/api${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return console.error(`Failed to fetch ${e}:`,t.status,t.statusText),[];let r=await t.json();if(!r.success)return console.error(`API error for ${e}:`,r.message),[];return r.data||[]}catch(t){return console.error(`Network error fetching ${e}:`,t),[]}}async function i(){return(await o("/blog")).filter(e=>"published"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}async function n(){return(await i()).filter(e=>e.featured)}async function a(e){try{let t=await fetch(`${s}/api/blog/slug/${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return null;let r=await t.json();return r.success?r.data:null}catch(e){return console.error("Error fetching blog post by slug:",e),null}}async function l(){return(await o("/videos")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function d(){return(await l()).filter(e=>e.featured)}async function c(){return(await o("/reels")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function u(){return(await c()).filter(e=>e.featured)}async function p(){return(await o("/clients")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function h(){return(await p()).filter(e=>e.featured)}},56888:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(60687),o=r(85814),i=r.n(o),n=r(28559),a=r(19093);function l({allPosts:e,featuredPosts:t}){return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("div",{className:"bg-muted text-foreground py-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)(i(),{href:"/#blog",className:"inline-flex items-center gap-2 text-accent hover:text-foreground transition-colors mb-8",children:[(0,s.jsx)(n.A,{size:20}),"Back to Portfolio"]}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-heading font-bold mb-4",children:"Blog & Insights"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl",children:"Sharing knowledge on video editing, storytelling, and creative techniques."})]})}),(0,s.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[t.length>0&&(0,s.jsxs)("section",{className:"mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-heading font-bold text-primary mb-8",children:"Featured Posts"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:t.map(e=>(0,s.jsx)(a.A,{post:e,variant:"featured",showExcerpt:!0,showAuthor:!0},e._id))})]}),(0,s.jsxs)("section",{children:[(0,s.jsx)("h2",{className:"text-3xl font-heading font-bold text-primary mb-8",children:t.length>0?"All Posts":"Latest Posts"}),0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-lg text-muted-foreground",children:"No blog posts available at the moment."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,s.jsx)(a.A,{post:e,variant:"default",showExcerpt:!0,showAuthor:!1},e._id))})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74661:(e,t,r)=>{Promise.resolve().then(r.bind(r,36814)),Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926)),Promise.resolve().then(r.bind(r,33484))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,176,474,849,789],()=>r(40868));module.exports=s})();