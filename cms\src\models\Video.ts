import mongoose, { Document, Schema } from 'mongoose';

export interface IVideo extends Document {
  id: string;
  title: string;
  slug: string;
  description?: string;
  category?: string;
  tags: string[];
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const VideoSchema = new Schema<IVideo>({
  id: {
    type: String,
    required: true,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  description: {
    type: String,
    maxlength: 1000,
  },
  category: {
    type: String,
    trim: true,
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  featured: {
    type: Boolean,
    default: false,
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  },
  order: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Indexes for better performance
VideoSchema.index({ slug: 1 });
VideoSchema.index({ status: 1, order: 1 });
VideoSchema.index({ category: 1 });
VideoSchema.index({ featured: 1 });

// Pre-save middleware to auto-generate slug
VideoSchema.pre('save', function(next) {
  if (!this.slug && this.title) {
    const slugify = require('slugify');
    this.slug = slugify(this.title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });
  }
  next();
});

export default mongoose.models.Video || mongoose.model<IVideo>('Video', VideoSchema);
