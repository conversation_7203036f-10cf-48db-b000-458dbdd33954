[{"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\BlogPageClient.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\BlogDetailClient.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\loading.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\not-found.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\ClientsPageClient.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\ReelsPageClient.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\robots.ts": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\sitemap.ts": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\VideosPageClient.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\index.ts": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\ThemeToggle.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\WhatsAppButton.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogCard.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\Header.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\index.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailContent.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailHeader.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailSidebar.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogFeaturedImage.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogList.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\index.ts": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientCard.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsFilter.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsGrid.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsList.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsPageHeader.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsStats.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\FeaturedClientsSection.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\index.ts": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Contact.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\FeaturedWork.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Hero.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\index.ts": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Testimonials.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\index.ts": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\index.ts": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\ReelsList.tsx": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\videos\\index.ts": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\index.ts": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Footer.tsx": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Header.tsx": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\index.ts": "50", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx": "51", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\providers\\ThemeProvider.tsx": "52", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\badge.tsx": "53", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\button.tsx": "54", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\card.tsx": "55", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\dialog.tsx": "56", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\form.tsx": "57", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\index.ts": "58", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\input.tsx": "59", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\label.tsx": "60", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\select.tsx": "61", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\textarea.tsx": "62", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\blog.ts": "63", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\clients.ts": "64", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\reels.ts": "65", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\testimonials.ts": "66", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\videos.ts": "67", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\hooks\\useScrollAnimation.ts": "68", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\api.ts": "69", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\utils.ts": "70"}, {"size": 2682, "mtime": 1752987398094, "results": "71", "hashOfConfig": "72"}, {"size": 1005, "mtime": 1752976425782, "results": "73", "hashOfConfig": "72"}, {"size": 1146, "mtime": 1752976425795, "results": "74", "hashOfConfig": "72"}, {"size": 4841, "mtime": 1752976425806, "results": "75", "hashOfConfig": "72"}, {"size": 1287, "mtime": 1752976425815, "results": "76", "hashOfConfig": "72"}, {"size": 2959, "mtime": 1752976425851, "results": "77", "hashOfConfig": "72"}, {"size": 2620, "mtime": 1752976425864, "results": "78", "hashOfConfig": "72"}, {"size": 1182, "mtime": 1752976425868, "results": "79", "hashOfConfig": "72"}, {"size": 2887, "mtime": 1752976425711, "results": "80", "hashOfConfig": "72"}, {"size": 4862, "mtime": 1752976425719, "results": "81", "hashOfConfig": "72"}, {"size": 868, "mtime": 1752976425731, "results": "82", "hashOfConfig": "72"}, {"size": 1586, "mtime": 1752976425884, "results": "83", "hashOfConfig": "72"}, {"size": 12758, "mtime": 1752987495470, "results": "84", "hashOfConfig": "72"}, {"size": 268, "mtime": 1752976425742, "results": "85", "hashOfConfig": "72"}, {"size": 780, "mtime": 1752976425752, "results": "86", "hashOfConfig": "72"}, {"size": 1078, "mtime": 1752976425903, "results": "87", "hashOfConfig": "72"}, {"size": 14477, "mtime": 1752987362101, "results": "88", "hashOfConfig": "72"}, {"size": 91, "mtime": 1752976425935, "results": "89", "hashOfConfig": "72"}, {"size": 1075, "mtime": 1752976425924, "results": "90", "hashOfConfig": "72"}, {"size": 4194, "mtime": 1752976425931, "results": "91", "hashOfConfig": "72"}, {"size": 5749, "mtime": 1752986101682, "results": "92", "hashOfConfig": "72"}, {"size": 3863, "mtime": 1752986037307, "results": "93", "hashOfConfig": "72"}, {"size": 1024, "mtime": 1752976426020, "results": "94", "hashOfConfig": "72"}, {"size": 5139, "mtime": 1752976425962, "results": "95", "hashOfConfig": "72"}, {"size": 3305, "mtime": 1752976425968, "results": "96", "hashOfConfig": "72"}, {"size": 7322, "mtime": 1752976425978, "results": "97", "hashOfConfig": "72"}, {"size": 731, "mtime": 1752976425986, "results": "98", "hashOfConfig": "72"}, {"size": 3471, "mtime": 1752978297557, "results": "99", "hashOfConfig": "72"}, {"size": 451, "mtime": 1752976426002, "results": "100", "hashOfConfig": "72"}, {"size": 5685, "mtime": 1752976426032, "results": "101", "hashOfConfig": "72"}, {"size": 3123, "mtime": 1752976426035, "results": "102", "hashOfConfig": "72"}, {"size": 2728, "mtime": 1752976426042, "results": "103", "hashOfConfig": "72"}, {"size": 6014, "mtime": 1752976426051, "results": "104", "hashOfConfig": "72"}, {"size": 2886, "mtime": 1752987466701, "results": "105", "hashOfConfig": "72"}, {"size": 1668, "mtime": 1752976426065, "results": "106", "hashOfConfig": "72"}, {"size": 2701, "mtime": 1752985989905, "results": "107", "hashOfConfig": "72"}, {"size": 460, "mtime": 1752976426081, "results": "108", "hashOfConfig": "72"}, {"size": 10721, "mtime": 1752986139361, "results": "109", "hashOfConfig": "72"}, {"size": 7006, "mtime": 1752986272412, "results": "110", "hashOfConfig": "72"}, {"size": 6873, "mtime": 1752986229883, "results": "111", "hashOfConfig": "72"}, {"size": 238, "mtime": 1752976426116, "results": "112", "hashOfConfig": "72"}, {"size": 13620, "mtime": 1752976426110, "results": "113", "hashOfConfig": "72"}, {"size": 218, "mtime": 1752976425946, "results": "114", "hashOfConfig": "72"}, {"size": 80, "mtime": 1752976426132, "results": "115", "hashOfConfig": "72"}, {"size": 8497, "mtime": 1752976426126, "results": "116", "hashOfConfig": "72"}, {"size": 87, "mtime": 1752976426144, "results": "117", "hashOfConfig": "72"}, {"size": 135, "mtime": 1752976425915, "results": "118", "hashOfConfig": "72"}, {"size": 7275, "mtime": 1752987316422, "results": "119", "hashOfConfig": "72"}, {"size": 11413, "mtime": 1752976426158, "results": "120", "hashOfConfig": "72"}, {"size": 169, "mtime": 1752976426171, "results": "121", "hashOfConfig": "72"}, {"size": 1039, "mtime": 1752976426164, "results": "122", "hashOfConfig": "72"}, {"size": 406, "mtime": 1752976426181, "results": "123", "hashOfConfig": "72"}, {"size": 2152, "mtime": 1752976426191, "results": "124", "hashOfConfig": "72"}, {"size": 2602, "mtime": 1752976426195, "results": "125", "hashOfConfig": "72"}, {"size": 1989, "mtime": 1752976426199, "results": "126", "hashOfConfig": "72"}, {"size": 3813, "mtime": 1752976426206, "results": "127", "hashOfConfig": "72"}, {"size": 3759, "mtime": 1752976426213, "results": "128", "hashOfConfig": "72"}, {"size": 261, "mtime": 1752976426231, "results": "129", "hashOfConfig": "72"}, {"size": 967, "mtime": 1752976426243, "results": "130", "hashOfConfig": "72"}, {"size": 611, "mtime": 1752976426260, "results": "131", "hashOfConfig": "72"}, {"size": 6253, "mtime": 1752976426269, "results": "132", "hashOfConfig": "72"}, {"size": 759, "mtime": 1752976426277, "results": "133", "hashOfConfig": "72"}, {"size": 6615, "mtime": 1752976426304, "results": "134", "hashOfConfig": "72"}, {"size": 2593, "mtime": 1752976426311, "results": "135", "hashOfConfig": "72"}, {"size": 1555, "mtime": 1752976426317, "results": "136", "hashOfConfig": "72"}, {"size": 1850, "mtime": 1752976426322, "results": "137", "hashOfConfig": "72"}, {"size": 1461, "mtime": 1752976426331, "results": "138", "hashOfConfig": "72"}, {"size": 1565, "mtime": 1752976426341, "results": "139", "hashOfConfig": "72"}, {"size": 7155, "mtime": 1752976426350, "results": "140", "hashOfConfig": "72"}, {"size": 166, "mtime": 1752976426354, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "bg6jy7", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\BlogPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\BlogDetailClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\ClientsPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\ReelsPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\VideosPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\WhatsAppButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailContent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogFeaturedImage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsFilter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsGrid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsPageHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsStats.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\FeaturedClientsSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\FeaturedWork.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\ReelsList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\videos\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\providers\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\blog.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\clients.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\reels.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\testimonials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\videos.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\hooks\\useScrollAnimation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\utils.ts", [], []]