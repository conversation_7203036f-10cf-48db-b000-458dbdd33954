[{"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\BlogPageClient.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\BlogDetailClient.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\loading.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\not-found.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\ClientsPageClient.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\ReelsPageClient.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\robots.ts": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\sitemap.ts": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\VideosPageClient.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\index.ts": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\ThemeToggle.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\WhatsAppButton.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogCard.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\Header.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\index.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailContent.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailHeader.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailSidebar.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogFeaturedImage.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogList.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\index.ts": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientCard.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsFilter.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsGrid.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsList.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsPageHeader.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsStats.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\FeaturedClientsSection.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\index.ts": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Contact.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\FeaturedWork.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Hero.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\index.ts": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Testimonials.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\index.ts": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\index.ts": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\ReelsList.tsx": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\videos\\index.ts": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\index.ts": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Footer.tsx": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Header.tsx": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\index.ts": "50", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx": "51", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\providers\\ThemeProvider.tsx": "52", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\badge.tsx": "53", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\button.tsx": "54", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\card.tsx": "55", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\dialog.tsx": "56", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\form.tsx": "57", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\index.ts": "58", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\input.tsx": "59", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\label.tsx": "60", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\select.tsx": "61", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\textarea.tsx": "62", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\blog.ts": "63", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\clients.ts": "64", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\reels.ts": "65", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\testimonials.ts": "66", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\videos.ts": "67", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\hooks\\useScrollAnimation.ts": "68", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\api.ts": "69", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\utils.ts": "70", "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\video-dialog.tsx": "71"}, {"size": 2682, "mtime": 1752987398094, "results": "72", "hashOfConfig": "73"}, {"size": 1005, "mtime": 1752976425782, "results": "74", "hashOfConfig": "73"}, {"size": 1146, "mtime": 1752976425795, "results": "75", "hashOfConfig": "73"}, {"size": 4841, "mtime": 1752976425806, "results": "76", "hashOfConfig": "73"}, {"size": 1287, "mtime": 1752976425815, "results": "77", "hashOfConfig": "73"}, {"size": 2959, "mtime": 1752976425851, "results": "78", "hashOfConfig": "73"}, {"size": 2620, "mtime": 1752976425864, "results": "79", "hashOfConfig": "73"}, {"size": 1182, "mtime": 1752976425868, "results": "80", "hashOfConfig": "73"}, {"size": 2887, "mtime": 1752976425711, "results": "81", "hashOfConfig": "73"}, {"size": 4862, "mtime": 1752976425719, "results": "82", "hashOfConfig": "73"}, {"size": 868, "mtime": 1752976425731, "results": "83", "hashOfConfig": "73"}, {"size": 1586, "mtime": 1752976425884, "results": "84", "hashOfConfig": "73"}, {"size": 12758, "mtime": 1752987495470, "results": "85", "hashOfConfig": "73"}, {"size": 268, "mtime": 1752976425742, "results": "86", "hashOfConfig": "73"}, {"size": 780, "mtime": 1752976425752, "results": "87", "hashOfConfig": "73"}, {"size": 1078, "mtime": 1752976425903, "results": "88", "hashOfConfig": "73"}, {"size": 12434, "mtime": 1752990876631, "results": "89", "hashOfConfig": "73"}, {"size": 91, "mtime": 1752976425935, "results": "90", "hashOfConfig": "73"}, {"size": 1075, "mtime": 1752976425924, "results": "91", "hashOfConfig": "73"}, {"size": 4191, "mtime": 1752990958746, "results": "92", "hashOfConfig": "73"}, {"size": 5749, "mtime": 1752986101682, "results": "93", "hashOfConfig": "73"}, {"size": 3863, "mtime": 1752986037307, "results": "94", "hashOfConfig": "73"}, {"size": 1024, "mtime": 1752976426020, "results": "95", "hashOfConfig": "73"}, {"size": 5139, "mtime": 1752976425962, "results": "96", "hashOfConfig": "73"}, {"size": 3305, "mtime": 1752976425968, "results": "97", "hashOfConfig": "73"}, {"size": 7322, "mtime": 1752976425978, "results": "98", "hashOfConfig": "73"}, {"size": 731, "mtime": 1752976425986, "results": "99", "hashOfConfig": "73"}, {"size": 3742, "mtime": 1752989980499, "results": "100", "hashOfConfig": "73"}, {"size": 451, "mtime": 1752976426002, "results": "101", "hashOfConfig": "73"}, {"size": 5685, "mtime": 1752976426032, "results": "102", "hashOfConfig": "73"}, {"size": 3123, "mtime": 1752976426035, "results": "103", "hashOfConfig": "73"}, {"size": 2728, "mtime": 1752976426042, "results": "104", "hashOfConfig": "73"}, {"size": 6014, "mtime": 1752976426051, "results": "105", "hashOfConfig": "73"}, {"size": 2886, "mtime": 1752987466701, "results": "106", "hashOfConfig": "73"}, {"size": 1668, "mtime": 1752976426065, "results": "107", "hashOfConfig": "73"}, {"size": 2701, "mtime": 1752985989905, "results": "108", "hashOfConfig": "73"}, {"size": 460, "mtime": 1752976426081, "results": "109", "hashOfConfig": "73"}, {"size": 10721, "mtime": 1752986139361, "results": "110", "hashOfConfig": "73"}, {"size": 5776, "mtime": 1752990270826, "results": "111", "hashOfConfig": "73"}, {"size": 6873, "mtime": 1752986229883, "results": "112", "hashOfConfig": "73"}, {"size": 238, "mtime": 1752976426116, "results": "113", "hashOfConfig": "73"}, {"size": 13620, "mtime": 1752976426110, "results": "114", "hashOfConfig": "73"}, {"size": 218, "mtime": 1752976425946, "results": "115", "hashOfConfig": "73"}, {"size": 80, "mtime": 1752976426132, "results": "116", "hashOfConfig": "73"}, {"size": 8497, "mtime": 1752976426126, "results": "117", "hashOfConfig": "73"}, {"size": 87, "mtime": 1752976426144, "results": "118", "hashOfConfig": "73"}, {"size": 135, "mtime": 1752976425915, "results": "119", "hashOfConfig": "73"}, {"size": 7348, "mtime": 1752991058675, "results": "120", "hashOfConfig": "73"}, {"size": 11413, "mtime": 1752976426158, "results": "121", "hashOfConfig": "73"}, {"size": 169, "mtime": 1752976426171, "results": "122", "hashOfConfig": "73"}, {"size": 1039, "mtime": 1752976426164, "results": "123", "hashOfConfig": "73"}, {"size": 406, "mtime": 1752976426181, "results": "124", "hashOfConfig": "73"}, {"size": 2152, "mtime": 1752976426191, "results": "125", "hashOfConfig": "73"}, {"size": 2602, "mtime": 1752976426195, "results": "126", "hashOfConfig": "73"}, {"size": 1989, "mtime": 1752976426199, "results": "127", "hashOfConfig": "73"}, {"size": 3813, "mtime": 1752976426206, "results": "128", "hashOfConfig": "73"}, {"size": 3759, "mtime": 1752976426213, "results": "129", "hashOfConfig": "73"}, {"size": 293, "mtime": 1752990380832, "results": "130", "hashOfConfig": "73"}, {"size": 967, "mtime": 1752976426243, "results": "131", "hashOfConfig": "73"}, {"size": 611, "mtime": 1752976426260, "results": "132", "hashOfConfig": "73"}, {"size": 6253, "mtime": 1752976426269, "results": "133", "hashOfConfig": "73"}, {"size": 759, "mtime": 1752976426277, "results": "134", "hashOfConfig": "73"}, {"size": 6615, "mtime": 1752976426304, "results": "135", "hashOfConfig": "73"}, {"size": 2593, "mtime": 1752976426311, "results": "136", "hashOfConfig": "73"}, {"size": 1555, "mtime": 1752976426317, "results": "137", "hashOfConfig": "73"}, {"size": 1850, "mtime": 1752976426322, "results": "138", "hashOfConfig": "73"}, {"size": 1461, "mtime": 1752976426331, "results": "139", "hashOfConfig": "73"}, {"size": 1565, "mtime": 1752976426341, "results": "140", "hashOfConfig": "73"}, {"size": 7155, "mtime": 1752976426350, "results": "141", "hashOfConfig": "73"}, {"size": 166, "mtime": 1752976426354, "results": "142", "hashOfConfig": "73"}, {"size": 2170, "mtime": 1752990242206, "results": "143", "hashOfConfig": "73"}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "bg6jy7", {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\BlogPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\BlogDetailClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\ClientsPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\clients\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\ReelsPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\VideosPageClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\common\\WhatsAppButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetail\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailContent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogDetailSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogFeaturedImage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\BlogList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\blog\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsFilter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsGrid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsPageHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\ClientsStats.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\FeaturedClientsSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\clients\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\FeaturedWork.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\home\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\reels\\ReelsList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\features\\videos\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\providers\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\blog.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\clients.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\reels.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\testimonials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\data\\videos.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\hooks\\useScrollAnimation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\ui\\video-dialog.tsx", [], []]