(()=>{var e={};e.id=8489,e.ids=[8489],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22186:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>i});var o=r(96559),a=r(48088),u=r(37719),n=r(32190);async function i(){try{let e=n.NextResponse.json({success:!0,message:"Logged out successfully"});return e.cookies.set("auth-token","",{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:0,path:"/"}),e}catch(e){return console.error("Logout error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\auth\\logout\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=p;function x(){return(0,u.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(22186));module.exports=s})();