(()=>{var e={};e.id=6190,e.ids=[6190],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27746:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>n,In:()=>o,Sw:()=>i,gx:()=>a});var s=r(32190);function o(){let e=process.env.FRONTEND_URL||process.env.NEXT_PUBLIC_FRONTEND_URL;return[...new Set(["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","https://uttamrimal.com.np","https://www.uttamrimal.com.np","https://uttam-portfolio.vercel.app","https://uttam-portfolio-git-main.vercel.app",...e?[e]:[],...process.env.ADDITIONAL_CORS_ORIGINS?.split(",").map(e=>e.trim())||[]].filter(Boolean))]}function n(e,t){return!!e&&(!!t.includes(e)||t.some(t=>{if(t.includes("*")){let r=t.replace(/\*/g,".*");return RegExp(`^${r}$`).test(e)}return!1}))}function i(e,t){let r,s=n(t||"",o()),i=process.env.FRONTEND_URL||process.env.NEXT_PUBLIC_FRONTEND_URL;r=s?t||"*":i?"null":"*",e.headers.set("Access-Control-Allow-Origin",r),e.headers.set("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),e.headers.set("Access-Control-Allow-Headers","Content-Type, Authorization, X-Requested-With"),e.headers.set("Access-Control-Allow-Credentials",s||"*"===r?"true":"false")}function a(e){return async t=>{let r=function(e){let t,r=n(e||"",o()),s=process.env.FRONTEND_URL||process.env.NEXT_PUBLIC_FRONTEND_URL;return{"Access-Control-Allow-Origin":t=r?e||"*":s?"null":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":r||"*"===t?"true":"false"}}(t.headers.get("origin")||void 0);if("OPTIONS"===t.method)return new s.NextResponse(null,{status:200,headers:r});try{let s=await e(t);return Object.entries(r).forEach(([e,t])=>{s.headers.set(e,t)}),s}catch(t){console.error("API Error:",t);let e=s.NextResponse.json({success:!1,message:"Internal server error"},{status:500});return Object.entries(r).forEach(([t,r])=>{e.headers.set(t,r)}),e}}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29555:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>O,routeModule:()=>p,serverHooks:()=>N,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>l,OPTIONS:()=>u});var o=r(96559),n=r(48088),i=r(37719),a=r(32190),c=r(27746);async function l(e){let t=e.headers.get("origin"),r=(0,c.In)(),s=(0,c.Fc)(t||"",r),o=a.NextResponse.json({success:!0,cors:{requestOrigin:t,allowedOrigins:r,originAllowed:s,environment:"production",timestamp:new Date().toISOString()},environmentVariables:{FRONTEND_URL:process.env.FRONTEND_URL||"NOT_SET",NEXT_PUBLIC_FRONTEND_URL:process.env.NEXT_PUBLIC_FRONTEND_URL||"NOT_SET",NODE_ENV:"production",ADDITIONAL_CORS_ORIGINS:process.env.ADDITIONAL_CORS_ORIGINS||"NOT_SET"},headers:{origin:e.headers.get("origin"),host:e.headers.get("host"),userAgent:e.headers.get("user-agent"),referer:e.headers.get("referer")},message:s?"CORS is properly configured for this origin":"CORS is not configured for this origin"});return(0,c.Sw)(o,t),o}async function u(e){let t=e.headers.get("origin"),r=new a.NextResponse(null,{status:200});return(0,c.Sw)(r,t),r}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cors-test/route",pathname:"/api/cors-test",filename:"route",bundlePath:"app/api/cors-test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\cors-test\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:N}=p;function O(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(29555));module.exports=s})();