"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8161],{2525:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3717:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},5623:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},7924:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8578:(e,r,n)=>{n.d(r,{UC:()=>ra,q7:()=>rl,ZL:()=>ro,bL:()=>rn,l9:()=>rt});var t,o=n(2115),a=n(5185),l=n(6101),u=n(6081),i=n(5845),c=n(3655);function s(e,r,n){if(!r.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return r.get(e)}function d(e,r){var n=s(e,r,"get");return n.get?n.get.call(e):n.value}function f(e,r,n){var t=s(e,r,"set");if(t.set)t.set.call(e,n);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=n}return n}var p=n(9708),v=n(5155);function m(e){let r=e+"CollectionProvider",[n,t]=(0,u.A)(r),[a,i]=n(r,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:r,children:n}=e,t=o.useRef(null),l=o.useRef(new Map).current;return(0,v.jsx)(a,{scope:r,itemMap:l,collectionRef:t,children:n})};c.displayName=r;let s=e+"CollectionSlot",d=(0,p.TL)(s),f=o.forwardRef((e,r)=>{let{scope:n,children:t}=e,o=i(s,n),a=(0,l.s)(r,o.collectionRef);return(0,v.jsx)(d,{ref:a,children:t})});f.displayName=s;let m=e+"CollectionItemSlot",h="data-radix-collection-item",w=(0,p.TL)(m),g=o.forwardRef((e,r)=>{let{scope:n,children:t,...a}=e,u=o.useRef(null),c=(0,l.s)(r,u),s=i(m,n);return o.useEffect(()=>(s.itemMap.set(u,{ref:u,...a}),()=>void s.itemMap.delete(u))),(0,v.jsx)(w,{...{[h]:""},ref:c,children:t})});return g.displayName=m,[{Provider:c,Slot:f,ItemSlot:g},function(r){let n=i(e+"CollectionConsumer",r);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>r.indexOf(e.ref.current)-r.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},t]}var h=new WeakMap;function w(e,r){if("at"in Array.prototype)return Array.prototype.at.call(e,r);let n=function(e,r){let n=e.length,t=g(r),o=t>=0?t:n+t;return o<0||o>=n?-1:o}(e,r);return -1===n?void 0:e[n]}function g(e){return e!=e||0===e?0:Math.trunc(e)}t=new WeakMap;var y=o.createContext(void 0);function x(e){let r=o.useContext(y);return e||r||"ltr"}var b=n(9178),M=n(2293),C=n(7900),R=n(1285),j=n(5152),k=n(4378),D=n(8905),_=n(9033),I="rovingFocusGroup.onEntryFocus",A={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[P,T,S]=m(E),[N,F]=(0,u.A)(E,[S]),[L,O]=N(E),K=o.forwardRef((e,r)=>(0,v.jsx)(P.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(P.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(G,{...e,ref:r})})}));K.displayName=E;var G=o.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:t,loop:u=!1,dir:s,currentTabStopId:d,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:p,onEntryFocus:m,preventScrollOnEntryFocus:h=!1,...w}=e,g=o.useRef(null),y=(0,l.s)(r,g),b=x(s),[M,C]=(0,i.i)({prop:d,defaultProp:null!=f?f:null,onChange:p,caller:E}),[R,j]=o.useState(!1),k=(0,_.c)(m),D=T(n),P=o.useRef(!1),[S,N]=o.useState(0);return o.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(I,k),()=>e.removeEventListener(I,k)},[k]),(0,v.jsx)(L,{scope:n,orientation:t,dir:b,loop:u,currentTabStopId:M,onItemFocus:o.useCallback(e=>C(e),[C]),onItemShiftTab:o.useCallback(()=>j(!0),[]),onFocusableItemAdd:o.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>N(e=>e-1),[]),children:(0,v.jsx)(c.sG.div,{tabIndex:R||0===S?-1:0,"data-orientation":t,...w,ref:y,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let r=!P.current;if(e.target===e.currentTarget&&r&&!R){let r=new CustomEvent(I,A);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=D().filter(e=>e.focusable);q([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),h)}}P.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>j(!1))})})}),B="RovingFocusGroupItem",U=o.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:t=!0,active:l=!1,tabStopId:u,children:i,...s}=e,d=(0,R.B)(),f=u||d,p=O(B,n),m=p.currentTabStopId===f,h=T(n),{onFocusableItemAdd:w,onFocusableItemRemove:g,currentTabStopId:y}=p;return o.useEffect(()=>{if(t)return w(),()=>g()},[t,w,g]),(0,v.jsx)(P.ItemSlot,{scope:n,id:f,focusable:t,active:l,children:(0,v.jsx)(c.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...s,ref:r,onMouseDown:(0,a.m)(e.onMouseDown,e=>{t?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return V[o]}(e,p.orientation,p.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();let t=n.indexOf(e.currentTarget);n=p.loop?function(e,r){return e.map((n,t)=>e[(r+t)%e.length])}(n,t+1):n.slice(t+1)}setTimeout(()=>q(n))}}),children:"function"==typeof i?i({isCurrentTabStop:m,hasTabStop:null!=y}):i})})});U.displayName=B;var V={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function q(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var z=n(8168),X=n(3795),H=["Enter"," "],W=["ArrowUp","PageDown","End"],Z=["ArrowDown","PageUp","Home",...W],Y={ltr:[...H,"ArrowRight"],rtl:[...H,"ArrowLeft"]},J={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Q="Menu",[$,ee,er]=m(Q),[en,et]=(0,u.A)(Q,[er,j.Bk,F]),eo=(0,j.Bk)(),ea=F(),[el,eu]=en(Q),[ei,ec]=en(Q),es=e=>{let{__scopeMenu:r,open:n=!1,children:t,dir:a,onOpenChange:l,modal:u=!0}=e,i=eo(r),[c,s]=o.useState(null),d=o.useRef(!1),f=(0,_.c)(l),p=x(a);return o.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,v.jsx)(j.bL,{...i,children:(0,v.jsx)(el,{scope:r,open:n,onOpenChange:f,content:c,onContentChange:s,children:(0,v.jsx)(ei,{scope:r,onClose:o.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:p,modal:u,children:t})})})};es.displayName=Q;var ed=o.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=eo(n);return(0,v.jsx)(j.Mz,{...o,...t,ref:r})});ed.displayName="MenuAnchor";var ef="MenuPortal",[ep,ev]=en(ef,{forceMount:void 0}),em=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=eu(ef,r);return(0,v.jsx)(ep,{scope:r,forceMount:n,children:(0,v.jsx)(D.C,{present:n||a.open,children:(0,v.jsx)(k.Z,{asChild:!0,container:o,children:t})})})};em.displayName=ef;var eh="MenuContent",[ew,eg]=en(eh),ey=o.forwardRef((e,r)=>{let n=ev(eh,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=eu(eh,e.__scopeMenu),l=ec(eh,e.__scopeMenu);return(0,v.jsx)($.Provider,{scope:e.__scopeMenu,children:(0,v.jsx)(D.C,{present:t||a.open,children:(0,v.jsx)($.Slot,{scope:e.__scopeMenu,children:l.modal?(0,v.jsx)(ex,{...o,ref:r}):(0,v.jsx)(eb,{...o,ref:r})})})})}),ex=o.forwardRef((e,r)=>{let n=eu(eh,e.__scopeMenu),t=o.useRef(null),u=(0,l.s)(r,t);return o.useEffect(()=>{let e=t.current;if(e)return(0,z.Eq)(e)},[]),(0,v.jsx)(eC,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),eb=o.forwardRef((e,r)=>{let n=eu(eh,e.__scopeMenu);return(0,v.jsx)(eC,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),eM=(0,p.TL)("MenuContent.ScrollLock"),eC=o.forwardRef((e,r)=>{let{__scopeMenu:n,loop:t=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:d,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:h,onDismiss:w,disableOutsideScroll:g,...y}=e,x=eu(eh,n),R=ec(eh,n),k=eo(n),D=ea(n),_=ee(n),[I,A]=o.useState(null),E=o.useRef(null),P=(0,l.s)(r,E,x.onContentChange),T=o.useRef(0),S=o.useRef(""),N=o.useRef(0),F=o.useRef(null),L=o.useRef("right"),O=o.useRef(0),G=g?X.A:o.Fragment,B=e=>{var r,n;let t=S.current+e,o=_().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(n=o.find(e=>e.textValue===u))?void 0:n.ref.current;!function e(r){S.current=r,window.clearTimeout(T.current),""!==r&&(T.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};o.useEffect(()=>()=>window.clearTimeout(T.current),[]),(0,M.Oh)();let U=o.useCallback(e=>{var r,n;return L.current===(null==(r=F.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,c=l.y,s=u.x,d=u.y;c>t!=d>t&&n<(s-i)*(t-c)/(d-c)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=F.current)?void 0:n.area)},[]);return(0,v.jsx)(ew,{scope:n,searchRef:S,onItemEnter:o.useCallback(e=>{U(e)&&e.preventDefault()},[U]),onItemLeave:o.useCallback(e=>{var r;U(e)||(null==(r=E.current)||r.focus(),A(null))},[U]),onTriggerLeave:o.useCallback(e=>{U(e)&&e.preventDefault()},[U]),pointerGraceTimerRef:N,onPointerGraceIntentChange:o.useCallback(e=>{F.current=e},[]),children:(0,v.jsx)(G,{...g?{as:eM,allowPinchZoom:!0}:void 0,children:(0,v.jsx)(C.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,a.m)(i,e=>{var r;e.preventDefault(),null==(r=E.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,v.jsx)(b.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:h,onDismiss:w,children:(0,v.jsx)(K,{asChild:!0,...D,dir:R.dir,orientation:"vertical",loop:t,currentTabStopId:I,onCurrentTabStopIdChange:A,onEntryFocus:(0,a.m)(d,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,v.jsx)(j.UC,{role:"menu","aria-orientation":"vertical","data-state":eZ(x.open),"data-radix-menu-content":"",dir:R.dir,...k,...y,ref:P,style:{outline:"none",...y.style},onKeyDown:(0,a.m)(y.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&B(e.key));let o=E.current;if(e.target!==o||!Z.includes(e.key))return;e.preventDefault();let a=_().filter(e=>!e.disabled).map(e=>e.ref.current);W.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,a.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(T.current),S.current="")}),onPointerMove:(0,a.m)(e.onPointerMove,eQ(e=>{let r=e.target,n=O.current!==e.clientX;e.currentTarget.contains(r)&&n&&(L.current=e.clientX>O.current?"right":"left",O.current=e.clientX)}))})})})})})})});ey.displayName=eh;var eR=o.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,v.jsx)(c.sG.div,{role:"group",...t,ref:r})});eR.displayName="MenuGroup";var ej=o.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,v.jsx)(c.sG.div,{...t,ref:r})});ej.displayName="MenuLabel";var ek="MenuItem",eD="menu.itemSelect",e_=o.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:t,...u}=e,i=o.useRef(null),s=ec(ek,e.__scopeMenu),d=eg(ek,e.__scopeMenu),f=(0,l.s)(r,i),p=o.useRef(!1);return(0,v.jsx)(eI,{...u,ref:f,disabled:n,onClick:(0,a.m)(e.onClick,()=>{let e=i.current;if(!n&&e){let r=new CustomEvent(eD,{bubbles:!0,cancelable:!0});e.addEventListener(eD,e=>null==t?void 0:t(e),{once:!0}),(0,c.hO)(e,r),r.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),p.current=!0},onPointerUp:(0,a.m)(e.onPointerUp,e=>{var r;p.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{let r=""!==d.searchRef.current;n||r&&" "===e.key||H.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});e_.displayName=ek;var eI=o.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:t=!1,textValue:u,...i}=e,s=eg(ek,n),d=ea(n),f=o.useRef(null),p=(0,l.s)(r,f),[m,h]=o.useState(!1),[w,g]=o.useState("");return o.useEffect(()=>{let e=f.current;if(e){var r;g((null!=(r=e.textContent)?r:"").trim())}},[i.children]),(0,v.jsx)($.ItemSlot,{scope:n,disabled:t,textValue:null!=u?u:w,children:(0,v.jsx)(U,{asChild:!0,...d,focusable:!t,children:(0,v.jsx)(c.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":t||void 0,"data-disabled":t?"":void 0,...i,ref:p,onPointerMove:(0,a.m)(e.onPointerMove,eQ(e=>{t?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,a.m)(e.onPointerLeave,eQ(e=>s.onItemLeave(e))),onFocus:(0,a.m)(e.onFocus,()=>h(!0)),onBlur:(0,a.m)(e.onBlur,()=>h(!1))})})})}),eA=o.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...o}=e;return(0,v.jsx)(eO,{scope:e.__scopeMenu,checked:n,children:(0,v.jsx)(e_,{role:"menuitemcheckbox","aria-checked":eY(n)?"mixed":n,...o,ref:r,"data-state":eJ(n),onSelect:(0,a.m)(o.onSelect,()=>null==t?void 0:t(!!eY(n)||!n),{checkForDefaultPrevented:!1})})})});eA.displayName="MenuCheckboxItem";var eE="MenuRadioGroup",[eP,eT]=en(eE,{value:void 0,onValueChange:()=>{}}),eS=o.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,_.c)(t);return(0,v.jsx)(eP,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,v.jsx)(eR,{...o,ref:r})})});eS.displayName=eE;var eN="MenuRadioItem",eF=o.forwardRef((e,r)=>{let{value:n,...t}=e,o=eT(eN,e.__scopeMenu),l=n===o.value;return(0,v.jsx)(eO,{scope:e.__scopeMenu,checked:l,children:(0,v.jsx)(e_,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":eJ(l),onSelect:(0,a.m)(t.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});eF.displayName=eN;var eL="MenuItemIndicator",[eO,eK]=en(eL,{checked:!1}),eG=o.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eK(eL,n);return(0,v.jsx)(D.C,{present:t||eY(a.checked)||!0===a.checked,children:(0,v.jsx)(c.sG.span,{...o,ref:r,"data-state":eJ(a.checked)})})});eG.displayName=eL;var eB=o.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,v.jsx)(c.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});eB.displayName="MenuSeparator";var eU=o.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=eo(n);return(0,v.jsx)(j.i3,{...o,...t,ref:r})});eU.displayName="MenuArrow";var[eV,eq]=en("MenuSub"),ez="MenuSubTrigger",eX=o.forwardRef((e,r)=>{let n=eu(ez,e.__scopeMenu),t=ec(ez,e.__scopeMenu),u=eq(ez,e.__scopeMenu),i=eg(ez,e.__scopeMenu),c=o.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},p=o.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return o.useEffect(()=>p,[p]),o.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,v.jsx)(ed,{asChild:!0,...f,children:(0,v.jsx)(eI,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":eZ(n.open),...e,ref:(0,l.t)(r,u.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,a.m)(e.onPointerMove,eQ(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||c.current||(i.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,a.m)(e.onPointerLeave,eQ(e=>{var r,t;p();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,a.m)(e.onKeyDown,r=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==r.key)&&Y[t.dir].includes(r.key)){var a;n.onOpenChange(!0),null==(a=n.content)||a.focus(),r.preventDefault()}})})})});eX.displayName=ez;var eH="MenuSubContent",eW=o.forwardRef((e,r)=>{let n=ev(eh,e.__scopeMenu),{forceMount:t=n.forceMount,...u}=e,i=eu(eh,e.__scopeMenu),c=ec(eh,e.__scopeMenu),s=eq(eH,e.__scopeMenu),d=o.useRef(null),f=(0,l.s)(r,d);return(0,v.jsx)($.Provider,{scope:e.__scopeMenu,children:(0,v.jsx)(D.C,{present:t||i.open,children:(0,v.jsx)($.Slot,{scope:e.__scopeMenu,children:(0,v.jsx)(eC,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;c.isUsingKeyboardRef.current&&(null==(r=d.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,a.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=J[c.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null==(t=s.trigger)||t.focus(),e.preventDefault()}})})})})})});function eZ(e){return e?"open":"closed"}function eY(e){return"indeterminate"===e}function eJ(e){return eY(e)?"indeterminate":e?"checked":"unchecked"}function eQ(e){return r=>"mouse"===r.pointerType?e(r):void 0}eW.displayName=eH;var e$="DropdownMenu",[e0,e1]=(0,u.A)(e$,[et]),e2=et(),[e5,e4]=e0(e$),e8=e=>{let{__scopeDropdownMenu:r,children:n,dir:t,open:a,defaultOpen:l,onOpenChange:u,modal:c=!0}=e,s=e2(r),d=o.useRef(null),[f,p]=(0,i.i)({prop:a,defaultProp:null!=l&&l,onChange:u,caller:e$});return(0,v.jsx)(e5,{scope:r,triggerId:(0,R.B)(),triggerRef:d,contentId:(0,R.B)(),open:f,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,v.jsx)(es,{...s,open:f,onOpenChange:p,dir:t,modal:c,children:n})})};e8.displayName=e$;var e3="DropdownMenuTrigger",e9=o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...o}=e,u=e4(e3,n),i=e2(n);return(0,v.jsx)(ed,{asChild:!0,...i,children:(0,v.jsx)(c.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...o,ref:(0,l.t)(r,u.triggerRef),onPointerDown:(0,a.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e9.displayName=e3;var e7=e=>{let{__scopeDropdownMenu:r,...n}=e,t=e2(r);return(0,v.jsx)(em,{...t,...n})};e7.displayName="DropdownMenuPortal";var e6="DropdownMenuContent",re=o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,l=e4(e6,n),u=e2(n),i=o.useRef(!1);return(0,v.jsx)(ey,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...t,ref:r,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,a.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});re.displayName=e6,o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eR,{...o,...t,ref:r})}).displayName="DropdownMenuGroup",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(ej,{...o,...t,ref:r})}).displayName="DropdownMenuLabel";var rr=o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(e_,{...o,...t,ref:r})});rr.displayName="DropdownMenuItem",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eA,{...o,...t,ref:r})}).displayName="DropdownMenuCheckboxItem",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eS,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eF,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eG,{...o,...t,ref:r})}).displayName="DropdownMenuItemIndicator",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eB,{...o,...t,ref:r})}).displayName="DropdownMenuSeparator",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eU,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eX,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",o.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=e2(n);return(0,v.jsx)(eW,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var rn=e8,rt=e9,ro=e7,ra=re,rl=rr}}]);