import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Client from '@/models/Client';
import { requireAuth } from '@/lib/auth';
import { withCors } from '@/lib/cors';
import slugify from 'slugify';

// GET /api/clients - Get all clients with pagination and filters
export const GET = withCors(async (request: NextRequest) => {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const industry = searchParams.get('industry');
    const featured = searchParams.get('featured');
    const search = searchParams.get('search');

    // Build query
    const query: any = {};

    if (status) query.status = status;
    if (industry) query.industry = industry;
    if (featured !== null) query.featured = featured === 'true';
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { industry: { $regex: search, $options: 'i' } },
        { projectType: { $regex: search, $options: 'i' } },
      ];
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Get clients with pagination
    const clients = await Client.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count
    const total = await Client.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: clients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Get clients error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// POST /api/clients - Create new client
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const data = await request.json();
    const {
      name,
      logo,
      description,
      website,
      industry,
      projectType,
      featured = false,
      status = 'draft',
      order = 0,
    } = data;

    // Validate required fields
    if (!name || !logo || !description) {
      return NextResponse.json(
        { error: 'Name, logo, and description are required' },
        { status: 400 }
      );
    }

    // Generate slug
    const baseSlug = slugify(name, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });

    // Ensure unique slug
    let slug = baseSlug;
    let counter = 1;
    while (await Client.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Create client
    const client = new Client({
      name,
      slug,
      logo,
      description,
      website,
      industry,
      projectType,
      featured,
      status,
      order,
    });

    await client.save();

    return NextResponse.json({
      success: true,
      data: client,
    }, { status: 201 });
  } catch (error) {
    console.error('Create client error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
