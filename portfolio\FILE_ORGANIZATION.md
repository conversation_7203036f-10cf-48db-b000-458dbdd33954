# File Organization Guide

## 📁 **Current Project Structure**

This portfolio project now follows a **feature-based architecture** with proper separation of concerns. Here's the complete file organization:

```
src/
├── app/                           # Next.js App Router
│   ├── blog/                      # Blog pages
│   ├── clients/                   # Clients pages
│   ├── reels/                     # Reels pages
│   ├── videos/                    # Videos pages
│   ├── layout.tsx                 # Root layout
│   ├── page.tsx                   # Homepage
│   └── not-found.tsx              # 404 page
│
├── components/                    # All React components
│   ├── features/                  # Feature-specific components
│   │   ├── blog/                  # Blog-related components
│   │   │   ├── BlogList.tsx       # Main blog listing component
│   │   │   ├── BlogCard.tsx       # Individual blog post card
│   │   │   ├── BlogDetailHeader.tsx
│   │   │   ├── BlogDetailContent.tsx
│   │   │   ├── BlogDetailSidebar.tsx
│   │   │   ├── BlogFeaturedImage.tsx
│   │   │   ├── BlogDetail/        # Blog detail sub-components
│   │   │   │   └── index.tsx      # Main blog detail component
│   │   │   └── index.ts           # Feature exports
│   │   │
│   │   ├── clients/               # Client-related components
│   │   │   ├── ClientsList.tsx    # Main clients listing
│   │   │   ├── ClientCard.tsx     # Individual client card
│   │   │   ├── ClientsFilter.tsx  # Client filtering
│   │   │   ├── ClientsGrid.tsx    # Client grid layout
│   │   │   ├── ClientsPageHeader.tsx
│   │   │   ├── ClientsStats.tsx   # Client statistics
│   │   │   ├── FeaturedClientsSection.tsx
│   │   │   └── index.ts           # Feature exports
│   │   │
│   │   ├── home/                  # Homepage components
│   │   │   ├── Hero.tsx           # Hero section
│   │   │   ├── FeaturedWork.tsx   # Featured work section
│   │   │   ├── Contact.tsx        # Contact section
│   │   │   ├── Testimonials.tsx   # Testimonials section
│   │   │   └── index.ts           # Feature exports
│   │   │
│   │   ├── reels/                 # Reels components
│   │   │   ├── ReelsList.tsx      # Main reels listing
│   │   │   └── index.ts           # Feature exports
│   │   │
│   │   ├── videos/                # Video components (future)
│   │   │   └── index.ts           # Feature exports
│   │   │
│   │   └── index.ts               # All features export
│   │
│   ├── layout/                    # Layout components
│   │   ├── Header.tsx             # Site header/navigation
│   │   ├── Footer.tsx             # Site footer
│   │   ├── ScrollToTop.tsx        # Scroll to top button
│   │   └── index.ts               # Layout exports
│   │
│   ├── common/                    # Shared utility components
│   │   ├── WhatsAppButton.tsx     # WhatsApp contact button
│   │   └── index.ts               # Common exports
│   │
│   ├── ui/                        # UI primitives (shadcn/ui)
│   │   ├── button.tsx             # Button component
│   │   ├── card.tsx               # Card component
│   │   ├── dialog.tsx             # Dialog/Modal component
│   │   ├── input.tsx              # Input component
│   │   ├── label.tsx              # Label component
│   │   ├── select.tsx             # Select component
│   │   ├── textarea.tsx           # Textarea component
│   │   ├── badge.tsx              # Badge component
│   │   └── form.tsx               # Form component
│   │
│   └── index.ts                   # Main components export
│
├── data/                          # Static data files
│   ├── blog.ts                    # Blog posts data
│   ├── clients.ts                 # Clients data
│   ├── reels.ts                   # Reels data
│   ├── testimonials.ts            # Testimonials data
│   └── videos.ts                  # Videos data
│
├── lib/                           # Utility functions
│   ├── api.ts                     # API functions
│   └── utils.ts                   # General utilities
│
└── public/                        # Static assets
    ├── images/                    # Image assets
    ├── manifest.json              # PWA manifest
    └── *.svg                      # SVG icons
```

## 🎯 **Component Categories**

### 1. **Feature Components** (`/features/`)
- **Purpose**: Business logic and feature-specific functionality
- **Examples**: `BlogList`, `ClientCard`, `Hero`
- **Characteristics**:
  - Contains business logic
  - Feature-specific
  - May use UI components internally
  - Organized by feature domain

### 2. **Layout Components** (`/layout/`)
- **Purpose**: Site-wide layout and navigation
- **Examples**: `Header`, `Footer`, `ScrollToTop`
- **Characteristics**:
  - Used across multiple pages
  - Handle site-wide functionality
  - Consistent across the application

### 3. **Common Components** (`/common/`)
- **Purpose**: Shared utility components
- **Examples**: `WhatsAppButton`
- **Characteristics**:
  - Used across multiple features
  - Utility-focused
  - Not feature-specific

### 4. **UI Components** (`/ui/`)
- **Purpose**: Basic, reusable UI primitives
- **Examples**: `Button`, `Input`, `Card`, `Dialog`
- **Characteristics**:
  - No business logic
  - Highly reusable
  - Style-focused
  - Props-driven
  - Based on shadcn/ui

## 📋 **Import Patterns**

### ✅ **Recommended Import Patterns**

```typescript
// Using barrel exports (cleaner)
import { BlogList, BlogCard } from '@/components/features/blog';
import { Header, Footer } from '@/components/layout';
import { Button, Card } from '@/components/ui';

// Direct imports (more explicit)
import BlogList from '@/components/features/blog/BlogList';
import Header from '@/components/layout/Header';
import { Button } from '@/components/ui/button';

// Feature-specific imports
import { ClientsList, ClientCard } from '@/components/features/clients';
```

### ❌ **Avoid These Patterns**

```typescript
// Relative imports (hard to maintain)
import BlogCard from '../../../features/blog/BlogCard';
import Header from '../../layout/Header';

// Root-level imports (breaks organization)
import BlogCard from '@/components/BlogCard';
```

## 🔄 **Migration Benefits**

### **Before (Old Structure)**
- Components scattered in root directory
- Duplicate components in multiple locations
- Unclear component relationships
- Difficult to find related components
- No clear separation of concerns

### **After (New Structure)**
- Clear feature-based organization
- No duplicate components
- Easy to find related components
- Better separation of concerns
- Scalable architecture
- Cleaner import statements

## 🚀 **Development Guidelines**

### **Adding New Components**

1. **Determine the category**:
   - Feature-specific? → `/features/{feature}/`
   - Layout-related? → `/layout/`
   - Shared utility? → `/common/`
   - UI primitive? → `/ui/`

2. **Follow naming conventions**:
   - PascalCase for component files: `BlogCard.tsx`
   - Descriptive names: `FeaturedClientsSection`
   - Consistent prefixes: `Blog*`, `Client*`, `Video*`

3. **Update index files**:
   - Add exports to appropriate `index.ts` files
   - Maintain barrel export structure

### **Best Practices**

- Keep components focused on single responsibility
- Use TypeScript interfaces for props
- Follow the established naming conventions
- Update index files when adding new components
- Use absolute imports with `@/` prefix
- Prefer barrel exports for cleaner imports

## 📦 **Index Files (Barrel Exports)**

Each directory has an `index.ts` file that exports all components from that directory:

```typescript
// src/components/features/blog/index.ts
export { default as BlogList } from './BlogList';
export { default as BlogCard } from './BlogCard';
// ... other exports

// Usage
import { BlogList, BlogCard } from '@/components/features/blog';
```

This enables cleaner imports and better developer experience.

## 🔧 **Maintenance**

- **Regular cleanup**: Remove unused components
- **Consistent naming**: Follow established conventions
- **Update documentation**: Keep this guide current
- **Index file maintenance**: Update exports when adding/removing components

This organization follows industry best practices and scales well as the project grows.
