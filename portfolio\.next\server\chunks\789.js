"use strict";exports.id=789,exports.ids=[789],exports.modules={19093:(e,t,r)=>{r.d(t,{A:()=>f});var a=r(60687),s=r(85814),n=r.n(s),o=r(30474),i=r(40228),l=r(48730),d=r(58869),c=r(96834),u=r(62185);function f({post:e,variant:t="default",showExcerpt:r=!0,showAuthor:s=!0,className:f=""}){return(0,a.jsx)("article",{className:`group ${{default:"bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105",featured:"bg-card rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 border-2 border-accent",compact:"bg-card rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300"}[t]} ${f}`,children:(0,a.jsxs)(n(),{href:`/blog/${e.slug}`,children:[(0,a.jsxs)("div",{className:`relative ${{default:"aspect-video",featured:"aspect-video",compact:"aspect-[4/3]"}[t]} overflow-hidden`,children:[e.thumbnail?(0,a.jsx)(o.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-primary-foreground text-6xl font-heading font-bold opacity-20",children:e.title.charAt(0)})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsx)("div",{className:"absolute top-4 left-4",children:(0,a.jsx)(c.E,{variant:"secondary",className:"bg-secondary/90 text-secondary-foreground backdrop-blur-sm",children:e.category})}),e.featured&&(0,a.jsx)("div",{className:"absolute top-4 right-4",children:(0,a.jsx)(c.E,{variant:"default",className:"bg-accent text-accent-foreground backdrop-blur-sm",children:"Featured"})})]}),(0,a.jsxs)("div",{className:{default:"p-6",featured:"p-8",compact:"p-4"}[t],children:[(0,a.jsx)("h3",{className:`${{default:"text-xl font-heading font-semibold text-primary mb-3",featured:"text-2xl font-heading font-bold text-primary mb-4",compact:"text-lg font-heading font-semibold text-primary mb-2"}[t]} group-hover:text-secondary transition-colors duration-300 line-clamp-2`,children:e.title}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(i.A,{size:14}),(0,a.jsx)("span",{children:(0,u.Yq)(e.createdAt)})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(l.A,{size:14}),(0,a.jsxs)("span",{children:[e.readTime," min read"]})]}),s&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(d.A,{size:14}),(0,a.jsx)("span",{children:"Uttam Rimal"})]})]}),r&&e.excerpt&&(0,a.jsx)("p",{className:`text-muted-foreground leading-relaxed mb-4 ${"compact"===t?"text-sm line-clamp-2":"line-clamp-3"}`,children:e.excerpt}),e.tags&&e.tags.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,"compact"===t?2:3).map(e=>(0,a.jsxs)(c.E,{variant:"outline",className:"text-xs hover:bg-primary hover:text-primary-foreground transition-colors",children:["#",e]},e)),e.tags.length>("compact"===t?2:3)&&(0,a.jsxs)(c.E,{variant:"outline",className:"text-xs",children:["+",e.tags.length-("compact"===t?2:3)]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-primary font-semibold text-sm group-hover:text-secondary transition-colors duration-300",children:"Read More →"}),"featured"===t&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${"published"===e.status?"bg-success":"bg-warning"}`}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.status})]})]})]})]})})}},31646:(e,t,r)=>{r.d(t,{default:()=>i});var a=r(60687),s=r(43210),n=r(2975),o=r(29523);function i(){let[e,t]=(0,s.useState)(!1);return e?(0,a.jsx)(o.$,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300","aria-label":"Scroll to top",children:(0,a.jsx)(n.A,{size:20})}):null}},33484:(e,t,r)=>{r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\components\\\\layout\\\\ScrollToTop.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx","default")},40228:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58869:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62185:(e,t,r)=>{r.d(t,{GB:()=>l,HI:()=>f,I9:()=>p,Yq:()=>m,dS:()=>c,g6:()=>s,lA:()=>g,rT:()=>o});async function a(e){try{let t=await fetch(`https://uttam-backend.vercel.app/api${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return console.error(`Failed to fetch ${e}:`,t.status,t.statusText),[];let r=await t.json();if(!r.success)return console.error(`API error for ${e}:`,r.message),[];return r.data||[]}catch(t){return console.error(`Network error fetching ${e}:`,t),[]}}async function s(){return(await a("/blog")).filter(e=>"published"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}async function n(){return(await a("/videos")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function o(){return(await n()).filter(e=>e.featured)}async function i(){return(await a("/reels")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function l(){return(await i()).filter(e=>e.featured)}async function d(){let e=await a("/testimonials");if(0===e.length){let{testimonials:e}=await r.e(304).then(r.bind(r,15304));return e.map((e,t)=>({...e,_id:e.id.toString(),slug:e.name.toLowerCase().replace(/\s+/g,"-"),rating:e.rating||5,status:"published",order:t+1,featured:t<3,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}))}return e.filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function c(){return(await d()).filter(e=>e.featured)}async function u(){return(await a("/clients")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function f(){return(await u()).filter(e=>e.featured)}function p(e){return`https://img.youtube.com/vi/${e}/maxresdefault.jpg`}function g(e){return`https://img.youtube.com/vi/${e}/maxresdefault.jpg`}function m(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},96834:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(60687);r(43210);var s=r(8730),n=r(24224),o=r(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",success:"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40",warning:"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40",info:"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}}};