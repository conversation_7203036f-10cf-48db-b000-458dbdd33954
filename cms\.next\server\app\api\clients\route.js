"use strict";(()=>{var e={};e.id=2898,e.ids=[2898],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53371:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>v,serverHooks:()=>y,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>g,POST:()=>m});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),p=t(75745),u=t(63675),c=t(12909),d=t(27746),l=t(67912),x=t.n(l);let g=(0,d.gx)(async e=>{try{await (0,p.A)();let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),o=r.get("status"),n=r.get("industry"),a=r.get("featured"),c=r.get("search"),d={};o&&(d.status=o),n&&(d.industry=n),null!==a&&(d.featured="true"===a),c&&(d.$or=[{name:{$regex:c,$options:"i"}},{description:{$regex:c,$options:"i"}},{industry:{$regex:c,$options:"i"}},{projectType:{$regex:c,$options:"i"}}]);let l=(t-1)*s,x=await u.A.find(d).sort({order:1,createdAt:-1}).skip(l).limit(s).lean(),g=await u.A.countDocuments(d);return i.NextResponse.json({success:!0,data:x,pagination:{page:t,limit:s,total:g,pages:Math.ceil(g/s)}})}catch(e){return console.error("Get clients error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}),m=(0,c.oC)(async e=>{try{await (0,p.A)();let{name:r,logo:t,description:s,website:o,industry:n,projectType:a,featured:c=!1,status:d="draft",order:l=0}=await e.json();if(!r||!t||!s)return i.NextResponse.json({error:"Name, logo, and description are required"},{status:400});let g=x()(r,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g}),m=g,v=1;for(;await u.A.findOne({slug:m});)m=`${g}-${v}`,v++;let w=new u.A({name:r,slug:m,logo:t,description:s,website:o,industry:n,projectType:a,featured:c,status:d,order:l});return await w.save(),i.NextResponse.json({success:!0,data:w},{status:201})}catch(e){return console.error("Create client error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}),v=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/clients/route",pathname:"/api/clients",filename:"route",bundlePath:"app/api/clients/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\clients\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:y}=v;function h(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},55511:e=>{e.exports=require("crypto")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,8404],()=>t(53371));module.exports=s})();