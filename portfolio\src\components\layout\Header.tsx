'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { Menu, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/common/ThemeToggle';

const navigation = [
  { name: 'Home', href: '/', anchor: '#home' },
  {
    name: 'Videos',
    href: '/',
    anchor: '#featured-work',
    dropdown: [
      { name: 'Featured Videos', href: '/', anchor: '#featured-work' },
      { name: 'View All Videos', href: '/videos' }
    ]
  },
  { name: 'Clients', href: '/', anchor: '#clients' },
  {
    name: 'Shorts',
    href: '/',
    anchor: '#shorts',
    dropdown: [
      { name: 'Featured Shorts', href: '/', anchor: '#shorts' },
      { name: 'View All Shorts', href: '/reels' }
    ]
  },
  {
    name: 'Blog',
    href: '/',
    anchor: '#blog',
    dropdown: [
      { name: 'Latest Posts', href: '/', anchor: '#blog' },
      { name: 'View All Posts', href: '/blog' }
    ]
  },
  { name: 'Testimonials', href: '/', anchor: '#testimonials' },
  { name: 'Contact', href: '/', anchor: '#contact' },
];

export default function Header() {
  const router = useRouter();
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);

      // Only update active section on home page
      if (pathname === '/') {
        const sections = navigation
          .filter(item => item.anchor)
          .map(item => item.anchor!.substring(1));

        const currentSection = sections.find(section => {
          const element = document.getElementById(section);
          if (element) {
            const rect = element.getBoundingClientRect();
            return rect.top <= 120 && rect.bottom >= 120;
          }
          return false;
        });

        if (currentSection) {
          setActiveSection(currentSection);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Call once to set initial state
    return () => window.removeEventListener('scroll', handleScroll);
  }, [pathname]);

  const handleNavClick = (href: string, anchor?: string) => {
    // Close mobile menu and dropdowns
    setIsMobileMenuOpen(false);
    setOpenDropdown(null);

    if (anchor) {
      // Handle anchor navigation
      if (pathname !== '/') {
        // Navigate to home page first, then scroll to section
        router.push('/');
        setTimeout(() => {
          scrollToSection(anchor.substring(1));
        }, 100);
      } else {
        // Already on home page, just scroll
        scrollToSection(anchor.substring(1));
      }
    } else {
      // Handle regular page navigation
      router.push(href);
    }
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 100;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  const handleDropdownEnter = (itemName: string) => {
    if (dropdownTimeoutRef.current) {
      clearTimeout(dropdownTimeoutRef.current);
    }
    setOpenDropdown(itemName);
  };

  const handleDropdownLeave = () => {
    dropdownTimeoutRef.current = setTimeout(() => {
      setOpenDropdown(null);
    }, 150);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdown(null);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled
      ? 'bg-background/95 backdrop-blur-md shadow-lg border-b border-foreground/10'
      : 'bg-background/90 backdrop-blur-sm'
      }`}>
      <nav className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link
            href="/"
            className="text-2xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 hover:scale-105 transform"
            onClick={() => {
              if (pathname === '/') {
                scrollToSection('home');
              }
            }}
          >
            UR
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => {
              const isActive = pathname === '/' && item.anchor && activeSection === item.anchor.substring(1);
              const isPageActive = pathname === item.href && !item.anchor;

              return (
                <div key={item.name} className="relative group">
                  {item.dropdown ? (
                    <>
                      <button
                        onClick={() => handleNavClick(item.href, item.anchor)}
                        onMouseEnter={() => handleDropdownEnter(item.name)}
                        onMouseLeave={handleDropdownLeave}
                        className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 flex items-center gap-1 ${
                          isActive || isPageActive
                            ? 'text-accent'
                            : 'text-foreground hover:text-accent'
                        }`}
                      >
                        {item.name}
                        <ChevronDown size={14} className={`transition-transform duration-200 ${
                          openDropdown === item.name ? 'rotate-180' : ''
                        }`} />
                        <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${
                          isActive || isPageActive ? 'w-full' : 'w-0 group-hover:w-full'
                        }`} />
                      </button>

                      {/* Dropdown Menu */}
                      <div
                        className={`absolute top-full left-0 mt-2 w-48 bg-background/95 backdrop-blur-md border border-foreground/10 rounded-lg shadow-lg transition-all duration-200 ${
                          openDropdown === item.name ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                        }`}
                        onMouseEnter={() => handleDropdownEnter(item.name)}
                        onMouseLeave={handleDropdownLeave}
                      >
                        {item.dropdown.map((dropdownItem) => (
                          <button
                            key={dropdownItem.name}
                            onClick={() => handleNavClick(dropdownItem.href, dropdownItem.anchor)}
                            className="block w-full text-left px-4 py-3 text-sm text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg"
                          >
                            {dropdownItem.name}
                          </button>
                        ))}
                      </div>
                    </>
                  ) : (
                    <button
                      onClick={() => handleNavClick(item.href, item.anchor)}
                      className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 ${
                        isActive || isPageActive
                          ? 'text-accent'
                          : 'text-foreground hover:text-accent'
                      }`}
                    >
                      {item.name}
                      <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${
                        isActive || isPageActive ? 'w-full' : 'w-0 group-hover:w-full'
                      }`} />
                    </button>
                  )}
                </div>
              );
            })}
          </div>

          {/* Theme Toggle & Mobile Menu */}
          <div className="flex items-center gap-2">
            <ThemeToggle />

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden text-foreground hover:text-accent hover:bg-foreground/10"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle menu"
            >
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`md:hidden transition-all duration-300 overflow-hidden ${
          isMobileMenuOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="py-4 space-y-2 border-t border-foreground/10 mt-4">
            {navigation.map((item) => {
              const isActive = pathname === '/' && item.anchor && activeSection === item.anchor.substring(1);
              const isPageActive = pathname === item.href && !item.anchor;

              return (
                <div key={item.name}>
                  <button
                    onClick={() => handleNavClick(item.href, item.anchor)}
                    className={`block w-full text-left px-4 py-3 text-sm font-semibold transition-colors duration-300 rounded-lg ${
                      isActive || isPageActive
                        ? 'text-accent bg-foreground/10'
                        : 'text-foreground hover:text-accent hover:bg-foreground/5'
                    }`}
                  >
                    {item.name}
                  </button>

                  {/* Mobile Dropdown Items */}
                  {item.dropdown && (
                    <div className="ml-4 mt-1 space-y-1">
                      {item.dropdown.map((dropdownItem) => (
                        <button
                          key={dropdownItem.name}
                          onClick={() => handleNavClick(dropdownItem.href, dropdownItem.anchor)}
                          className="block w-full text-left px-4 py-2 text-xs text-foreground/80 hover:text-accent hover:bg-foreground/5 transition-colors duration-300 rounded-lg"
                        >
                          {dropdownItem.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Mobile Theme Toggle */}
            <div className="px-4 py-3 border-t border-foreground/10 mt-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-semibold text-foreground">Theme</span>
                <ThemeToggle />
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
}
