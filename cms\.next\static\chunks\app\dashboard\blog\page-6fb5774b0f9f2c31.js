(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7262],{1503:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var a=s(5155),r=s(2115),n=s(6874),l=s.n(n),d=s(6913),i=s(285),c=s(2523),o=s(6126),x=s(6695),h=s(5127),u=s(4838),m=s(4616),f=s(2657),j=s(9946);let p=(0,j.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var v=s(3717),g=s(8564),b=s(7924);let y=(0,j.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var w=s(5623),N=s(2525),A=s(9509);function k(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)(!0),[j,k]=(0,r.useState)(""),[_,C]=(0,r.useState)("all");(0,r.useEffect)(()=>{E()},[]);let E=async()=>{try{let e=await fetch("/api/blog",{credentials:"include"});if(e.ok){let s=await e.json();t(s.data)}}catch(e){console.error("Error fetching posts:",e)}finally{n(!1)}},S=async s=>{if(confirm("Are you sure you want to delete this post?"))try{(await fetch("/api/blog/".concat(s),{method:"DELETE",credentials:"include"})).ok&&t(e.filter(e=>e._id!==s))}catch(e){console.error("Error deleting post:",e)}},L=e.filter(e=>{let t=e.title.toLowerCase().includes(j.toLowerCase())||e.excerpt.toLowerCase().includes(j.toLowerCase()),s="all"===_||e.status===_;return t&&s}),Z=e=>(0,a.jsx)(o.E,{variant:{draft:"secondary",published:"default",archived:"outline"}[e],children:e.charAt(0).toUpperCase()+e.slice(1)}),D=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Blog Posts"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your blog content"})]}),(0,a.jsx)(l(),{href:"/dashboard/blog/new",children:(0,a.jsxs)(i.$,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"New Post"]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)(x.Zp,{children:[(0,a.jsxs)(x.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(x.ZB,{className:"text-sm font-medium",children:"Total Posts"}),(0,a.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.length})})]}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsxs)(x.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(x.ZB,{className:"text-sm font-medium",children:"Published"}),(0,a.jsx)(p,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"published"===e.status).length})})]}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsxs)(x.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(x.ZB,{className:"text-sm font-medium",children:"Drafts"}),(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"draft"===e.status).length})})]}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsxs)(x.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(x.ZB,{className:"text-sm font-medium",children:"Featured"}),(0,a.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>e.featured).length})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(c.p,{placeholder:"Search posts...",value:j,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,a.jsxs)("select",{value:_,onChange:e=>C(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"published",children:"Published"}),(0,a.jsx)("option",{value:"draft",children:"Draft"}),(0,a.jsx)("option",{value:"archived",children:"Archived"})]})]}),(0,a.jsx)(x.Zp,{children:(0,a.jsxs)(h.XI,{children:[(0,a.jsx)(h.A0,{children:(0,a.jsxs)(h.Hj,{children:[(0,a.jsx)(h.nd,{children:"Title"}),(0,a.jsx)(h.nd,{children:"Category"}),(0,a.jsx)(h.nd,{children:"Status"}),(0,a.jsx)(h.nd,{children:"Featured"}),(0,a.jsx)(h.nd,{children:"Read Time"}),(0,a.jsx)(h.nd,{children:"Date"}),(0,a.jsx)(h.nd,{className:"w-[70px]",children:"Actions"})]})}),(0,a.jsx)(h.BF,{children:s?(0,a.jsx)(h.Hj,{children:(0,a.jsx)(h.nA,{colSpan:7,className:"text-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"})})}):0===L.length?(0,a.jsx)(h.Hj,{children:(0,a.jsx)(h.nA,{colSpan:7,className:"text-center py-8 text-gray-500",children:"No posts found"})}):L.map(e=>(0,a.jsxs)(h.Hj,{children:[(0,a.jsx)(h.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.excerpt})]})}),(0,a.jsx)(h.nA,{children:(0,a.jsx)(o.E,{variant:"outline",children:e.category})}),(0,a.jsx)(h.nA,{children:Z(e.status)}),(0,a.jsx)(h.nA,{children:e.featured&&(0,a.jsx)(g.A,{className:"h-4 w-4 text-yellow-500 fill-current"})}),(0,a.jsx)(h.nA,{children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y,{className:"h-3 w-3 mr-1 text-gray-400"}),e.readTime," min"]})}),(0,a.jsx)(h.nA,{className:"text-sm text-gray-500",children:D(e.publishedAt||e.createdAt)}),(0,a.jsx)(h.nA,{children:(0,a.jsxs)(u.rI,{children:[(0,a.jsx)(u.ty,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(u.SQ,{align:"end",children:[(0,a.jsx)(u._2,{asChild:!0,children:(0,a.jsxs)(l(),{href:"/dashboard/blog/".concat(e._id),children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,a.jsx)(u._2,{asChild:!0,children:(0,a.jsxs)("a",{href:"".concat(A.env.NEXT_PUBLIC_FRONTEND_URL||"http://localhost:3000","/blog/").concat(e.slug),target:"_blank",rel:"noopener noreferrer",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"View"]})}),(0,a.jsxs)(u._2,{onClick:()=>S(e._id),className:"text-red-600",children:[(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e._id))})]})})]})})}},4838:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>i,_2:()=>c,rI:()=>l,ty:()=>d});var a=s(5155);s(2115);var r=s(8578),n=s(9434);function l(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"dropdown-menu",...t})}function d(e){let{...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t})}function i(e){let{className:t,sideOffset:s=4,...l}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...l})})}function c(e){let{className:t,inset:s,variant:l="default",...d}=e;return(0,a.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":l,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d})}},5127:(e,t,s)=>{"use strict";s.d(t,{A0:()=>l,BF:()=>d,Hj:()=>i,XI:()=>n,nA:()=>o,nd:()=>c});var a=s(5155);s(2115);var r=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...s})})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(5155);s(2115);var r=s(9708),n=s(2085),l=s(9434);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,asChild:n=!1,...i}=e,c=n?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(d({variant:s}),t),...i})}},6449:(e,t,s)=>{Promise.resolve().then(s.bind(s,1503))}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,5812,8161,6823,8441,1684,7358],()=>t(6449)),_N_E=e.O()}]);