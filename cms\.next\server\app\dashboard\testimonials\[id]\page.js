(()=>{var e={};e.id=7780,e.ids=[7780],e.modules={2273:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o={children:["",{children:["dashboard",{children:["testimonials",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,13689)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\testimonials\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\testimonials\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/testimonials/[id]/page",pathname:"/dashboard/testimonials/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9005:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13689:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\testimonials\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\testimonials\\[id]\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23527:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var a=r(60687),s=r(43210),i=r(29523),n=r(89667),l=r(80013),d=r(44493),o=r(11860),c=r(41862),u=r(9005);let p=(0,r(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var h=r(47342),m=r(30474);function x({value:e,onChange:t,label:r="Image",folder:x="portfolio-cms",className:v="",accept:g="image/*",maxSize:f=5}){let[j,b]=(0,s.useState)(!1),[y,k]=(0,s.useState)(""),[N,w]=(0,s.useState)(""),[A,C]=(0,s.useState)(!1),P=(0,s.useRef)(null),S=async e=>{if(e){if(e.size>1024*f*1024)return void k(`File size must be less than ${f}MB`);if(!e.type.startsWith("image/"))return void k("Please select a valid image file");b(!0),k("");try{let r=new FormData;r.append("file",e),r.append("folder",x);let a=await fetch("/api/upload",{method:"POST",body:r}),s=await a.json();a.ok?t(s.url):k(s.error||"Upload failed")}catch(e){k("Network error occurred")}finally{b(!1)}}},F=()=>{N.trim()&&(t(N.trim()),w(""),C(!1))};return(0,a.jsxs)("div",{className:`space-y-4 ${v}`,children:[(0,a.jsx)(l.J,{children:r}),e?(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.default,{src:e,alt:"Uploaded image",width:300,height:200,className:"w-full h-48 object-cover rounded-lg"}),(0,a.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{t(""),P.current&&(P.current.value="")},children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2 truncate",children:e})]})}):(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&S(t)},onDragOver:e=>{e.preventDefault()},children:j?(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-500 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop an image here, or click to select"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>P.current?.click(),children:[(0,a.jsx)(p,{className:"mr-2 h-4 w-4"}),"Upload File"]}),(0,a.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>C(!A),children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Add URL"]})]})]})}),A&&(0,a.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,a.jsx)(n.p,{placeholder:"Enter image URL...",value:N,onChange:e=>w(e.target.value),onKeyPress:e=>"Enter"===e.key&&F()}),(0,a.jsx)(i.$,{type:"button",onClick:F,children:"Add"}),(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>C(!1),children:"Cancel"})]}),y&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-2",children:y}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Supports: JPG, PNG, GIF, WebP (max ",f,"MB)"]})]})}),(0,a.jsx)("input",{ref:P,type:"file",accept:g,onChange:e=>{let t=e.target.files?.[0];t&&S(t)},className:"hidden"})]})}},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28992:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var a=r(60687),s=r(43210),i=r(16189),n=r(72455),l=r(23527),d=r(29523),o=r(89667),c=r(34729),u=r(80013),p=r(44493),h=r(54987),m=r(96834),x=r(91821),v=r(64398),g=r(41862),f=r(28559),j=r(8819),b=r(13861),y=r(58869),k=r(85814),N=r.n(k);function w(){let e,t=(0,i.useRouter)(),r=(0,i.useParams)(),[k,w]=(0,s.useState)(!1),[A,C]=(0,s.useState)(!0),[P,S]=(0,s.useState)(""),[F,R]=(0,s.useState)(""),[T,E]=(0,s.useState)({name:"",avatar:"",role:"",company:"",content:"",rating:5,email:"",linkedinUrl:"",featured:!1,status:"draft",order:0}),M=(e,t)=>{E(r=>({...r,[e]:t}))},U=async e=>{w(!0),S(""),R("");try{let a=await fetch(`/api/testimonials/${r.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...T,status:e})}),s=await a.json();a.ok?(R(`Testimonial ${"published"===e?"published":"updated"} successfully!`),setTimeout(()=>{t.push("/dashboard/testimonials")},1500)):S(s.error||"Failed to update testimonial")}catch(e){S("Network error occurred")}finally{w(!1)}};return A?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(g.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{children:"Loading testimonial..."})]})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(N(),{href:"/dashboard/testimonials",children:(0,a.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Back to Testimonials"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Testimonial"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Update testimonial information"})]})]})}),P&&(0,a.jsx)(x.Fc,{variant:"destructive",children:(0,a.jsx)(x.TN,{children:P})}),F&&(0,a.jsx)(x.Fc,{children:(0,a.jsx)(x.TN,{children:F})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"xl:col-span-3 space-y-8",children:[(0,a.jsxs)(p.Zp,{children:[(0,a.jsx)(p.aR,{children:(0,a.jsx)(p.ZB,{children:"Person Information"})}),(0,a.jsxs)(p.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"name",children:"Name *"}),(0,a.jsx)(o.p,{id:"name",value:T.name,onChange:e=>M("name",e.target.value),placeholder:"Enter person's name...",required:!0})]}),(0,a.jsx)("div",{children:(0,a.jsx)(l.A,{label:"Photo (Optional)",value:T.avatar,onChange:e=>M("avatar",e),folder:"testimonials"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"role",children:"Role/Position *"}),(0,a.jsx)(o.p,{id:"role",value:T.role,onChange:e=>M("role",e.target.value),placeholder:"e.g., CEO, Marketing Director",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"company",children:"Company"}),(0,a.jsx)(o.p,{id:"company",value:T.company,onChange:e=>M("company",e.target.value),placeholder:"e.g., Tech Corp, Creative Agency"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(o.p,{id:"email",type:"email",value:T.email,onChange:e=>M("email",e.target.value),placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"linkedinUrl",children:"LinkedIn URL"}),(0,a.jsx)(o.p,{id:"linkedinUrl",value:T.linkedinUrl,onChange:e=>M("linkedinUrl",e.target.value),placeholder:"https://linkedin.com/in/username"})]})]})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsx)(p.aR,{children:(0,a.jsx)(p.ZB,{children:"Testimonial Content"})}),(0,a.jsxs)(p.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"content",children:"Testimonial Content *"}),(0,a.jsx)(c.T,{id:"content",value:T.content,onChange:e=>M("content",e.target.value),placeholder:"Enter the testimonial content...",rows:6,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"rating",children:"Rating *"}),(0,a.jsxs)("select",{id:"rating",value:T.rating,onChange:e=>M("rating",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",required:!0,children:[(0,a.jsx)("option",{value:5,children:"5 Stars - Excellent"}),(0,a.jsx)("option",{value:4,children:"4 Stars - Very Good"}),(0,a.jsx)("option",{value:3,children:"3 Stars - Good"}),(0,a.jsx)("option",{value:2,children:"2 Stars - Fair"}),(0,a.jsx)("option",{value:1,children:"1 Star - Poor"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6 xl:sticky xl:top-6 xl:h-fit",children:[(0,a.jsxs)(p.Zp,{children:[(0,a.jsx)(p.aR,{children:(0,a.jsx)(p.ZB,{children:"Settings"})}),(0,a.jsxs)(p.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.d,{id:"featured",checked:T.featured,onCheckedChange:e=>M("featured",e)}),(0,a.jsx)(u.J,{htmlFor:"featured",children:"Featured Testimonial"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(d.$,{onClick:()=>U("draft"),variant:"outline",className:"w-full",disabled:k||!T.name||!T.role||!T.content,children:[k?(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Save as Draft"]}),(0,a.jsxs)(d.$,{onClick:()=>U("published"),className:"w-full",disabled:k||!T.name||!T.role||!T.content,children:[k?(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Update & Publish"]})]})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsx)(p.aR,{children:(0,a.jsx)(p.ZB,{children:"Preview"})}),(0,a.jsx)(p.Wu,{children:(0,a.jsx)("div",{className:"bg-gray-50 p-6 rounded-lg border",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:(e=T.rating,Array.from({length:5},(t,r)=>(0,a.jsx)(v.A,{className:`h-4 w-4 ${r<e?"text-yellow-400 fill-current":"text-gray-300"}`},r)))}),(0,a.jsxs)("blockquote",{className:"text-gray-700 italic",children:['"',T.content||"Testimonial content will appear here...",'"']}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 pt-4 border-t",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden",children:T.avatar?(0,a.jsx)("img",{src:T.avatar,alt:T.name||"Person",className:"w-full h-full object-cover"}):(0,a.jsx)(y.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:T.name||"Person Name"}),(T.role||T.company)&&(0,a.jsx)("div",{className:"text-xs text-gray-500",children:[T.role,T.company].filter(Boolean).join(" at ")})]})]}),T.featured&&(0,a.jsx)("div",{className:"pt-2",children:(0,a.jsx)(m.E,{className:"text-xs bg-yellow-100 text-yellow-800",children:"Featured"})})]})})})]})]})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32334:(e,t,r)=>{Promise.resolve().then(r.bind(r,28992))},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(60687);r(43210);var s=r(4780);function i({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},46246:(e,t,r)=>{Promise.resolve().then(r.bind(r,13689))},47342:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},54987:(e,t,r)=>{"use strict";r.d(t,{d:()=>n});var a=r(60687);r(43210);var s=r(83680),i=r(4780);function n({className:e,...t}){return(0,a.jsx)(s.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var a=r(43210),s=r(14163),i=r(60687),n=a.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var a=r(60687);r(43210);var s=r(78148),i=r(4780);function n({className:e,...t}){return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},83680:(e,t,r)=>{"use strict";r.d(t,{bL:()=>y,zi:()=>k});var a=r(43210),s=r(70569),i=r(98599),n=r(11273),l=r(65551),d=r(18853),o=r(14163),c=r(60687),u="Switch",[p,h]=(0,n.A)(u),[m,x]=p(u),v=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:n,checked:d,defaultChecked:p,required:h,disabled:x,value:v="on",onCheckedChange:g,form:f,...y}=e,[k,N]=a.useState(null),w=(0,i.s)(t,e=>N(e)),A=a.useRef(!1),C=!k||f||!!k.closest("form"),[P,S]=(0,l.i)({prop:d,defaultProp:p??!1,onChange:g,caller:u});return(0,c.jsxs)(m,{scope:r,checked:P,disabled:x,children:[(0,c.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":P,"aria-required":h,"data-state":b(P),"data-disabled":x?"":void 0,disabled:x,value:v,...y,ref:w,onClick:(0,s.m)(e.onClick,e=>{S(e=>!e),C&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),C&&(0,c.jsx)(j,{control:k,bubbles:!A.current,name:n,value:v,checked:P,required:h,disabled:x,form:f,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var g="SwitchThumb",f=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,s=x(g,r);return(0,c.jsx)(o.sG.span,{"data-state":b(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t})});f.displayName=g;var j=a.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:s=!0,...n},l)=>{let o=a.useRef(null),u=(0,i.s)(o,l),p=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r),h=(0,d.X)(t);return a.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let a=new Event("click",{bubbles:s});t.call(e,r),e.dispatchEvent(a)}},[p,r,s]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...n,tabIndex:-1,ref:u,style:{...n.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var y=v,k=f},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>d});var a=r(60687);r(43210);var s=r(24224),i=r(4780);let n=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...r})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(60687);r(43210);var s=r(8730),i=r(24224),n=r(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...i}){let d=r?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,1771,1658,9365,4758,474,9638],()=>r(2273));module.exports=a})();