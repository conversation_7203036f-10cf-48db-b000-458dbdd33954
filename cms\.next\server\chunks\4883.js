"use strict";exports.id=4883,exports.ids=[4883],exports.modules={63143:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},88233:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},92930:(e,r,t)=>{t.d(r,{UC:()=>rr,q7:()=>rt,ZL:()=>re,bL:()=>e9,l9:()=>e7});var n=t(43210),o=t(70569),a=t(98599),l=t(11273),u=t(65551),i=t(14163),c=t(8730),s=t(60687);function d(e){let r=e+"CollectionProvider",[t,o]=(0,l.A)(r),[u,i]=t(r,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:r,children:t}=e,o=n.useRef(null),a=n.useRef(new Map).current;return(0,s.jsx)(u,{scope:r,itemMap:a,collectionRef:o,children:t})};d.displayName=r;let f=e+"CollectionSlot",p=(0,c.TL)(f),m=n.forwardRef((e,r)=>{let{scope:t,children:n}=e,o=i(f,t),l=(0,a.s)(r,o.collectionRef);return(0,s.jsx)(p,{ref:l,children:n})});m.displayName=f;let v=e+"CollectionItemSlot",h="data-radix-collection-item",w=(0,c.TL)(v),g=n.forwardRef((e,r)=>{let{scope:t,children:o,...l}=e,u=n.useRef(null),c=(0,a.s)(r,u),d=i(v,t);return n.useEffect(()=>(d.itemMap.set(u,{ref:u,...l}),()=>void d.itemMap.delete(u))),(0,s.jsx)(w,{...{[h]:""},ref:c,children:o})});return g.displayName=v,[{Provider:d,Slot:m,ItemSlot:g},function(r){let t=i(e+"CollectionConsumer",r);return n.useCallback(()=>{let e=t.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(t.itemMap.values()).sort((e,t)=>r.indexOf(e.ref.current)-r.indexOf(t.ref.current))},[t.collectionRef,t.itemMap])},o]}var f=new WeakMap;function p(e,r){if("at"in Array.prototype)return Array.prototype.at.call(e,r);let t=function(e,r){let t=e.length,n=m(r),o=n>=0?n:t+n;return o<0||o>=t?-1:o}(e,r);return -1===t?void 0:e[t]}function m(e){return e!=e||0===e?0:Math.trunc(e)}var v=n.createContext(void 0);function h(e){let r=n.useContext(v);return e||r||"ltr"}var w=t(31355),g=t(1359),x=t(32547),y=t(96963),M=t(55509),b=t(25028),C=t(46059),R=t(13495),j="rovingFocusGroup.onEntryFocus",k={bubbles:!1,cancelable:!0},D="RovingFocusGroup",[_,I,A]=d(D),[P,T]=(0,l.A)(D,[A]),[E,S]=P(D),F=n.forwardRef((e,r)=>(0,s.jsx)(_.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(_.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(L,{...e,ref:r})})}));F.displayName=D;var L=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:l,loop:c=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:v,preventScrollOnEntryFocus:w=!1,...g}=e,x=n.useRef(null),y=(0,a.s)(r,x),M=h(d),[b,C]=(0,u.i)({prop:f,defaultProp:p??null,onChange:m,caller:D}),[_,A]=n.useState(!1),P=(0,R.c)(v),T=I(t),S=n.useRef(!1),[F,L]=n.useState(0);return n.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(j,P),()=>e.removeEventListener(j,P)},[P]),(0,s.jsx)(E,{scope:t,orientation:l,dir:M,loop:c,currentTabStopId:b,onItemFocus:n.useCallback(e=>C(e),[C]),onItemShiftTab:n.useCallback(()=>A(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,s.jsx)(i.sG.div,{tabIndex:_||0===F?-1:0,"data-orientation":l,...g,ref:y,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!S.current;if(e.target===e.currentTarget&&r&&!_){let r=new CustomEvent(j,k);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=T().filter(e=>e.focusable);G([e.find(e=>e.active),e.find(e=>e.id===b),...e].filter(Boolean).map(e=>e.ref.current),w)}}S.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>A(!1))})})}),N="RovingFocusGroupItem",O=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:l=!1,tabStopId:u,children:c,...d}=e,f=(0,y.B)(),p=u||f,m=S(N,t),v=m.currentTabStopId===p,h=I(t),{onFocusableItemAdd:w,onFocusableItemRemove:g,currentTabStopId:x}=m;return n.useEffect(()=>{if(a)return w(),()=>g()},[a,w,g]),(0,s.jsx)(_.ItemSlot,{scope:t,id:p,focusable:a,active:l,children:(0,s.jsx)(i.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...d,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return K[o]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let n=t.indexOf(e.currentTarget);t=m.loop?function(e,r){return e.map((t,n)=>e[(r+n)%e.length])}(t,n+1):t.slice(n+1)}setTimeout(()=>G(t))}}),children:"function"==typeof c?c({isCurrentTabStop:v,hasTabStop:null!=x}):c})})});O.displayName=N;var K={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function G(e,r=!1){let t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var B=t(63376),U=t(42247),V=["Enter"," "],q=["ArrowUp","PageDown","End"],z=["ArrowDown","PageUp","Home",...q],X={ltr:[...V,"ArrowRight"],rtl:[...V,"ArrowLeft"]},H={ltr:["ArrowLeft"],rtl:["ArrowRight"]},W="Menu",[Z,Y,$]=d(W),[J,Q]=(0,l.A)(W,[$,M.Bk,T]),ee=(0,M.Bk)(),er=T(),[et,en]=J(W),[eo,ea]=J(W),el=e=>{let{__scopeMenu:r,open:t=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=ee(r),[c,d]=n.useState(null),f=n.useRef(!1),p=(0,R.c)(l),m=h(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,s.jsx)(M.bL,{...i,children:(0,s.jsx)(et,{scope:r,open:t,onOpenChange:p,content:c,onContentChange:d,children:(0,s.jsx)(eo,{scope:r,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:u,children:o})})})};el.displayName=W;var eu=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=ee(t);return(0,s.jsx)(M.Mz,{...o,...n,ref:r})});eu.displayName="MenuAnchor";var ei="MenuPortal",[ec,es]=J(ei,{forceMount:void 0}),ed=e=>{let{__scopeMenu:r,forceMount:t,children:n,container:o}=e,a=en(ei,r);return(0,s.jsx)(ec,{scope:r,forceMount:t,children:(0,s.jsx)(C.C,{present:t||a.open,children:(0,s.jsx)(b.Z,{asChild:!0,container:o,children:n})})})};ed.displayName=ei;var ef="MenuContent",[ep,em]=J(ef),ev=n.forwardRef((e,r)=>{let t=es(ef,e.__scopeMenu),{forceMount:n=t.forceMount,...o}=e,a=en(ef,e.__scopeMenu),l=ea(ef,e.__scopeMenu);return(0,s.jsx)(Z.Provider,{scope:e.__scopeMenu,children:(0,s.jsx)(C.C,{present:n||a.open,children:(0,s.jsx)(Z.Slot,{scope:e.__scopeMenu,children:l.modal?(0,s.jsx)(eh,{...o,ref:r}):(0,s.jsx)(ew,{...o,ref:r})})})})}),eh=n.forwardRef((e,r)=>{let t=en(ef,e.__scopeMenu),l=n.useRef(null),u=(0,a.s)(r,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,B.Eq)(e)},[]),(0,s.jsx)(ex,{...e,ref:u,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),ew=n.forwardRef((e,r)=>{let t=en(ef,e.__scopeMenu);return(0,s.jsx)(ex,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),eg=(0,c.TL)("MenuContent.ScrollLock"),ex=n.forwardRef((e,r)=>{let{__scopeMenu:t,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:c,disableOutsidePointerEvents:d,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:h,onDismiss:y,disableOutsideScroll:b,...C}=e,R=en(ef,t),j=ea(ef,t),k=ee(t),D=er(t),_=Y(t),[I,A]=n.useState(null),P=n.useRef(null),T=(0,a.s)(r,P,R.onContentChange),E=n.useRef(0),S=n.useRef(""),L=n.useRef(0),N=n.useRef(null),O=n.useRef("right"),K=n.useRef(0),G=b?U.A:n.Fragment,B=e=>{let r=S.current+e,t=_().filter(e=>!e.disabled),n=document.activeElement,o=t.find(e=>e.ref.current===n)?.textValue,a=function(e,r,t){var n;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=t?e.indexOf(t):-1,l=(n=Math.max(a,0),e.map((r,t)=>e[(n+t)%e.length]));1===o.length&&(l=l.filter(e=>e!==t));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==t?u:void 0}(t.map(e=>e.textValue),r,o),l=t.find(e=>e.textValue===a)?.ref.current;!function e(r){S.current=r,window.clearTimeout(E.current),""!==r&&(E.current=window.setTimeout(()=>e(""),1e3))}(r),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(E.current),[]),(0,g.Oh)();let V=n.useCallback(e=>O.current===N.current?.side&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,c=l.y,s=u.x,d=u.y;c>n!=d>n&&t<(s-i)*(n-c)/(d-c)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,N.current?.area),[]);return(0,s.jsx)(ep,{scope:t,searchRef:S,onItemEnter:n.useCallback(e=>{V(e)&&e.preventDefault()},[V]),onItemLeave:n.useCallback(e=>{V(e)||(P.current?.focus(),A(null))},[V]),onTriggerLeave:n.useCallback(e=>{V(e)&&e.preventDefault()},[V]),pointerGraceTimerRef:L,onPointerGraceIntentChange:n.useCallback(e=>{N.current=e},[]),children:(0,s.jsx)(G,{...b?{as:eg,allowPinchZoom:!0}:void 0,children:(0,s.jsx)(x.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{e.preventDefault(),P.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,s.jsx)(w.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:h,onDismiss:y,children:(0,s.jsx)(F,{asChild:!0,...D,dir:j.dir,orientation:"vertical",loop:l,currentTabStopId:I,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(f,e=>{j.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,s.jsx)(M.UC,{role:"menu","aria-orientation":"vertical","data-state":ez(R.open),"data-radix-menu-content":"",dir:j.dir,...k,...C,ref:T,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&B(e.key));let o=P.current;if(e.target!==o||!z.includes(e.key))return;e.preventDefault();let a=_().filter(e=>!e.disabled).map(e=>e.ref.current);q.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(E.current),S.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eW(e=>{let r=e.target,t=K.current!==e.clientX;e.currentTarget.contains(r)&&t&&(O.current=e.clientX>K.current?"right":"left",K.current=e.clientX)}))})})})})})})});ev.displayName=ef;var ey=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,s.jsx)(i.sG.div,{role:"group",...n,ref:r})});ey.displayName="MenuGroup";var eM=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,s.jsx)(i.sG.div,{...n,ref:r})});eM.displayName="MenuLabel";var eb="MenuItem",eC="menu.itemSelect",eR=n.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:l,...u}=e,c=n.useRef(null),d=ea(eb,e.__scopeMenu),f=em(eb,e.__scopeMenu),p=(0,a.s)(r,c),m=n.useRef(!1);return(0,s.jsx)(ej,{...u,ref:p,disabled:t,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!t&&e){let r=new CustomEvent(eC,{bubbles:!0,cancelable:!0});e.addEventListener(eC,e=>l?.(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?m.current=!1:d.onClose()}}),onPointerDown:r=>{e.onPointerDown?.(r),m.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{m.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==f.searchRef.current;t||r&&" "===e.key||V.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eR.displayName=eb;var ej=n.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:l=!1,textValue:u,...c}=e,d=em(eb,t),f=er(t),p=n.useRef(null),m=(0,a.s)(r,p),[v,h]=n.useState(!1),[w,g]=n.useState("");return n.useEffect(()=>{let e=p.current;e&&g((e.textContent??"").trim())},[c.children]),(0,s.jsx)(Z.ItemSlot,{scope:t,disabled:l,textValue:u??w,children:(0,s.jsx)(O,{asChild:!0,...f,focusable:!l,children:(0,s.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...c,ref:m,onPointerMove:(0,o.m)(e.onPointerMove,eW(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eW(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),ek=n.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:n,...a}=e;return(0,s.jsx)(eS,{scope:e.__scopeMenu,checked:t,children:(0,s.jsx)(eR,{role:"menuitemcheckbox","aria-checked":eX(t)?"mixed":t,...a,ref:r,"data-state":eH(t),onSelect:(0,o.m)(a.onSelect,()=>n?.(!!eX(t)||!t),{checkForDefaultPrevented:!1})})})});ek.displayName="MenuCheckboxItem";var eD="MenuRadioGroup",[e_,eI]=J(eD,{value:void 0,onValueChange:()=>{}}),eA=n.forwardRef((e,r)=>{let{value:t,onValueChange:n,...o}=e,a=(0,R.c)(n);return(0,s.jsx)(e_,{scope:e.__scopeMenu,value:t,onValueChange:a,children:(0,s.jsx)(ey,{...o,ref:r})})});eA.displayName=eD;var eP="MenuRadioItem",eT=n.forwardRef((e,r)=>{let{value:t,...n}=e,a=eI(eP,e.__scopeMenu),l=t===a.value;return(0,s.jsx)(eS,{scope:e.__scopeMenu,checked:l,children:(0,s.jsx)(eR,{role:"menuitemradio","aria-checked":l,...n,ref:r,"data-state":eH(l),onSelect:(0,o.m)(n.onSelect,()=>a.onValueChange?.(t),{checkForDefaultPrevented:!1})})})});eT.displayName=eP;var eE="MenuItemIndicator",[eS,eF]=J(eE,{checked:!1}),eL=n.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:n,...o}=e,a=eF(eE,t);return(0,s.jsx)(C.C,{present:n||eX(a.checked)||!0===a.checked,children:(0,s.jsx)(i.sG.span,{...o,ref:r,"data-state":eH(a.checked)})})});eL.displayName=eE;var eN=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,s.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:r})});eN.displayName="MenuSeparator";var eO=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=ee(t);return(0,s.jsx)(M.i3,{...o,...n,ref:r})});eO.displayName="MenuArrow";var[eK,eG]=J("MenuSub"),eB="MenuSubTrigger",eU=n.forwardRef((e,r)=>{let t=en(eB,e.__scopeMenu),l=ea(eB,e.__scopeMenu),u=eG(eB,e.__scopeMenu),i=em(eB,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:f}=i,p={__scopeMenu:e.__scopeMenu},m=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>m,[m]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),f(null)}},[d,f]),(0,s.jsx)(eu,{asChild:!0,...p,children:(0,s.jsx)(ej,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":u.contentId,"data-state":ez(t.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{e.onClick?.(r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eW(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||t.open||c.current||(i.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{t.onOpenChange(!0),m()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eW(e=>{m();let r=t.content?.getBoundingClientRect();if(r){let n=t.content?.dataset.side,o="right"===n,a=r[o?"left":"right"],l=r[o?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:r.top},{x:l,y:r.top},{x:l,y:r.bottom},{x:a,y:r.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let n=""!==i.searchRef.current;e.disabled||n&&" "===r.key||X[l.dir].includes(r.key)&&(t.onOpenChange(!0),t.content?.focus(),r.preventDefault())})})})});eU.displayName=eB;var eV="MenuSubContent",eq=n.forwardRef((e,r)=>{let t=es(ef,e.__scopeMenu),{forceMount:l=t.forceMount,...u}=e,i=en(ef,e.__scopeMenu),c=ea(ef,e.__scopeMenu),d=eG(eV,e.__scopeMenu),f=n.useRef(null),p=(0,a.s)(r,f);return(0,s.jsx)(Z.Provider,{scope:e.__scopeMenu,children:(0,s.jsx)(C.C,{present:l||i.open,children:(0,s.jsx)(Z.Slot,{scope:e.__scopeMenu,children:(0,s.jsx)(ex,{id:d.contentId,"aria-labelledby":d.triggerId,...u,ref:p,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=H[c.dir].includes(e.key);r&&t&&(i.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function ez(e){return e?"open":"closed"}function eX(e){return"indeterminate"===e}function eH(e){return eX(e)?"indeterminate":e?"checked":"unchecked"}function eW(e){return r=>"mouse"===r.pointerType?e(r):void 0}eq.displayName=eV;var eZ="DropdownMenu",[eY,e$]=(0,l.A)(eZ,[Q]),eJ=Q(),[eQ,e0]=eY(eZ),e1=e=>{let{__scopeDropdownMenu:r,children:t,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:c=!0}=e,d=eJ(r),f=n.useRef(null),[p,m]=(0,u.i)({prop:a,defaultProp:l??!1,onChange:i,caller:eZ});return(0,s.jsx)(eQ,{scope:r,triggerId:(0,y.B)(),triggerRef:f,contentId:(0,y.B)(),open:p,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),modal:c,children:(0,s.jsx)(el,{...d,open:p,onOpenChange:m,dir:o,modal:c,children:t})})};e1.displayName=eZ;var e2="DropdownMenuTrigger",e3=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:n=!1,...l}=e,u=e0(e2,t),c=eJ(t);return(0,s.jsx)(eu,{asChild:!0,...c,children:(0,s.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e3.displayName=e2;var e6=e=>{let{__scopeDropdownMenu:r,...t}=e,n=eJ(r);return(0,s.jsx)(ed,{...n,...t})};e6.displayName="DropdownMenuPortal";var e8="DropdownMenuContent",e4=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,l=e0(e8,t),u=eJ(t),i=n.useRef(!1);return(0,s.jsx)(ev,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{i.current||l.triggerRef.current?.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!l.modal||n)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e4.displayName=e8,n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(ey,{...o,...n,ref:r})}).displayName="DropdownMenuGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eM,{...o,...n,ref:r})}).displayName="DropdownMenuLabel";var e5=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eR,{...o,...n,ref:r})});e5.displayName="DropdownMenuItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(ek,{...o,...n,ref:r})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eA,{...o,...n,ref:r})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eT,{...o,...n,ref:r})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eL,{...o,...n,ref:r})}).displayName="DropdownMenuItemIndicator",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eN,{...o,...n,ref:r})}).displayName="DropdownMenuSeparator",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eO,{...o,...n,ref:r})}).displayName="DropdownMenuArrow",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eU,{...o,...n,ref:r})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eJ(t);return(0,s.jsx)(eq,{...o,...n,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e9=e1,e7=e3,re=e6,rr=e4,rt=e5},93661:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},99270:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};