(()=>{var e={};e.id=8779,e.ids=[8779],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3743:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\reels\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\reels\\[id]\\page.tsx","default")},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9293:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var s=r(60687),a=r(43210),i=r(16189),d=r(72455),n=r(29523),l=r(89667),o=r(34729),c=r(80013),u=r(44493),p=r(54987),h=r(96834),x=r(91821),m=r(66232),v=r(84113),f=r(97840),b=r(41862),g=r(28559),j=r(8819),y=r(13861),w=r(85814),k=r.n(w);function N(){let e,t=(0,i.useRouter)(),r=(0,i.useParams)(),[w,N]=(0,a.useState)(!1),[A,P]=(0,a.useState)(!0),[C,S]=(0,a.useState)(""),[T,E]=(0,a.useState)(""),[R,_]=(0,a.useState)({id:"",title:"",description:"",platform:"instagram",embedUrl:"",featured:!1,status:"draft",order:0}),D=(e,t)=>{_(r=>({...r,[e]:t}))},U=e=>`https://www.youtube.com/embed/${e}`,z=async e=>{N(!0),S(""),E("");try{let s=R.embedUrl||U(R.id),a=await fetch(`/api/reels/${r.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...R,embedUrl:s,platform:"youtube",status:e})}),i=await a.json();a.ok?(E(`Short ${"published"===e?"published":"updated"} successfully!`),setTimeout(()=>{t.push("/dashboard/reels")},1500)):S(i.error||"Failed to update short")}catch(e){S("Network error occurred")}finally{N(!1)}};return A?(0,s.jsx)(d.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(b.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{children:"Loading short..."})]})})}):(0,s.jsx)(d.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(k(),{href:"/dashboard/reels",children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Back to Shorts"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit YouTube Short"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Update YouTube Short information"})]})]})}),C&&(0,s.jsx)(x.Fc,{variant:"destructive",children:(0,s.jsx)(x.TN,{children:C})}),T&&(0,s.jsx)(x.Fc,{children:(0,s.jsx)(x.TN,{children:T})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,s.jsx)("div",{className:"xl:col-span-3 space-y-8",children:(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Short Information"})}),(0,s.jsxs)(u.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"id",children:"YouTube Video ID *"}),(0,s.jsx)(l.p,{id:"id",value:R.id,onChange:e=>D("id",e.target.value),placeholder:"e.g., dQw4w9WgXcQ",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"YouTube video ID from the Shorts URL"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"title",children:"Title *"}),(0,s.jsx)(l.p,{id:"title",value:R.title,onChange:e=>D("title",e.target.value),placeholder:"Enter reel title...",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"description",children:"Description"}),(0,s.jsx)(o.T,{id:"description",value:R.description,onChange:e=>D("description",e.target.value),placeholder:"Enter reel description...",rows:4})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"embedUrl",children:"Custom Embed URL (Optional)"}),(0,s.jsx)(l.p,{id:"embedUrl",value:R.embedUrl,onChange:e=>D("embedUrl",e.target.value),placeholder:"Leave empty to auto-generate"}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Auto-generated: ",R.id?U(R.platform,R.id):"Enter reel ID to preview"]})]})]})]})}),(0,s.jsxs)("div",{className:"space-y-6 xl:sticky xl:top-6 xl:h-fit",children:[(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Settings"})}),(0,s.jsxs)(u.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.d,{id:"featured",checked:R.featured,onCheckedChange:e=>D("featured",e)}),(0,s.jsx)(c.J,{htmlFor:"featured",children:"Featured Reel"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(n.$,{onClick:()=>z("draft"),variant:"outline",className:"w-full",disabled:w||!R.title||!R.id,children:[w?(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Save as Draft"]}),(0,s.jsxs)(n.$,{onClick:()=>z("published"),className:"w-full",disabled:w||!R.title||!R.id,children:[w?(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Update & Publish"]})]})]})]}),(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{children:(0,s.jsx)(u.ZB,{children:"Preview"})}),(0,s.jsx)(u.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"aspect-[9/16] max-w-[200px] bg-gray-100 rounded-lg overflow-hidden",children:R.id?(0,s.jsx)("img",{src:(e=R.id,`https://img.youtube.com/vi/${e}/maxresdefault.jpg`),alt:R.title||"YouTube Shorts thumbnail",className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/images/placeholder.svg"}}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,s.jsx)(f.A,{className:"h-12 w-12 text-gray-400"})})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-medium text-sm",children:R.title||"Reel Title"}),(0,s.jsxs)(h.E,{variant:"outline",className:"text-xs",children:[(e=>{switch(e){case"instagram":return(0,s.jsx)(m.A,{className:"h-4 w-4"});case"youtube":return(0,s.jsx)(v.A,{className:"h-4 w-4"});default:return(0,s.jsx)(f.A,{className:"h-4 w-4"})}})("youtube"),(0,s.jsx)("span",{className:"ml-1",children:"YouTube Shorts"})]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:R.description||"Reel description will appear here..."}),R.featured&&(0,s.jsx)(h.E,{className:"text-xs bg-yellow-100 text-yellow-800",children:"Featured"}),R.id&&(0,s.jsxs)("p",{className:"text-xs text-gray-400",children:["ID: ",R.id]})]})]})})]})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54987:(e,t,r)=>{"use strict";r.d(t,{d:()=>d});var s=r(60687);r(43210);var a=r(83680),i=r(4780);function d({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},56265:(e,t,r)=>{Promise.resolve().then(r.bind(r,3743))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66232:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var s=r(43210),a=r(14163),i=r(60687),d=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var n=d},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687);r(43210);var a=r(78148),i=r(4780);function d({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},82453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=r(65239),a=r(48088),i=r(88170),d=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let o={children:["",{children:["dashboard",{children:["reels",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3743)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\reels\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\reels\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/reels/[id]/page",pathname:"/dashboard/reels/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},83680:(e,t,r)=>{"use strict";r.d(t,{bL:()=>y,zi:()=>w});var s=r(43210),a=r(70569),i=r(98599),d=r(11273),n=r(65551),l=r(18853),o=r(14163),c=r(60687),u="Switch",[p,h]=(0,d.A)(u),[x,m]=p(u),v=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:d,checked:l,defaultChecked:p,required:h,disabled:m,value:v="on",onCheckedChange:f,form:b,...y}=e,[w,k]=s.useState(null),N=(0,i.s)(t,e=>k(e)),A=s.useRef(!1),P=!w||b||!!w.closest("form"),[C,S]=(0,n.i)({prop:l,defaultProp:p??!1,onChange:f,caller:u});return(0,c.jsxs)(x,{scope:r,checked:C,disabled:m,children:[(0,c.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":C,"aria-required":h,"data-state":j(C),"data-disabled":m?"":void 0,disabled:m,value:v,...y,ref:N,onClick:(0,a.m)(e.onClick,e=>{S(e=>!e),P&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),P&&(0,c.jsx)(g,{control:w,bubbles:!A.current,name:d,value:v,checked:C,required:h,disabled:m,form:b,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var f="SwitchThumb",b=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,a=m(f,r);return(0,c.jsx)(o.sG.span,{"data-state":j(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});b.displayName=f;var g=s.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:a=!0,...d},n)=>{let o=s.useRef(null),u=(0,i.s)(o,n),p=function(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r),h=(0,l.X)(t);return s.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let s=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(s)}},[p,r,a]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...d,tabIndex:-1,ref:u,style:{...d.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var y=v,w=b},84113:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>n,TN:()=>l});var s=r(60687);r(43210);var a=r(24224),i=r(4780);let d=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(d({variant:t}),e),...r})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),i=r(24224),d=r(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...i}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,d.cn)(n({variant:t}),e),...i})}},98121:(e,t,r)=>{Promise.resolve().then(r.bind(r,9293))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,1771,1658,9365,4758,9638],()=>r(82453));module.exports=s})();