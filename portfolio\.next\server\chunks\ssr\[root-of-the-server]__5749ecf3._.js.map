{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/common/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { Moon, Sun } from 'lucide-react';\nimport { useTheme } from 'next-themes';\nimport { Button } from '@/components/ui/button';\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = React.useState(false);\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return (\n      <Button variant=\"ghost\" size=\"icon\" className=\"w-9 h-9\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">Toggle theme</span>\n      </Button>\n    );\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"w-9 h-9 hover:bg-accent hover:text-accent-foreground transition-colors\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem] transition-all\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem] transition-all\" />\n      )}\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;YAAO,WAAU;;8BAC5C,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Menu, X, ChevronDown } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { ThemeToggle } from '@/components/common/ThemeToggle';\n\nconst navigation = [\n  { name: 'Home', href: '/', anchor: '#home' },\n  {\n    name: 'Videos',\n    href: '/',\n    anchor: '#featured-work',\n    dropdown: [\n      { name: 'Featured Videos', href: '/', anchor: '#featured-work' },\n      { name: 'View All Videos', href: '/videos' }\n    ]\n  },\n  { name: 'Clients', href: '/', anchor: '#clients' },\n  {\n    name: 'Shorts',\n    href: '/',\n    anchor: '#shorts',\n    dropdown: [\n      { name: 'Featured Shorts', href: '/', anchor: '#shorts' },\n      { name: 'View All Shorts', href: '/reels' }\n    ]\n  },\n  {\n    name: 'Blog',\n    href: '/',\n    anchor: '#blog',\n    dropdown: [\n      { name: 'Latest Posts', href: '/', anchor: '#blog' },\n      { name: 'View All Posts', href: '/blog' }\n    ]\n  },\n  { name: 'Testimonials', href: '/', anchor: '#testimonials' },\n  { name: 'Contact', href: '/', anchor: '#contact' },\n];\n\nexport default function Header() {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const [openDropdown, setOpenDropdown] = useState<string | null>(null);\n  const dropdownTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n\n      // Only update active section on home page\n      if (pathname === '/') {\n        const sections = navigation\n          .filter(item => item.anchor)\n          .map(item => item.anchor!.substring(1));\n\n        const currentSection = sections.find(section => {\n          const element = document.getElementById(section);\n          if (element) {\n            const rect = element.getBoundingClientRect();\n            return rect.top <= 120 && rect.bottom >= 120;\n          }\n          return false;\n        });\n\n        if (currentSection) {\n          setActiveSection(currentSection);\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    handleScroll(); // Call once to set initial state\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [pathname]);\n\n  const handleNavClick = (href: string, anchor?: string) => {\n    // Close mobile menu and dropdowns\n    setIsMobileMenuOpen(false);\n    setOpenDropdown(null);\n\n    if (anchor) {\n      // Handle anchor navigation\n      if (pathname !== '/') {\n        // Navigate to home page first, then scroll to section\n        router.push('/');\n        setTimeout(() => {\n          scrollToSection(anchor.substring(1));\n        }, 100);\n      } else {\n        // Already on home page, just scroll\n        scrollToSection(anchor.substring(1));\n      }\n    } else {\n      // Handle regular page navigation\n      router.push(href);\n    }\n  };\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const headerOffset = 100;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  const handleDropdownEnter = (itemName: string) => {\n    if (dropdownTimeoutRef.current) {\n      clearTimeout(dropdownTimeoutRef.current);\n    }\n    setOpenDropdown(itemName);\n  };\n\n  const handleDropdownLeave = () => {\n    dropdownTimeoutRef.current = setTimeout(() => {\n      setOpenDropdown(null);\n    }, 150);\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = () => {\n      setOpenDropdown(null);\n    };\n\n    document.addEventListener('click', handleClickOutside);\n    return () => document.removeEventListener('click', handleClickOutside);\n  }, []);\n\n  return (\n    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled\n      ? 'bg-background/95 backdrop-blur-md shadow-lg border-b border-foreground/10'\n      : 'bg-background/90 backdrop-blur-sm'\n      }`}>\n      <nav className=\"container mx-auto px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"text-2xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 hover:scale-105 transform\"\n            onClick={() => {\n              if (pathname === '/') {\n                scrollToSection('home');\n              }\n            }}\n          >\n            UR\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => {\n              const isActive = pathname === '/' && item.anchor && activeSection === item.anchor.substring(1);\n              const isPageActive = pathname === item.href && !item.anchor;\n\n              return (\n                <div key={item.name} className=\"relative group\">\n                  {item.dropdown ? (\n                    <>\n                      <button\n                        onClick={() => handleNavClick(item.href, item.anchor)}\n                        onMouseEnter={() => handleDropdownEnter(item.name)}\n                        onMouseLeave={handleDropdownLeave}\n                        className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 flex items-center gap-1 ${\n                          isActive || isPageActive\n                            ? 'text-accent'\n                            : 'text-foreground hover:text-accent'\n                        }`}\n                      >\n                        {item.name}\n                        <ChevronDown size={14} className={`transition-transform duration-200 ${\n                          openDropdown === item.name ? 'rotate-180' : ''\n                        }`} />\n                        <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${\n                          isActive || isPageActive ? 'w-full' : 'w-0 group-hover:w-full'\n                        }`} />\n                      </button>\n\n                      {/* Dropdown Menu */}\n                      <div\n                        className={`absolute top-full left-0 mt-2 w-48 bg-background/95 backdrop-blur-md border border-foreground/10 rounded-lg shadow-lg transition-all duration-200 ${\n                          openDropdown === item.name ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\n                        }`}\n                        onMouseEnter={() => handleDropdownEnter(item.name)}\n                        onMouseLeave={handleDropdownLeave}\n                      >\n                        {item.dropdown.map((dropdownItem) => (\n                          <button\n                            key={dropdownItem.name}\n                            onClick={() => handleNavClick(dropdownItem.href, dropdownItem.anchor)}\n                            className=\"block w-full text-left px-4 py-3 text-sm text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg\"\n                          >\n                            {dropdownItem.name}\n                          </button>\n                        ))}\n                      </div>\n                    </>\n                  ) : (\n                    <button\n                      onClick={() => handleNavClick(item.href, item.anchor)}\n                      className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 ${\n                        isActive || isPageActive\n                          ? 'text-accent'\n                          : 'text-foreground hover:text-accent'\n                      }`}\n                    >\n                      {item.name}\n                      <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${\n                        isActive || isPageActive ? 'w-full' : 'w-0 group-hover:w-full'\n                      }`} />\n                    </button>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n\n          {/* Theme Toggle & Mobile Menu */}\n          <div className=\"flex items-center gap-2\">\n            <ThemeToggle />\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"md:hidden text-foreground hover:text-accent hover:bg-foreground/10\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              aria-label=\"Toggle menu\"\n            >\n              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div className={`md:hidden transition-all duration-300 overflow-hidden ${\n          isMobileMenuOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'\n        }`}>\n          <div className=\"py-4 space-y-2 border-t border-foreground/10 mt-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === '/' && item.anchor && activeSection === item.anchor.substring(1);\n              const isPageActive = pathname === item.href && !item.anchor;\n\n              return (\n                <div key={item.name}>\n                  <button\n                    onClick={() => handleNavClick(item.href, item.anchor)}\n                    className={`block w-full text-left px-4 py-3 text-sm font-semibold transition-colors duration-300 rounded-lg ${\n                      isActive || isPageActive\n                        ? 'text-accent bg-foreground/10'\n                        : 'text-foreground hover:text-accent hover:bg-foreground/5'\n                    }`}\n                  >\n                    {item.name}\n                  </button>\n\n                  {/* Mobile Dropdown Items */}\n                  {item.dropdown && (\n                    <div className=\"ml-4 mt-1 space-y-1\">\n                      {item.dropdown.map((dropdownItem) => (\n                        <button\n                          key={dropdownItem.name}\n                          onClick={() => handleNavClick(dropdownItem.href, dropdownItem.anchor)}\n                          className=\"block w-full text-left px-4 py-2 text-xs text-foreground/80 hover:text-accent hover:bg-foreground/5 transition-colors duration-300 rounded-lg\"\n                        >\n                          {dropdownItem.name}\n                        </button>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n\n            {/* Mobile Theme Toggle */}\n            <div className=\"px-4 py-3 border-t border-foreground/10 mt-4\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-semibold text-foreground\">Theme</span>\n                <ThemeToggle />\n              </div>\n            </div>\n          </div>\n        </div>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAK,QAAQ;IAAQ;IAC3C;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;YACR;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,QAAQ;YAAiB;YAC/D;gBAAE,MAAM;gBAAmB,MAAM;YAAU;SAC5C;IACH;IACA;QAAE,MAAM;QAAW,MAAM;QAAK,QAAQ;IAAW;IACjD;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;YACR;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,QAAQ;YAAU;YACxD;gBAAE,MAAM;gBAAmB,MAAM;YAAS;SAC3C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;YACR;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,QAAQ;YAAQ;YACnD;gBAAE,MAAM;gBAAkB,MAAM;YAAQ;SACzC;IACH;IACA;QAAE,MAAM;QAAgB,MAAM;QAAK,QAAQ;IAAgB;IAC3D;QAAE,MAAM;QAAW,MAAM;QAAK,QAAQ;IAAW;CAClD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;YAE/B,0CAA0C;YAC1C,IAAI,aAAa,KAAK;gBACpB,MAAM,WAAW,WACd,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,EAC1B,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAE,SAAS,CAAC;gBAEtC,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA;oBACnC,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,IAAI,SAAS;wBACX,MAAM,OAAO,QAAQ,qBAAqB;wBAC1C,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI;oBAC3C;oBACA,OAAO;gBACT;gBAEA,IAAI,gBAAgB;oBAClB,iBAAiB;gBACnB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,gBAAgB,iCAAiC;QACjD,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC,MAAc;QACpC,kCAAkC;QAClC,oBAAoB;QACpB,gBAAgB;QAEhB,IAAI,QAAQ;YACV,2BAA2B;YAC3B,IAAI,aAAa,KAAK;gBACpB,sDAAsD;gBACtD,OAAO,IAAI,CAAC;gBACZ,WAAW;oBACT,gBAAgB,OAAO,SAAS,CAAC;gBACnC,GAAG;YACL,OAAO;gBACL,oCAAoC;gBACpC,gBAAgB,OAAO,SAAS,CAAC;YACnC;QACF,OAAO;YACL,iCAAiC;YACjC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QACA,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB,OAAO,GAAG,WAAW;YACtC,gBAAgB;QAClB,GAAG;IACL;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,gBAAgB;QAClB;QAEA,SAAS,gBAAgB,CAAC,SAAS;QACnC,OAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;IACrD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAO,WAAW,CAAC,4DAA4D,EAAE,aAC9E,8EACA,qCACA;kBACF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,IAAI,aAAa,KAAK;oCACpB,gBAAgB;gCAClB;4BACF;sCACD;;;;;;sCAKD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,OAAO,KAAK,MAAM,IAAI,kBAAkB,KAAK,MAAM,CAAC,SAAS,CAAC;gCAC5F,MAAM,eAAe,aAAa,KAAK,IAAI,IAAI,CAAC,KAAK,MAAM;gCAE3D,qBACE,8OAAC;oCAAoB,WAAU;8CAC5B,KAAK,QAAQ,iBACZ;;0DACE,8OAAC;gDACC,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,MAAM;gDACpD,cAAc,IAAM,oBAAoB,KAAK,IAAI;gDACjD,cAAc;gDACd,WAAW,CAAC,gGAAgG,EAC1G,YAAY,eACR,gBACA,qCACJ;;oDAED,KAAK,IAAI;kEACV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,kCAAkC,EACnE,iBAAiB,KAAK,IAAI,GAAG,eAAe,IAC5C;;;;;;kEACF,8OAAC;wDAAK,WAAW,CAAC,qEAAqE,EACrF,YAAY,eAAe,WAAW,0BACtC;;;;;;;;;;;;0DAIJ,8OAAC;gDACC,WAAW,CAAC,kJAAkJ,EAC5J,iBAAiB,KAAK,IAAI,GAAG,sCAAsC,sCACnE;gDACF,cAAc,IAAM,oBAAoB,KAAK,IAAI;gDACjD,cAAc;0DAEb,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,8OAAC;wDAEC,SAAS,IAAM,eAAe,aAAa,IAAI,EAAE,aAAa,MAAM;wDACpE,WAAU;kEAET,aAAa,IAAI;uDAJb,aAAa,IAAI;;;;;;;;;;;qEAU9B,8OAAC;wCACC,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,MAAM;wCACpD,WAAW,CAAC,wEAAwE,EAClF,YAAY,eACR,gBACA,qCACJ;;4CAED,KAAK,IAAI;0DACV,8OAAC;gDAAK,WAAW,CAAC,qEAAqE,EACrF,YAAY,eAAe,WAAW,0BACtC;;;;;;;;;;;;mCArDE,KAAK,IAAI;;;;;4BA0DvB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2IAAA,CAAA,cAAW;;;;;8CAGZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;oCACpC,cAAW;8CAEV,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAMxD,8OAAC;oBAAI,WAAW,CAAC,sDAAsD,EACrE,mBAAmB,8BAA8B,qBACjD;8BACA,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,OAAO,KAAK,MAAM,IAAI,kBAAkB,KAAK,MAAM,CAAC,SAAS,CAAC;gCAC5F,MAAM,eAAe,aAAa,KAAK,IAAI,IAAI,CAAC,KAAK,MAAM;gCAE3D,qBACE,8OAAC;;sDACC,8OAAC;4CACC,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,MAAM;4CACpD,WAAW,CAAC,iGAAiG,EAC3G,YAAY,eACR,iCACA,2DACJ;sDAED,KAAK,IAAI;;;;;;wCAIX,KAAK,QAAQ,kBACZ,8OAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,8OAAC;oDAEC,SAAS,IAAM,eAAe,aAAa,IAAI,EAAE,aAAa,MAAM;oDACpE,WAAU;8DAET,aAAa,IAAI;mDAJb,aAAa,IAAI;;;;;;;;;;;mCAjBtB,KAAK,IAAI;;;;;4BA4BvB;0CAGA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;sDACxD,8OAAC,2IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { ArrowUp, Mail, Phone, MapPin, Linkedin, Instagram, Youtube, Facebook, Heart } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  const handleNavClick = (href: string) => {\n    const targetId = href.substring(1);\n    const element = document.getElementById(targetId);\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  const navigation = [\n    { name: 'Home', href: '#home' },\n    { name: 'Work', href: '#featured-work' },\n    { name: 'Clients', href: '#clients' },\n    { name: 'Shorts', href: '#shorts' },\n    { name: 'Blog', href: '#blog' },\n    { name: 'Testimonials', href: '#testimonials' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  const socialLinks = [\n    { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },\n    { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },\n    { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },\n    { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },\n  ];\n\n  const contactInfo = [\n    { icon: Mail, text: '<EMAIL>', href: 'mailto:<EMAIL>' },\n    { icon: Phone, text: '+977 ************', href: 'tel:+9779840692118' },\n    { icon: MapPin, text: 'Kathmandu, Nepal', href: '#' },\n  ];\n\n  return (\n    <footer className=\"bg-muted text-foreground relative\">\n      {/* Scroll to Top Button */}\n      <Button\n        onClick={scrollToTop}\n        className=\"absolute -top-6 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n        aria-label=\"Scroll to top\"\n      >\n        <ArrowUp size={20} />\n      </Button>\n\n      <div className=\"container mx-auto px-4 pt-16 pb-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link\n              href=\"#home\"\n              className=\"text-3xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 inline-block mb-4\"\n              onClick={(e) => {\n                e.preventDefault();\n                handleNavClick('#home');\n              }}\n            >\n              Uttam Rimal\n            </Link>\n            <p className=\"text-muted-foreground leading-relaxed mb-6 max-w-md\">\n              Creative Video Editor & Storyteller passionate about transforming footage into compelling narratives.\n              Let&apos;s bring your vision to life through the power of visual storytelling.\n            </p>\n\n            {/* Social Links */}\n            <div className=\"flex gap-4\">\n              {socialLinks.map(({ icon: Icon, href, label }) => (\n                <a\n                  key={label}\n                  href={href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"w-10 h-10 bg-foreground/10 hover:bg-accent text-foreground hover:text-accent-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110\"\n                  aria-label={label}\n                >\n                  <Icon size={18} />\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-heading font-semibold text-accent mb-4\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              {navigation.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      handleNavClick(item.href);\n                    }}\n                    className=\"text-muted-foreground hover:text-accent transition-colors duration-300 text-sm block py-1\"\n                  >\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-heading font-semibold text-accent mb-4\">\n              Get in Touch\n            </h3>\n            <ul className=\"space-y-3\">\n              {contactInfo.map((info, index) => (\n                <li key={index} className=\"flex items-center gap-3\">\n                  <info.icon size={16} className=\"text-accent flex-shrink-0\" />\n                  {info.href !== '#' ? (\n                    <a\n                      href={info.href}\n                      className=\"text-muted-foreground hover:text-accent transition-colors duration-300 text-sm\"\n                    >\n                      {info.text}\n                    </a>\n                  ) : (\n                    <span className=\"text-muted-foreground text-sm\">{info.text}</span>\n                  )}\n                </li>\n              ))}\n            </ul>\n\n            {/* WhatsApp CTA */}\n            <div className=\"mt-6\">\n              <a\n                href=\"https://wa.me/9779840692118\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center gap-2 bg-success hover:bg-success/90 text-success-foreground px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105\"\n              >\n                <Phone size={16} />\n                WhatsApp\n              </a>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-border pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            <div className=\"text-muted-foreground text-sm text-center md:text-left\">\n              <p className=\"flex items-center justify-center md:justify-start gap-1\">\n                © {new Date().getFullYear()} Uttam Rimal.\n              </p>\n            </div>\n\n            <Link\n              href=\"http://ashishkamat.com.np\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-flex items-center gap-1 text-muted-foreground hover:text-accent transition-colors duration-300 text-sm font-medium\"\n            >\n              <span>Made with</span>\n              <Heart size={14} className=\"text-destructive fill-current heartbeat\" />\n              <span>by:</span>\n              <span className=\"font-semibold blink\">Ashish Kamat</span>\n            </Link>\n\n          </div>\n\n          {/* Additional Info */}\n          <div className=\"mt-4 text-center\">\n            <p className=\"text-muted-foreground/60 text-xs\">\n              Professional video editing services • Available for freelance projects • Based in Kathmandu, Nepal\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,KAAK,SAAS,CAAC;QAChC,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAiB;QACvC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAA0C,OAAO;QAAW;QACpF;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAwC,OAAO;QAAY;QACpF;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAwC,OAAO;QAAU;QAChF;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAuC,OAAO;QAAW;KAClF;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,kMAAA,CAAA,OAAI;YAAE,MAAM;YAAqB,MAAM;QAA2B;QAC1E;YAAE,MAAM,oMAAA,CAAA,QAAK;YAAE,MAAM;YAAqB,MAAM;QAAqB;QACrE;YAAE,MAAM,0MAAA,CAAA,SAAM;YAAE,MAAM;YAAoB,MAAM;QAAI;KACrD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;oBAAC,MAAM;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;kDACD;;;;;;kDAGD,8OAAC;wCAAE,WAAU;kDAAsD;;;;;;kDAMnE,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC3C,8OAAC;gDAEC,MAAM;gDACN,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAY;0DAEZ,cAAA,8OAAC;oDAAK,MAAM;;;;;;+CAPP;;;;;;;;;;;;;;;;0CAcb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,SAAS,CAAC;wDACR,EAAE,cAAc;wDAChB,eAAe,KAAK,IAAI;oDAC1B;oDACA,WAAU;8DAET,KAAK,IAAI;;;;;;+CATL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAiBxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,8OAAC;wCAAG,WAAU;kDACX,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;gDAAe,WAAU;;kEACxB,8OAAC,KAAK,IAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAC9B,KAAK,IAAI,KAAK,oBACb,8OAAC;wDACC,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;6EAGZ,8OAAC;wDAAK,WAAU;kEAAiC,KAAK,IAAI;;;;;;;+CAVrD;;;;;;;;;;kDAiBb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;gDAA0D;gDAClE,IAAI,OAAO,WAAW;gDAAG;;;;;;;;;;;;kDAIhC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC;0DAAK;;;;;;0DACN,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC3B,8OAAC;0DAAK;;;;;;0DACN,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAM1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D", "debugId": null}}]}