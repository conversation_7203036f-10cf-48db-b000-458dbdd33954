(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8779],{239:(e,t,r)=>{"use strict";r.d(t,{bL:()=>y,zi:()=>w});var a=r(2115),s=r(5185),i=r(6101),l=r(6081),d=r(5845),n=r(1275),c=r(3655),o=r(5155),u="Switch",[h,x]=(0,l.A)(u),[p,m]=h(u),v=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:n,defaultChecked:h,required:x,disabled:m,value:v="on",onCheckedChange:f,form:b,...y}=e,[w,N]=a.useState(null),k=(0,i.s)(t,e=>N(e)),A=a.useRef(!1),S=!w||b||!!w.closest("form"),[T,E]=(0,d.i)({prop:n,defaultProp:null!=h&&h,onChange:f,caller:u});return(0,o.jsxs)(p,{scope:r,checked:T,disabled:m,children:[(0,o.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":x,"data-state":j(T),"data-disabled":m?"":void 0,disabled:m,value:v,...y,ref:k,onClick:(0,s.m)(e.onClick,e=>{E(e=>!e),S&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),S&&(0,o.jsx)(g,{control:w,bubbles:!A.current,name:l,value:v,checked:T,required:x,disabled:m,form:b,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var f="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,s=m(f,r);return(0,o.jsx)(c.sG.span,{"data-state":j(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t})});b.displayName=f;var g=a.forwardRef((e,t)=>{let{__scopeSwitch:r,control:s,checked:l,bubbles:d=!0,...c}=e,u=a.useRef(null),h=(0,i.s)(u,t),x=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l),p=(0,n.X)(s);return a.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==l&&t){let r=new Event("click",{bubbles:d});t.call(e,l),e.dispatchEvent(r)}},[x,l,d]),(0,o.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:h,style:{...c.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var y=v,w=b},333:(e,t,r)=>{"use strict";r.d(t,{d:()=>l});var a=r(5155);r(2115);var s=r(239),i=r(9434);function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,a.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>d});var a=r(2115),s=r(3655),i=r(5155),l=a.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var d=l},1154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2925:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])},4229:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var a=r(5155);r(2115);var s=r(968),i=r(9434);function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},5079:(e,t,r)=>{Promise.resolve().then(r.bind(r,9113))},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>n});var a=r(5155);r(2115);var s=r(2085),i=r(9434);let l=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(l({variant:r}),t),...s})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},5684:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var a=r(5155);r(2115);var s=r(9708),i=r(2085),l=r(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:r,asChild:i=!1,...n}=e,c=i?s.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(d({variant:r}),t),...n})}},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8539:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(5155);r(2115);var s=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}},9113:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var a=r(5155),s=r(2115),i=r(5695),l=r(6913),d=r(285),n=r(2523),c=r(8539),o=r(5057),u=r(6695),h=r(333),x=r(6126),p=r(5365),m=r(5684),v=r(2925),f=r(5690),b=r(1154),g=r(7550),j=r(4229),y=r(2657),w=r(6874),N=r.n(w);function k(){let e,t=(0,i.useRouter)(),r=(0,i.useParams)(),[w,k]=(0,s.useState)(!1),[A,S]=(0,s.useState)(!0),[T,E]=(0,s.useState)(""),[C,R]=(0,s.useState)(""),[F,U]=(0,s.useState)({id:"",title:"",description:"",platform:"instagram",embedUrl:"",featured:!1,status:"draft",order:0});(0,s.useEffect)(()=>{r.id&&z()},[r.id]);let z=async()=>{try{let e=await fetch("/api/reels/".concat(r.id),{credentials:"include"});if(e.ok){let t=(await e.json()).data;U({id:t.id||"",title:t.title||"",description:t.description||"",platform:t.platform||"instagram",embedUrl:t.embedUrl||"",featured:t.featured||!1,status:t.status||"draft",order:t.order||0})}else E("Failed to fetch reel")}catch(e){E("Network error occurred")}finally{S(!1)}},D=(e,t)=>{U(r=>({...r,[e]:t}))},M=e=>"https://www.youtube.com/embed/".concat(e),P=async e=>{k(!0),E(""),R("");try{let a=F.embedUrl||M(F.id),s=await fetch("/api/reels/".concat(r.id),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...F,embedUrl:a,platform:"youtube",status:e})}),i=await s.json();s.ok?(R("Short ".concat("published"===e?"published":"updated"," successfully!")),setTimeout(()=>{t.push("/dashboard/reels")},1500)):E(i.error||"Failed to update short")}catch(e){E("Network error occurred")}finally{k(!1)}};return A?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(b.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{children:"Loading short..."})]})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(N(),{href:"/dashboard/reels",children:(0,a.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Back to Shorts"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit YouTube Short"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Update YouTube Short information"})]})]})}),T&&(0,a.jsx)(p.Fc,{variant:"destructive",children:(0,a.jsx)(p.TN,{children:T})}),C&&(0,a.jsx)(p.Fc,{children:(0,a.jsx)(p.TN,{children:C})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,a.jsx)("div",{className:"xl:col-span-3 space-y-8",children:(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{children:"Short Information"})}),(0,a.jsxs)(u.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"id",children:"YouTube Video ID *"}),(0,a.jsx)(n.p,{id:"id",value:F.id,onChange:e=>D("id",e.target.value),placeholder:"e.g., dQw4w9WgXcQ",required:!0}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"YouTube video ID from the Shorts URL"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"title",children:"Title *"}),(0,a.jsx)(n.p,{id:"title",value:F.title,onChange:e=>D("title",e.target.value),placeholder:"Enter reel title...",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"description",children:"Description"}),(0,a.jsx)(c.T,{id:"description",value:F.description,onChange:e=>D("description",e.target.value),placeholder:"Enter reel description...",rows:4})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"embedUrl",children:"Custom Embed URL (Optional)"}),(0,a.jsx)(n.p,{id:"embedUrl",value:F.embedUrl,onChange:e=>D("embedUrl",e.target.value),placeholder:"Leave empty to auto-generate"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Auto-generated: ",F.id?M(F.platform,F.id):"Enter reel ID to preview"]})]})]})]})}),(0,a.jsxs)("div",{className:"space-y-6 xl:sticky xl:top-6 xl:h-fit",children:[(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{children:"Settings"})}),(0,a.jsxs)(u.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.d,{id:"featured",checked:F.featured,onCheckedChange:e=>D("featured",e)}),(0,a.jsx)(o.J,{htmlFor:"featured",children:"Featured Reel"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(d.$,{onClick:()=>P("draft"),variant:"outline",className:"w-full",disabled:w||!F.title||!F.id,children:[w?(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Save as Draft"]}),(0,a.jsxs)(d.$,{onClick:()=>P("published"),className:"w-full",disabled:w||!F.title||!F.id,children:[w?(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Update & Publish"]})]})]})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{children:"Preview"})}),(0,a.jsx)(u.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"aspect-[9/16] max-w-[200px] bg-gray-100 rounded-lg overflow-hidden",children:F.id?(0,a.jsx)("img",{src:(e=F.id,"https://img.youtube.com/vi/".concat(e,"/maxresdefault.jpg")),alt:F.title||"YouTube Shorts thumbnail",className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/images/placeholder.svg"}}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(f.A,{className:"h-12 w-12 text-gray-400"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-medium text-sm",children:F.title||"Reel Title"}),(0,a.jsxs)(x.E,{variant:"outline",className:"text-xs",children:[(e=>{switch(e){case"instagram":return(0,a.jsx)(m.A,{className:"h-4 w-4"});case"youtube":return(0,a.jsx)(v.A,{className:"h-4 w-4"});default:return(0,a.jsx)(f.A,{className:"h-4 w-4"})}})("youtube"),(0,a.jsx)("span",{className:"ml-1",children:"YouTube Shorts"})]})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:F.description||"Reel description will appear here..."}),F.featured&&(0,a.jsx)(x.E,{className:"text-xs bg-yellow-100 text-yellow-800",children:"Featured"}),F.id&&(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:["ID: ",F.id]})]})]})})]})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,5812,6823,8441,1684,7358],()=>t(5079)),_N_E=e.O()}]);