(()=>{var e={};e.id=4076,e.ids=[4076],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9005:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18309:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o={children:["",{children:["dashboard",{children:["clients",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63532)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\clients\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\clients\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/clients/[id]/page",pathname:"/dashboard/clients/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},18782:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(60687),a=r(43210),i=r(16189),n=r(72455),l=r(23527),d=r(29523),o=r(89667),c=r(34729),u=r(80013),p=r(44493),h=r(54987),x=r(91821),m=r(28559),v=r(8819),f=r(13861),g=r(85814),b=r.n(g);function j(){let e=(0,i.useRouter)(),t=(0,i.useParams)(),[r,g]=(0,a.useState)(!1),[j,y]=(0,a.useState)(!0),[k,w]=(0,a.useState)(""),[N,C]=(0,a.useState)(""),[A,P]=(0,a.useState)({name:"",logo:"",description:"",website:"",industry:"",projectType:"",featured:!1,status:"draft",order:0}),S=(e,t)=>{P(r=>({...r,[e]:t}))},T=async r=>{g(!0),w(""),C("");try{let s=await fetch(`/api/clients/${t.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...A,status:r})}),a=await s.json();s.ok?(C(`Client ${"published"===r?"published":"updated"} successfully!`),setTimeout(()=>{e.push("/dashboard/clients")},1500)):w(a.error||"Failed to update client")}catch(e){w("Network error occurred")}finally{g(!1)}};return j?(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})})}):(0,s.jsx)(n.A,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(b(),{href:"/dashboard/clients",children:(0,s.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Back to Clients"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Client"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Update client information"})]})]})}),k&&(0,s.jsx)(x.Fc,{variant:"destructive",children:(0,s.jsx)(x.TN,{children:k})}),N&&(0,s.jsx)(x.Fc,{children:(0,s.jsx)(x.TN,{children:N})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Client Information"})}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"name",children:"Client Name *"}),(0,s.jsx)(o.p,{id:"name",value:A.name,onChange:e=>S("name",e.target.value),placeholder:"Enter client name...",required:!0})]}),(0,s.jsx)("div",{children:(0,s.jsx)(l.A,{label:"Client Logo *",value:A.logo,onChange:e=>S("logo",e),folder:"clients"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"description",children:"Description *"}),(0,s.jsx)(c.T,{id:"description",value:A.description,onChange:e=>S("description",e.target.value),placeholder:"Brief description about the client and your work together...",rows:4,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"website",children:"Website"}),(0,s.jsx)(o.p,{id:"website",type:"url",value:A.website,onChange:e=>S("website",e.target.value),placeholder:"https://client-website.com"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"industry",children:"Industry"}),(0,s.jsx)(o.p,{id:"industry",value:A.industry,onChange:e=>S("industry",e.target.value),placeholder:"e.g., Technology, Healthcare, Finance"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"projectType",children:"Project Type"}),(0,s.jsx)(o.p,{id:"projectType",value:A.projectType,onChange:e=>S("projectType",e.target.value),placeholder:"e.g., Web Development, Branding, Marketing"})]})]})]})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Publish"})}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"status",children:"Status"}),(0,s.jsxs)("select",{id:"status",value:A.status,onChange:e=>S("status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,s.jsx)("option",{value:"draft",children:"Draft"}),(0,s.jsx)("option",{value:"published",children:"Published"}),(0,s.jsx)("option",{value:"archived",children:"Archived"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.d,{id:"featured",checked:A.featured,onCheckedChange:e=>S("featured",e)}),(0,s.jsx)(u.J,{htmlFor:"featured",children:"Featured Client"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(d.$,{onClick:()=>T("draft"),variant:"outline",className:"w-full",disabled:r,children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]}),(0,s.jsxs)(d.$,{onClick:()=>T("published"),className:"w-full",disabled:r||!A.name||!A.logo||!A.description,children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Update & Publish"]})]})]})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsx)(p.aR,{children:(0,s.jsx)(p.ZB,{children:"Settings"})}),(0,s.jsx)(p.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:"order",children:"Display Order"}),(0,s.jsx)(o.p,{id:"order",type:"number",value:A.order,onChange:e=>S("order",parseInt(e.target.value)||0),placeholder:"0"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Lower numbers appear first"})]})})]})]})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23527:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(60687),a=r(43210),i=r(29523),n=r(89667),l=r(80013),d=r(44493),o=r(11860),c=r(41862),u=r(9005);let p=(0,r(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var h=r(47342),x=r(30474);function m({value:e,onChange:t,label:r="Image",folder:m="portfolio-cms",className:v="",accept:f="image/*",maxSize:g=5}){let[b,j]=(0,a.useState)(!1),[y,k]=(0,a.useState)(""),[w,N]=(0,a.useState)(""),[C,A]=(0,a.useState)(!1),P=(0,a.useRef)(null),S=async e=>{if(e){if(e.size>1024*g*1024)return void k(`File size must be less than ${g}MB`);if(!e.type.startsWith("image/"))return void k("Please select a valid image file");j(!0),k("");try{let r=new FormData;r.append("file",e),r.append("folder",m);let s=await fetch("/api/upload",{method:"POST",body:r}),a=await s.json();s.ok?t(a.url):k(a.error||"Upload failed")}catch(e){k("Network error occurred")}finally{j(!1)}}},T=()=>{w.trim()&&(t(w.trim()),N(""),A(!1))};return(0,s.jsxs)("div",{className:`space-y-4 ${v}`,children:[(0,s.jsx)(l.J,{children:r}),e?(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x.default,{src:e,alt:"Uploaded image",width:300,height:200,className:"w-full h-48 object-cover rounded-lg"}),(0,s.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{t(""),P.current&&(P.current.value="")},children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2 truncate",children:e})]})}):(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&S(t)},onDragOver:e=>{e.preventDefault()},children:b?(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-500 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop an image here, or click to select"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>P.current?.click(),children:[(0,s.jsx)(p,{className:"mr-2 h-4 w-4"}),"Upload File"]}),(0,s.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>A(!C),children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Add URL"]})]})]})}),C&&(0,s.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,s.jsx)(n.p,{placeholder:"Enter image URL...",value:w,onChange:e=>N(e.target.value),onKeyPress:e=>"Enter"===e.key&&T()}),(0,s.jsx)(i.$,{type:"button",onClick:T,children:"Add"}),(0,s.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>A(!1),children:"Cancel"})]}),y&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-2",children:y}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Supports: JPG, PNG, GIF, WebP (max ",g,"MB)"]})]})}),(0,s.jsx)("input",{ref:P,type:"file",accept:f,onChange:e=>{let t=e.target.files?.[0];t&&S(t)},className:"hidden"})]})}},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42070:(e,t,r)=>{Promise.resolve().then(r.bind(r,18782))},47342:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},54987:(e,t,r)=>{"use strict";r.d(t,{d:()=>n});var s=r(60687);r(43210);var a=r(83680),i=r(4780);function n({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63532:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\clients\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\clients\\[id]\\page.tsx","default")},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var s=r(43210),a=r(14163),i=r(60687),n=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},79022:(e,t,r)=>{Promise.resolve().then(r.bind(r,63532))},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(60687);r(43210);var a=r(78148),i=r(4780);function n({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},83680:(e,t,r)=>{"use strict";r.d(t,{bL:()=>y,zi:()=>k});var s=r(43210),a=r(70569),i=r(98599),n=r(11273),l=r(65551),d=r(18853),o=r(14163),c=r(60687),u="Switch",[p,h]=(0,n.A)(u),[x,m]=p(u),v=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:n,checked:d,defaultChecked:p,required:h,disabled:m,value:v="on",onCheckedChange:f,form:g,...y}=e,[k,w]=s.useState(null),N=(0,i.s)(t,e=>w(e)),C=s.useRef(!1),A=!k||g||!!k.closest("form"),[P,S]=(0,l.i)({prop:d,defaultProp:p??!1,onChange:f,caller:u});return(0,c.jsxs)(x,{scope:r,checked:P,disabled:m,children:[(0,c.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":P,"aria-required":h,"data-state":j(P),"data-disabled":m?"":void 0,disabled:m,value:v,...y,ref:N,onClick:(0,a.m)(e.onClick,e=>{S(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,c.jsx)(b,{control:k,bubbles:!C.current,name:n,value:v,checked:P,required:h,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var f="SwitchThumb",g=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,a=m(f,r);return(0,c.jsx)(o.sG.span,{"data-state":j(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});g.displayName=f;var b=s.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:a=!0,...n},l)=>{let o=s.useRef(null),u=(0,i.s)(o,l),p=function(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r),h=(0,d.X)(t);return s.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let s=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(s)}},[p,r,a]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...n,tabIndex:-1,ref:u,style:{...n.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var y=v,k=g},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>d});var s=r(60687);r(43210);var a=r(24224),i=r(4780);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...r})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,1771,1658,9365,4758,474,9638],()=>r(18309));module.exports=s})();