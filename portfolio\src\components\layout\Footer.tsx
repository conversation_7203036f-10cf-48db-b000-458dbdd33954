'use client';

import Link from 'next/link';
import { ArrowUp, Mail, Phone, MapPin, Linkedin, Instagram, Youtube, Facebook, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Footer() {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleNavClick = (href: string) => {
    const targetId = href.substring(1);
    const element = document.getElementById(targetId);
    if (element) {
      const headerOffset = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  const navigation = [
    { name: 'Home', href: '#home' },
    { name: 'Work', href: '#featured-work' },
    { name: 'Clients', href: '#clients' },
    { name: 'Shorts', href: '#shorts' },
    { name: 'Blog', href: '#blog' },
    { name: 'Testimonials', href: '#testimonials' },
    { name: 'Contact', href: '#contact' },
  ];

  const socialLinks = [
    { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },
    { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },
    { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },
    { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },
  ];

  const contactInfo = [
    { icon: Mail, text: '<EMAIL>', href: 'mailto:<EMAIL>' },
    { icon: Phone, text: '+977 ************', href: 'tel:+9779840692118' },
    { icon: MapPin, text: 'Kathmandu, Nepal', href: '#' },
  ];

  return (
    <footer className="bg-muted text-foreground relative">
      {/* Scroll to Top Button */}
      <Button
        onClick={scrollToTop}
        className="absolute -top-6 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300"
        aria-label="Scroll to top"
      >
        <ArrowUp size={20} />
      </Button>

      <div className="container mx-auto px-4 pt-16 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link
              href="#home"
              className="text-3xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 inline-block mb-4"
              onClick={(e) => {
                e.preventDefault();
                handleNavClick('#home');
              }}
            >
              Uttam Rimal
            </Link>
            <p className="text-muted-foreground leading-relaxed mb-6 max-w-md">
              Creative Video Editor & Storyteller passionate about transforming footage into compelling narratives.
              Let&apos;s bring your vision to life through the power of visual storytelling.
            </p>

            {/* Social Links */}
            <div className="flex gap-4">
              {socialLinks.map(({ icon: Icon, href, label }) => (
                <a
                  key={label}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-foreground/10 hover:bg-accent text-foreground hover:text-accent-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                  aria-label={label}
                >
                  <Icon size={18} />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-heading font-semibold text-accent mb-4">
              Quick Links
            </h3>
            <ul className="space-y-2">
              {navigation.map((item) => (
                <li key={item.name}>
                  <Button
                    variant="ghost"
                    onClick={() => handleNavClick(item.href)}
                    className="text-muted-foreground hover:text-accent transition-colors duration-300 text-sm h-auto p-0 justify-start"
                  >
                    {item.name}
                  </Button>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-heading font-semibold text-accent mb-4">
              Get in Touch
            </h3>
            <ul className="space-y-3">
              {contactInfo.map((info, index) => (
                <li key={index} className="flex items-center gap-3">
                  <info.icon size={16} className="text-accent flex-shrink-0" />
                  {info.href !== '#' ? (
                    <a
                      href={info.href}
                      className="text-muted-foreground hover:text-accent transition-colors duration-300 text-sm"
                    >
                      {info.text}
                    </a>
                  ) : (
                    <span className="text-muted-foreground text-sm">{info.text}</span>
                  )}
                </li>
              ))}
            </ul>

            {/* WhatsApp CTA */}
            <div className="mt-6">
              <a
                href="https://wa.me/9779840692118"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-success hover:bg-success/90 text-success-foreground px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105"
              >
                <Phone size={16} />
                WhatsApp
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-border pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-muted-foreground text-sm text-center md:text-left">
              <p className="flex items-center justify-center md:justify-start gap-1">
                © {new Date().getFullYear()} Uttam Rimal.
              </p>
            </div>

            <Link
              href="http://ashishkamat.com.np"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-1 text-muted-foreground hover:text-accent transition-colors duration-300 text-sm font-medium"
            >
              <span>Made with</span>
              <Heart size={14} className="text-destructive fill-current heartbeat" />
              <span>by:</span>
              <span className="font-semibold blink">Ashish Kamat</span>
            </Link>

          </div>

          {/* Additional Info */}
          <div className="mt-4 text-center">
            <p className="text-muted-foreground/60 text-xs">
              Professional video editing services • Available for freelance projects • Based in Kathmandu, Nepal
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
