"use strict";(()=>{var e={};e.id=3964,e.ids=[3964],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66084:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>A});var s={};t.r(s),t.d(s,{DELETE:()=>v,GET:()=>w,PUT:()=>f});var o=t(96559),n=t(48088),i=t(37719),a=t(32190),d=t(75745),u=t(35325),p=t(12909),l=t(27746),c=t(67912),x=t.n(c);async function w(e,{params:r}){try{await (0,d.A)();let{id:t}=await r,s=await u.A.findOne({$or:[{_id:t},{id:t},{slug:t}]}).lean();if(!s){let r=a.NextResponse.json({error:"Video not found"},{status:404});return(0,l.Sw)(r,e.headers.get("origin")),r}let o=a.NextResponse.json({success:!0,data:s});return(0,l.Sw)(o,e.headers.get("origin")),o}catch(t){console.error("Get video error:",t);let r=a.NextResponse.json({error:"Internal server error"},{status:500});return(0,l.Sw)(r,e.headers.get("origin")),r}}let f=(0,p.oC)(async(e,{params:r})=>{try{await (0,d.A)();let{id:t}=await r,s=await e.json(),o=await u.A.findById(t);if(!o)return a.NextResponse.json({error:"Video not found"},{status:404});if(s.title&&s.title!==o.title){let e=x()(s.title,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g}),r=e,o=1;for(;await u.A.findOne({slug:r,_id:{$ne:t}});)r=`${e}-${o}`,o++;s.slug=r}if(s.id&&s.id!==o.id&&await u.A.findOne({id:s.id,_id:{$ne:t}}))return a.NextResponse.json({error:"Video with this ID already exists"},{status:400});let n=await u.A.findByIdAndUpdate(t,{...s,updatedAt:new Date},{new:!0,runValidators:!0});return a.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Update video error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}),v=(0,p.oC)(async(e,{params:r})=>{try{await (0,d.A)();let{id:e}=await r;if(!await u.A.findById(e))return a.NextResponse.json({error:"Video not found"},{status:404});return await u.A.findByIdAndDelete(e),a.NextResponse.json({success:!0,message:"Video deleted successfully"})}catch(e){return console.error("Delete video error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}),g=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/videos/[id]/route",pathname:"/api/videos/[id]",filename:"route",bundlePath:"app/api/videos/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\videos\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:A,serverHooks:h}=g;function j(){return(0,i.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:A})}},79428:e=>{e.exports=require("buffer")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,7428],()=>t(66084));module.exports=s})();