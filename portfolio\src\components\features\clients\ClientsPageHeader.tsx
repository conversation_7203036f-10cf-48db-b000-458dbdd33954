import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, Users, Handshake } from 'lucide-react';

export default function ClientsPageHeader() {
  return (
    <div className="relative bg-muted text-foreground py-20 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 border border-foreground/20 rounded-full"></div>
        <div className="absolute top-32 right-20 w-16 h-16 border border-foreground/20 rounded-full"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 border border-foreground/20 rounded-full"></div>
        <div className="absolute bottom-32 right-1/3 w-24 h-24 border border-foreground/20 rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <Link
          href="/#clients"
          className="inline-flex items-center gap-2 text-accent hover:text-foreground transition-colors mb-8 group"
        >
          <ArrowLeft size={20} className="group-hover:-translate-x-1 transition-transform duration-300" />
          Back to Portfolio
        </Link>
        
        <div className="max-w-4xl">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-4 bg-foreground/10 backdrop-blur-sm rounded-2xl">
              <Users size={32} className="text-foreground" />
            </div>
            <div className="p-4 bg-foreground/10 backdrop-blur-sm rounded-2xl">
              <Handshake size={32} className="text-foreground" />
            </div>
          </div>

          <h1 className="text-secondary text-4xl md:text-5xl font-heading font-bold mb-4">
            Our Clients
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mb-6">
            Building Success Together
          </p>

          <p className="text-xl text-muted-foreground max-w-2xl">
            Proud to have collaborated with diverse clients across various industries, building lasting partnerships through exceptional video editing services and creative storytelling.
          </p>

          <div className="flex flex-wrap gap-4 mt-8">
            <div className="bg-foreground/10 backdrop-blur-sm px-4 py-2 rounded-full">
              <span className="text-sm font-medium">🎬 Video Editing</span>
            </div>
            <div className="bg-foreground/10 backdrop-blur-sm px-4 py-2 rounded-full">
              <span className="text-sm font-medium">📱 Social Media Content</span>
            </div>
            <div className="bg-foreground/10 backdrop-blur-sm px-4 py-2 rounded-full">
              <span className="text-sm font-medium">🎯 Brand Storytelling</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
