{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/app/blog/%5Bslug%5D/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header Skeleton */}\n      <div className=\"bg-primary text-primary-foreground py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-6 bg-primary-foreground/20 rounded w-32 mb-8\"></div>\n            \n            <div className=\"max-w-4xl\">\n              <div className=\"flex gap-2 mb-4\">\n                <div className=\"h-6 bg-white/20 rounded w-20\"></div>\n                <div className=\"h-6 bg-white/20 rounded w-16\"></div>\n              </div>\n              \n              <div className=\"h-12 bg-white/20 rounded w-3/4 mb-4\"></div>\n              \n              <div className=\"flex gap-6 mb-6\">\n                <div className=\"h-4 bg-white/20 rounded w-24\"></div>\n                <div className=\"h-4 bg-white/20 rounded w-20\"></div>\n                <div className=\"h-4 bg-white/20 rounded w-28\"></div>\n              </div>\n              \n              <div className=\"h-6 bg-white/20 rounded w-full mb-2\"></div>\n              <div className=\"h-6 bg-white/20 rounded w-2/3\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Featured Image Skeleton */}\n      <div className=\"relative aspect-video max-h-96 bg-gray-200 animate-pulse\"></div>\n\n      {/* Content Skeleton */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"flex flex-col lg:flex-row gap-12\">\n            {/* Main Content Skeleton */}\n            <article className=\"flex-1 animate-pulse\">\n              <div className=\"space-y-4 mb-8\">\n                <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n              </div>\n              \n              <div className=\"space-y-4 mb-8\">\n                <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-2/3\"></div>\n              </div>\n\n              {/* Tags Skeleton */}\n              <div className=\"mt-12 pt-8 border-t border-gray-200\">\n                <div className=\"h-6 bg-gray-200 rounded w-16 mb-4\"></div>\n                <div className=\"flex gap-2\">\n                  <div className=\"h-6 bg-gray-200 rounded w-16\"></div>\n                  <div className=\"h-6 bg-gray-200 rounded w-20\"></div>\n                  <div className=\"h-6 bg-gray-200 rounded w-14\"></div>\n                </div>\n              </div>\n\n              {/* Share Skeleton */}\n              <div className=\"mt-8 pt-8 border-t border-gray-200\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"h-6 bg-gray-200 rounded w-32\"></div>\n                  <div className=\"h-10 bg-gray-200 rounded w-20\"></div>\n                </div>\n              </div>\n            </article>\n\n            {/* Sidebar Skeleton */}\n            <aside className=\"lg:w-80 animate-pulse\">\n              <div className=\"sticky top-8 space-y-8\">\n                {/* Author Info Skeleton */}\n                <div className=\"bg-muted p-6 rounded-xl\">\n                  <div className=\"h-6 bg-gray-200 rounded w-32 mb-4\"></div>\n                  <div className=\"flex items-center gap-4 mb-4\">\n                    <div className=\"w-16 h-16 bg-gray-200 rounded-full\"></div>\n                    <div>\n                      <div className=\"h-4 bg-gray-200 rounded w-24 mb-2\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-32\"></div>\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"h-3 bg-gray-200 rounded w-full\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n                  </div>\n                </div>\n\n                {/* Related Posts Skeleton */}\n                <div className=\"bg-muted p-6 rounded-xl\">\n                  <div className=\"h-6 bg-gray-200 rounded w-28 mb-4\"></div>\n                  <div className=\"space-y-4\">\n                    {[1, 2, 3].map((i) => (\n                      <div key={i}>\n                        <div className=\"h-4 bg-gray-200 rounded w-full mb-1\"></div>\n                        <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </aside>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAKnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAGnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;2DAFP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC", "debugId": null}}]}