import Image from 'next/image';
import { type BlogPost } from '@/lib/api';

interface BlogFeaturedImageProps {
  post: BlogPost;
}

export default function BlogFeaturedImage({ post }: BlogFeaturedImageProps) {
  if (!post.thumbnail) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 -mt-8">
      {/* Featured Image Container */}
      <div className="relative aspect-video max-h-[500px] overflow-hidden rounded-xl border shadow-lg bg-card">
        <Image
          src={post.thumbnail}
          alt={post.title}
          fill
          className="object-cover"
          priority
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
        />
      </div>
    </div>
  );
}
