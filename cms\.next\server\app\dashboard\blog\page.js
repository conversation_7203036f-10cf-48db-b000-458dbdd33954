(()=>{var e={};e.id=7262,e.ids=[7262],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>i,BF:()=>d,Hj:()=>o,XI:()=>n,nA:()=>c,nd:()=>l});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function d({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function l({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21342:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>o,_2:()=>l,rI:()=>i,ty:()=>d});var r=s(60687);s(43210);var a=s(92930),n=s(4780);function i({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dropdown-menu",...e})}function d({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...e})}function o({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function l({className:e,inset:t,variant:s="default",...i}){return(0,r.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}},26252:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),d=s(72455),o=s(29523),l=s(89667),c=s(96834),u=s(44493),x=s(6211),h=s(21342),m=s(96474),p=s(13861),f=s(62688);let v=(0,f.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var b=s(63143),j=s(64398),g=s(99270);let y=(0,f.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var w=s(93661),N=s(88233);function A(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[f,A]=(0,a.useState)(""),[k,_]=(0,a.useState)("all"),C=async s=>{if(confirm("Are you sure you want to delete this post?"))try{(await fetch(`/api/blog/${s}`,{method:"DELETE",credentials:"include"})).ok&&t(e.filter(e=>e._id!==s))}catch(e){console.error("Error deleting post:",e)}},P=e.filter(e=>{let t=e.title.toLowerCase().includes(f.toLowerCase())||e.excerpt.toLowerCase().includes(f.toLowerCase()),s="all"===k||e.status===k;return t&&s}),E=e=>(0,r.jsx)(c.E,{variant:{draft:"secondary",published:"default",archived:"outline"}[e],children:e.charAt(0).toUpperCase()+e.slice(1)}),D=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,r.jsx)(d.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Blog Posts"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your blog content"})]}),(0,r.jsx)(i(),{href:"/dashboard/blog/new",children:(0,r.jsxs)(o.$,{children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"New Post"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Total Posts"}),(0,r.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.length})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Published"}),(0,r.jsx)(v,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"published"===e.status).length})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Drafts"}),(0,r.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"draft"===e.status).length})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(u.ZB,{className:"text-sm font-medium",children:"Featured"}),(0,r.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>e.featured).length})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(l.p,{placeholder:"Search posts...",value:f,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("select",{value:k,onChange:e=>_(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"published",children:"Published"}),(0,r.jsx)("option",{value:"draft",children:"Draft"}),(0,r.jsx)("option",{value:"archived",children:"Archived"})]})]}),(0,r.jsx)(u.Zp,{children:(0,r.jsxs)(x.XI,{children:[(0,r.jsx)(x.A0,{children:(0,r.jsxs)(x.Hj,{children:[(0,r.jsx)(x.nd,{children:"Title"}),(0,r.jsx)(x.nd,{children:"Category"}),(0,r.jsx)(x.nd,{children:"Status"}),(0,r.jsx)(x.nd,{children:"Featured"}),(0,r.jsx)(x.nd,{children:"Read Time"}),(0,r.jsx)(x.nd,{children:"Date"}),(0,r.jsx)(x.nd,{className:"w-[70px]",children:"Actions"})]})}),(0,r.jsx)(x.BF,{children:s?(0,r.jsx)(x.Hj,{children:(0,r.jsx)(x.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"})})}):0===P.length?(0,r.jsx)(x.Hj,{children:(0,r.jsx)(x.nA,{colSpan:7,className:"text-center py-8 text-gray-500",children:"No posts found"})}):P.map(e=>(0,r.jsxs)(x.Hj,{children:[(0,r.jsx)(x.nA,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.title}),(0,r.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.excerpt})]})}),(0,r.jsx)(x.nA,{children:(0,r.jsx)(c.E,{variant:"outline",children:e.category})}),(0,r.jsx)(x.nA,{children:E(e.status)}),(0,r.jsx)(x.nA,{children:e.featured&&(0,r.jsx)(j.A,{className:"h-4 w-4 text-yellow-500 fill-current"})}),(0,r.jsx)(x.nA,{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"h-3 w-3 mr-1 text-gray-400"}),e.readTime," min"]})}),(0,r.jsx)(x.nA,{className:"text-sm text-gray-500",children:D(e.publishedAt||e.createdAt)}),(0,r.jsx)(x.nA,{children:(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsx)(o.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(h.SQ,{align:"end",children:[(0,r.jsx)(h._2,{asChild:!0,children:(0,r.jsxs)(i(),{href:`/dashboard/blog/${e._id}`,children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,r.jsx)(h._2,{asChild:!0,children:(0,r.jsxs)("a",{href:`${process.env.NEXT_PUBLIC_FRONTEND_URL||"http://localhost:3000"}/blog/${e.slug}`,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"View"]})}),(0,r.jsxs)(h._2,{onClick:()=>C(e._id),className:"text-red-600",children:[(0,r.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e._id))})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44105:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l={children:["",{children:["dashboard",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80638)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\blog\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\blog\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/blog/page",pathname:"/dashboard/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},59759:(e,t,s)=>{Promise.resolve().then(s.bind(s,80638))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68007:(e,t,s)=>{Promise.resolve().then(s.bind(s,26252))},79551:e=>{"use strict";e.exports=require("url")},80638:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\cms\\\\src\\\\app\\\\dashboard\\\\blog\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\dashboard\\blog\\page.tsx","default")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...n}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:t}),e),...n})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,1771,1658,9365,4758,4883,9638],()=>s(44105));module.exports=r})();