(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1431],{239:(e,t,s)=>{"use strict";s.d(t,{bL:()=>y,zi:()=>N});var a=s(2115),r=s(5185),l=s(6101),i=s(6081),n=s(5845),d=s(1275),c=s(3655),o=s(5155),u="Switch",[h,x]=(0,i.A)(u),[p,m]=h(u),v=a.forwardRef((e,t)=>{let{__scopeSwitch:s,name:i,checked:d,defaultChecked:h,required:x,disabled:m,value:v="on",onCheckedChange:g,form:j,...y}=e,[N,w]=a.useState(null),k=(0,l.s)(t,e=>w(e)),C=a.useRef(!1),A=!N||j||!!N.closest("form"),[T,F]=(0,n.i)({prop:d,defaultProp:null!=h&&h,onChange:g,caller:u});return(0,o.jsxs)(p,{scope:s,checked:T,disabled:m,children:[(0,o.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":x,"data-state":b(T),"data-disabled":m?"":void 0,disabled:m,value:v,...y,ref:k,onClick:(0,r.m)(e.onClick,e=>{F(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,o.jsx)(f,{control:N,bubbles:!C.current,name:i,value:v,checked:T,required:x,disabled:m,form:j,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var g="SwitchThumb",j=a.forwardRef((e,t)=>{let{__scopeSwitch:s,...a}=e,r=m(g,s);return(0,o.jsx)(c.sG.span,{"data-state":b(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:t})});j.displayName=g;var f=a.forwardRef((e,t)=>{let{__scopeSwitch:s,control:r,checked:i,bubbles:n=!0,...c}=e,u=a.useRef(null),h=(0,l.s)(u,t),x=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(i),p=(0,d.X)(r);return a.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==i&&t){let s=new Event("click",{bubbles:n});t.call(e,i),e.dispatchEvent(s)}},[x,i,n]),(0,o.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...c,tabIndex:-1,ref:h,style:{...c.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}f.displayName="SwitchBubbleInput";var y=v,N=j},333:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var a=s(5155);s(2115);var r=s(239),l=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...s,children:(0,a.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},968:(e,t,s)=>{"use strict";s.d(t,{b:()=>n});var a=s(2115),r=s(3655),l=s(5155),i=a.forwardRef((e,t)=>(0,l.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var n=i},1154:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2053:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(5155),r=s(2115),l=s(285),i=s(2523),n=s(5057),d=s(6695),c=s(4416),o=s(1154),u=s(7213);let h=(0,s(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var x=s(8164),p=s(6766);function m(e){let{value:t,onChange:s,label:m="Image",folder:v="portfolio-cms",className:g="",accept:j="image/*",maxSize:f=5}=e,[b,y]=(0,r.useState)(!1),[N,w]=(0,r.useState)(""),[k,C]=(0,r.useState)(""),[A,T]=(0,r.useState)(!1),F=(0,r.useRef)(null),S=async e=>{if(e){if(e.size>1024*f*1024)return void w("File size must be less than ".concat(f,"MB"));if(!e.type.startsWith("image/"))return void w("Please select a valid image file");y(!0),w("");try{let t=new FormData;t.append("file",e),t.append("folder",v);let a=await fetch("/api/upload",{method:"POST",body:t}),r=await a.json();a.ok?s(r.url):w(r.error||"Upload failed")}catch(e){w("Network error occurred")}finally{y(!1)}}},P=()=>{k.trim()&&(s(k.trim()),C(""),T(!1))};return(0,a.jsxs)("div",{className:"space-y-4 ".concat(g),children:[(0,a.jsx)(n.J,{children:m}),t?(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.default,{src:t,alt:"Uploaded image",width:300,height:200,className:"w-full h-48 object-cover rounded-lg"}),(0,a.jsx)(l.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{s(""),F.current&&(F.current.value="")},children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2 truncate",children:t})]})}):(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&S(t)},onDragOver:e=>{e.preventDefault()},children:b?(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 animate-spin text-blue-500 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop an image here, or click to select"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=F.current)?void 0:e.click()},children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4"}),"Upload File"]}),(0,a.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>T(!A),children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Add URL"]})]})]})}),A&&(0,a.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,a.jsx)(i.p,{placeholder:"Enter image URL...",value:k,onChange:e=>C(e.target.value),onKeyPress:e=>"Enter"===e.key&&P()}),(0,a.jsx)(l.$,{type:"button",onClick:P,children:"Add"}),(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>T(!1),children:"Cancel"})]}),N&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-2",children:N}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Supports: JPG, PNG, GIF, WebP (max ",f,"MB)"]})]})}),(0,a.jsx)("input",{ref:F,type:"file",accept:j,onChange:e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];s&&S(s)},className:"hidden"})]})}},3041:(e,t,s)=>{Promise.resolve().then(s.bind(s,5821))},4229:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5057:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var a=s(5155);s(2115);var r=s(968),l=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},5365:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>n,TN:()=>d});var a=s(5155);s(2115);var r=s(2085),l=s(9434);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(i({variant:s}),t),...r})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...s})}},5821:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(5155),r=s(2115),l=s(5695),i=s(6913),n=s(2053),d=s(285),c=s(2523),o=s(8539),u=s(5057),h=s(6695),x=s(333),p=s(5365),m=s(7550),v=s(4229),g=s(2657),j=s(8136),f=s(6874),b=s.n(f);function y(){let e=(0,l.useRouter)(),[t,s]=(0,r.useState)(!1),[f,y]=(0,r.useState)(""),[N,w]=(0,r.useState)(""),[k,C]=(0,r.useState)({name:"",logo:"",description:"",website:"",industry:"",projectType:"",featured:!1,status:"draft",order:0}),A=(e,t)=>{C(s=>({...s,[e]:t}))},T=async t=>{s(!0),y(""),w("");try{let s=await fetch("/api/clients",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...k,status:t})}),a=await s.json();s.ok?(w("Client ".concat("published"===t?"published":"saved as draft"," successfully!")),setTimeout(()=>{e.push("/dashboard/clients")},1500)):y(a.error||"Failed to save client")}catch(e){y("Network error occurred")}finally{s(!1)}};return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(b(),{href:"/dashboard/clients",children:(0,a.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Back to Clients"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"New Client"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Add a new client to your portfolio"})]})]})}),f&&(0,a.jsx)(p.Fc,{variant:"destructive",children:(0,a.jsx)(p.TN,{children:f})}),N&&(0,a.jsx)(p.Fc,{children:(0,a.jsx)(p.TN,{children:N})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Client Information"})}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"name",children:"Client Name *"}),(0,a.jsx)(c.p,{id:"name",value:k.name,onChange:e=>A("name",e.target.value),placeholder:"Enter client name...",required:!0})]}),(0,a.jsx)("div",{children:(0,a.jsx)(n.A,{label:"Client Logo *",value:k.logo,onChange:e=>A("logo",e),folder:"clients"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"description",children:"Description *"}),(0,a.jsx)(o.T,{id:"description",value:k.description,onChange:e=>A("description",e.target.value),placeholder:"Brief description about the client and your work together...",rows:4,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"website",children:"Website (Optional)"}),(0,a.jsx)(c.p,{id:"website",type:"url",value:k.website,onChange:e=>A("website",e.target.value),placeholder:"https://client-website.com (optional)"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"industry",children:"Industry"}),(0,a.jsx)(c.p,{id:"industry",value:k.industry,onChange:e=>A("industry",e.target.value),placeholder:"e.g., Technology, Healthcare, Finance"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"projectType",children:"Project Type"}),(0,a.jsx)(c.p,{id:"projectType",value:k.projectType,onChange:e=>A("projectType",e.target.value),placeholder:"e.g., Web Development, Branding, Marketing"})]})]})]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Publish"})}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.d,{id:"featured",checked:k.featured,onCheckedChange:e=>A("featured",e)}),(0,a.jsx)(u.J,{htmlFor:"featured",children:"Featured Client"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(d.$,{onClick:()=>T("draft"),variant:"outline",className:"w-full",disabled:t,children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Save Draft"]}),(0,a.jsxs)(d.$,{onClick:()=>T("published"),className:"w-full",disabled:t||!k.name||!k.logo||!k.description,children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Publish"]})]})]})]}),(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Settings"})}),(0,a.jsx)(h.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{htmlFor:"order",children:"Display Order"}),(0,a.jsx)(c.p,{id:"order",type:"number",value:k.order,onChange:e=>A("order",parseInt(e.target.value)||0),placeholder:"0"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Lower numbers appear first"})]})})]}),(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Preview"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[k.logo?(0,a.jsx)("img",{src:k.logo,alt:k.name,className:"w-12 h-12 object-contain rounded"}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded flex items-center justify-center",children:(0,a.jsx)(j.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:k.name||"Client Name"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:k.industry||"Industry"})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:k.description||"Client description will appear here..."}),k.website&&(0,a.jsxs)("p",{className:"text-xs text-blue-600 mb-2",children:["\uD83C\uDF10 ",k.website]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{children:k.projectType||"Project Type"}),k.featured&&(0,a.jsx)("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:"Featured"})]})]})})]})]})]})]})})}},7213:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8164:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},8539:(e,t,s)=>{"use strict";s.d(t,{T:()=>l});var a=s(5155);s(2115);var r=s(9434);function l(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,5812,6766,6823,8441,1684,7358],()=>t(3041)),_N_E=e.O()}]);