'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Users,
  MessageSquare,
  Video,
  Play,
  Star,
  Plus,
  Edit,
  Calendar,
  Eye
} from 'lucide-react';
import Link from 'next/link';

interface DashboardStats {
  totalPosts: number;
  publishedPosts: number;
  draftPosts: number;
  totalVideos: number;
  totalReels: number;
  totalTestimonials: number;
  totalClients: number;
  featuredContent: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPosts: 0,
    publishedPosts: 0,
    draftPosts: 0,
    totalVideos: 0,
    totalReels: 0,
    totalTestimonials: 0,
    totalClients: 0,
    featuredContent: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      let totalFeatured = 0;

      // Fetch blog stats
      const blogResponse = await fetch('/api/blog', {
        credentials: 'include',
      });

      if (blogResponse.ok) {
        const blogData = await blogResponse.json();
        const posts = blogData.data;
        const featuredPosts = posts.filter((p: any) => p.featured).length;
        totalFeatured += featuredPosts;

        setStats(prev => ({
          ...prev,
          totalPosts: posts.length,
          publishedPosts: posts.filter((p: any) => p.status === 'published').length,
          draftPosts: posts.filter((p: any) => p.status === 'draft').length,
        }));
      }

      // Fetch videos stats
      const videosResponse = await fetch('/api/videos', {
        credentials: 'include',
      });

      if (videosResponse.ok) {
        const videosData = await videosResponse.json();
        const videos = videosData.data;
        const featuredVideos = videos.filter((v: any) => v.featured).length;
        totalFeatured += featuredVideos;

        setStats(prev => ({
          ...prev,
          totalVideos: videos.length,
        }));
      }

      // Fetch reels stats
      const reelsResponse = await fetch('/api/reels', {
        credentials: 'include',
      });

      if (reelsResponse.ok) {
        const reelsData = await reelsResponse.json();
        const reels = reelsData.data;
        const featuredReels = reels.filter((r: any) => r.featured).length;
        totalFeatured += featuredReels;

        setStats(prev => ({
          ...prev,
          totalReels: reels.length,
        }));
      }

      // Fetch testimonials stats
      const testimonialsResponse = await fetch('/api/testimonials', {
        credentials: 'include',
      });

      if (testimonialsResponse.ok) {
        const testimonialsData = await testimonialsResponse.json();
        const testimonials = testimonialsData.data;
        const featuredTestimonials = testimonials.filter((t: any) => t.featured).length;
        totalFeatured += featuredTestimonials;

        setStats(prev => ({
          ...prev,
          totalTestimonials: testimonials.length,
        }));
      }

      // Fetch clients stats
      const clientsResponse = await fetch('/api/clients', {
        credentials: 'include',
      });

      if (clientsResponse.ok) {
        const clientsData = await clientsResponse.json();
        const clients = clientsData.data;
        const featuredClients = clients.filter((c: any) => c.featured).length;
        totalFeatured += featuredClients;

        setStats(prev => ({
          ...prev,
          totalClients: clients.length,
          featuredContent: totalFeatured,
        }));
      }

    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Blog Posts',
      value: stats.totalPosts,
      icon: FileText,
      description: 'Total articles',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Videos',
      value: stats.totalVideos,
      icon: Video,
      description: 'YouTube videos',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Reels',
      value: stats.totalReels,
      icon: Play,
      description: 'Social media reels',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Testimonials',
      value: stats.totalTestimonials,
      icon: MessageSquare,
      description: 'Client feedback',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Clients',
      value: stats.totalClients,
      icon: Users,
      description: 'Business clients',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Featured Content',
      value: stats.featuredContent,
      icon: Star,
      description: 'Highlighted items',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Content Dashboard</h1>
          <p className="text-lg text-gray-600">Manage your portfolio content easily</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {statCards.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {loading ? (
                    <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                  ) : (
                    stat.value.toLocaleString()
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-semibold">Create New Content</CardTitle>
            <p className="text-sm text-gray-600">Add new content to your portfolio</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <Link href="/dashboard/blog/new">
                <Button variant="outline" className="h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-blue-50 hover:border-blue-300">
                  <FileText className="h-6 w-6 text-blue-600" />
                  <span className="text-sm font-medium">Blog Post</span>
                </Button>
              </Link>

              <Link href="/dashboard/videos/new">
                <Button variant="outline" className="h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-red-50 hover:border-red-300">
                  <Video className="h-6 w-6 text-red-600" />
                  <span className="text-sm font-medium">Video</span>
                </Button>
              </Link>

              <Link href="/dashboard/reels/new">
                <Button variant="outline" className="h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-purple-50 hover:border-purple-300">
                  <Play className="h-6 w-6 text-purple-600" />
                  <span className="text-sm font-medium">Reel</span>
                </Button>
              </Link>

              <Link href="/dashboard/testimonials/new">
                <Button variant="outline" className="h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-green-50 hover:border-green-300">
                  <MessageSquare className="h-6 w-6 text-green-600" />
                  <span className="text-sm font-medium">Testimonial</span>
                </Button>
              </Link>

              <Link href="/dashboard/clients/new">
                <Button variant="outline" className="h-20 w-full flex flex-col items-center justify-center space-y-2 hover:bg-orange-50 hover:border-orange-300">
                  <Users className="h-6 w-6 text-orange-600" />
                  <span className="text-sm font-medium">Client</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Content Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-semibold">Manage Content</CardTitle>
              <p className="text-sm text-gray-600">View and edit your existing content</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <Link href="/dashboard/blog">
                <Button variant="ghost" className="w-full justify-start h-12 hover:bg-blue-50">
                  <FileText className="h-5 w-5 text-blue-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Blog Posts</div>
                    <div className="text-xs text-gray-500">{stats.totalPosts} articles</div>
                  </div>
                </Button>
              </Link>

              <Link href="/dashboard/videos">
                <Button variant="ghost" className="w-full justify-start h-12 hover:bg-red-50">
                  <Video className="h-5 w-5 text-red-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Videos</div>
                    <div className="text-xs text-gray-500">{stats.totalVideos} videos</div>
                  </div>
                </Button>
              </Link>

              <Link href="/dashboard/reels">
                <Button variant="ghost" className="w-full justify-start h-12 hover:bg-purple-50">
                  <Play className="h-5 w-5 text-purple-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Reels</div>
                    <div className="text-xs text-gray-500">{stats.totalReels} reels</div>
                  </div>
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-semibold">Business Content</CardTitle>
              <p className="text-sm text-gray-600">Manage testimonials and clients</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <Link href="/dashboard/testimonials">
                <Button variant="ghost" className="w-full justify-start h-12 hover:bg-green-50">
                  <MessageSquare className="h-5 w-5 text-green-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Testimonials</div>
                    <div className="text-xs text-gray-500">{stats.totalTestimonials} reviews</div>
                  </div>
                </Button>
              </Link>

              <Link href="/dashboard/clients">
                <Button variant="ghost" className="w-full justify-start h-12 hover:bg-orange-50">
                  <Users className="h-5 w-5 text-orange-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Clients</div>
                    <div className="text-xs text-gray-500">{stats.totalClients} clients</div>
                  </div>
                </Button>
              </Link>

              <div className="pt-2">
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center">
                    <Star className="h-5 w-5 text-yellow-600 mr-3" />
                    <div>
                      <div className="font-medium text-sm">Featured Content</div>
                      <div className="text-xs text-gray-500">Highlighted across portfolio</div>
                    </div>
                  </div>
                  <div className="text-lg font-bold text-yellow-600">
                    {stats.featuredContent}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
