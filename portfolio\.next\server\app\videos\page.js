(()=>{var e={};e.id=227,e.ids=[227],e.modules={381:(e,t,r)=>{Promise.resolve().then(r.bind(r,2208)),Promise.resolve().then(r.bind(r,92830)),Promise.resolve().then(r.bind(r,94743)),Promise.resolve().then(r.bind(r,31646))},2208:(e,t,r)=>{"use strict";r.d(t,{default:()=>v});var s=r(60687),a=r(43210),i=r(85814),o=r.n(i),n=r(30474),l=r(28559),d=r(97840),c=r(80462),u=r(6943),p=r(25366),f=r(96834),m=r(29523),x=r(15079),h=r(39641),g=r(62185);function v({allVideos:e,featuredVideos:t}){let[r,i]=(0,a.useState)(null),[v,b]=(0,a.useState)(e),[y,j]=(0,a.useState)("grid"),[w,N]=(0,a.useState)("all"),k=Array.from(new Set(e.map(e=>e.category).filter(Boolean)));return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("div",{className:"bg-muted text-foreground py-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)(o(),{href:"/#featured-work",className:"inline-flex items-center gap-2 text-accent hover:text-foreground transition-colors mb-8",children:[(0,s.jsx)(l.A,{size:20}),"Back to Portfolio"]}),(0,s.jsx)("h1",{className:"text-secondary text-4xl md:text-5xl font-heading font-bold mb-4",children:"Video Portfolio"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl",children:"Explore my complete collection of video editing work, from creative projects to professional tutorials."})]})}),(0,s.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[t.length>0&&(0,s.jsxs)("section",{className:"mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-heading font-bold text-primary mb-8",children:"Featured Videos"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map(e=>(0,s.jsx)("div",{children:(0,s.jsxs)("div",{onClick:()=>i(e.id),className:"group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer h-[420px] flex flex-col",children:[(0,s.jsxs)("div",{className:"relative aspect-video overflow-hidden flex-shrink-0",children:[(0,s.jsx)(n.default,{src:(0,g.I9)(e.id),alt:e.title,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center",children:(0,s.jsx)("div",{className:"bg-card/90 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-primary ml-1",fill:"currentColor"})})}),(0,s.jsx)("div",{className:"absolute top-4 left-4",children:(0,s.jsx)(f.E,{variant:"default",className:"bg-accent text-accent-foreground",children:"Featured"})}),e.category&&(0,s.jsx)("div",{className:"absolute top-4 right-4",children:(0,s.jsx)(f.E,{variant:"outline",children:e.category})})]}),(0,s.jsxs)("div",{className:"p-6 flex-1 flex flex-col",children:[(0,s.jsx)("h3",{className:"text-xl font-heading font-semibold text-primary mb-3 group-hover:text-secondary transition-colors duration-300 line-clamp-2",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-2 flex-1",children:e.description}),e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mt-auto",children:[e.tags.slice(0,3).map(e=>(0,s.jsx)(f.E,{variant:"outline",className:"text-xs",children:e},e)),e.tags.length>3&&(0,s.jsxs)(f.E,{variant:"outline",className:"text-xs",children:["+",e.tags.length-3]})]})]})]})},e._id))})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{size:20,className:"text-primary"}),(0,s.jsx)("span",{className:"font-medium text-primary",children:"Filter:"})]}),(0,s.jsxs)(x.l6,{value:w,onValueChange:t=>{N(t),"all"===t?b(e):b(e.filter(e=>e.category===t))},children:[(0,s.jsx)(x.bq,{className:"w-48",children:(0,s.jsx)(x.yv,{placeholder:"Select category"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"all",children:"All Categories"}),k.map(e=>(0,s.jsx)(x.eb,{value:e,children:e},e))]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.$,{variant:"grid"===y?"default":"outline",size:"sm",onClick:()=>j("grid"),children:(0,s.jsx)(u.A,{size:16})}),(0,s.jsx)(m.$,{variant:"list"===y?"default":"outline",size:"sm",onClick:()=>j("list"),children:(0,s.jsx)(p.A,{size:16})})]})]}),(0,s.jsxs)("section",{children:[(0,s.jsxs)("h2",{className:"text-3xl font-heading font-bold text-primary mb-8",children:["all"===w?"All Videos":`${w} Videos`,(0,s.jsxs)("span",{className:"text-lg font-normal text-muted-foreground ml-2",children:["(",v.length," videos)"]})]}),0===v.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-lg text-muted-foreground",children:"No videos found for the selected category."})}):(0,s.jsx)("div",{className:"grid"===y?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8":"space-y-6",children:v.map(e=>(0,s.jsx)("div",{children:(0,s.jsxs)("div",{onClick:()=>i(e.id),className:`group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer ${"list"===y?"flex h-[200px]":"h-[420px] flex flex-col"}`,children:[(0,s.jsxs)("div",{className:`relative overflow-hidden flex-shrink-0 ${"list"===y?"w-80 aspect-video":"aspect-video"}`,children:[(0,s.jsx)(n.default,{src:(0,g.I9)(e.id),alt:e.title,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center",children:(0,s.jsx)("div",{className:"bg-card/90 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-primary ml-1",fill:"currentColor"})})}),e.featured&&(0,s.jsx)("div",{className:"absolute top-4 left-4",children:(0,s.jsx)(f.E,{variant:"secondary",children:"Featured"})}),e.category&&(0,s.jsx)("div",{className:"absolute top-4 right-4",children:(0,s.jsx)(f.E,{variant:"secondary",className:"bg-secondary/90 text-secondary-foreground",children:e.category})})]}),(0,s.jsxs)("div",{className:`p-6 flex-1 flex flex-col ${"list"===y?"justify-center":""}`,children:[(0,s.jsx)("h3",{className:`font-heading font-semibold text-primary mb-3 group-hover:text-secondary transition-colors duration-300 ${"list"===y?"text-lg line-clamp-2":"text-xl line-clamp-2"}`,children:e.title}),e.description&&(0,s.jsx)("p",{className:`text-muted-foreground text-sm leading-relaxed mb-4 ${"list"===y?"line-clamp-2":"line-clamp-2 flex-1"}`,children:e.description}),e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:`flex flex-wrap gap-2 ${"grid"===y?"mt-auto":""}`,children:[e.tags.slice(0,"list"===y?5:3).map(e=>(0,s.jsx)(f.E,{variant:"outline",className:"text-xs",children:e},e)),e.tags.length>("list"===y?5:3)&&(0,s.jsxs)(f.E,{variant:"outline",className:"text-xs",children:["+",e.tags.length-("list"===y?5:3)]})]})]})]})},e._id))})]})]}),(0,s.jsx)(h.U,{isOpen:null!==r,onClose:()=>i(null),videoId:r,videoTitle:[...t,...v].find(e=>e.id===r)?.title,variant:"default"})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},9436:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["videos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81339)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/videos/page",pathname:"/videos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>d,yv:()=>c});var s=r(60687);r(43210);var a=r(55491),i=r(78272),o=r(13964),n=r(3589),l=r(4780);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...o}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...i}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,s.jsx)(m,{}),(0,s.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(x,{})]})})}function f({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function m({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}function x({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31646:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(43210),i=r(2975),o=r(29523);function n(){let[e,t]=(0,a.useState)(!1);return e?(0,s.jsx)(o.$,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300","aria-label":"Scroll to top",children:(0,s.jsx)(i.A,{size:20})}):null}},33484:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\components\\\\layout\\\\ScrollToTop.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},39641:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var s=r(60687),a=r(11860),i=r(63503),o=r(29523);function n({isOpen:e,onClose:t,videoId:r,videoTitle:n="Video",variant:l="default"}){let d="compact"===l?"absolute top-4 right-4 z-10 bg-primary/50 hover:bg-primary/70 text-primary-foreground rounded-full p-2 transition-colors":"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2";return(0,s.jsx)(i.lG,{open:e,onOpenChange:e=>!e&&t(),children:(0,s.jsx)(i.Cf,{className:"compact"===l?"max-w-4xl w-full p-0":"w-[95vw] sm:max-w-2xl md:max-w-4xl lg:max-w-6xl p-0 bg-black border-0",children:(0,s.jsxs)("div",{className:"relative w-full aspect-video",children:["default"===l?(0,s.jsx)(i.HM,{asChild:!0,children:(0,s.jsx)(o.$,{variant:"ghost",size:"icon",className:d,children:(0,s.jsx)(a.A,{size:24})})}):(0,s.jsx)("button",{onClick:t,className:d,children:(0,s.jsx)(a.A,{size:20})}),r&&(0,s.jsx)("iframe",{src:`https://www.youtube.com/embed/${r}?autoplay=1&rel=0&modestbranding=1`,title:n,className:`w-full h-full ${"compact"===l?"rounded-lg":""}`,allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})]})})})}},52343:(e,t,r)=>{"use strict";r.d(t,{GB:()=>u,HI:()=>f,Lf:()=>p,OA:()=>c,Sk:()=>o,fY:()=>l,g6:()=>i,rT:()=>d,t1:()=>n});let s="https://uttam-backend.vercel.app";async function a(e){try{let t=await fetch(`${s}/api${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return console.error(`Failed to fetch ${e}:`,t.status,t.statusText),[];let r=await t.json();if(!r.success)return console.error(`API error for ${e}:`,r.message),[];return r.data||[]}catch(t){return console.error(`Network error fetching ${e}:`,t),[]}}async function i(){return(await a("/blog")).filter(e=>"published"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}async function o(){return(await i()).filter(e=>e.featured)}async function n(e){try{let t=await fetch(`${s}/api/blog/slug/${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return null;let r=await t.json();return r.success?r.data:null}catch(e){return console.error("Error fetching blog post by slug:",e),null}}async function l(){return(await a("/videos")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function d(){return(await l()).filter(e=>e.featured)}async function c(){return(await a("/reels")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function u(){return(await c()).filter(e=>e.featured)}async function p(){return(await a("/clients")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function f(){return(await p()).filter(e=>e.featured)}},62185:(e,t,r)=>{"use strict";r.d(t,{GB:()=>l,HI:()=>p,I9:()=>f,Yq:()=>x,dS:()=>c,g6:()=>a,lA:()=>m,rT:()=>o});async function s(e){try{let t=await fetch(`https://uttam-backend.vercel.app/api${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return console.error(`Failed to fetch ${e}:`,t.status,t.statusText),[];let r=await t.json();if(!r.success)return console.error(`API error for ${e}:`,r.message),[];return r.data||[]}catch(t){return console.error(`Network error fetching ${e}:`,t),[]}}async function a(){return(await s("/blog")).filter(e=>"published"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}async function i(){return(await s("/videos")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function o(){return(await i()).filter(e=>e.featured)}async function n(){return(await s("/reels")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function l(){return(await n()).filter(e=>e.featured)}async function d(){let e=await s("/testimonials");if(0===e.length){let{testimonials:e}=await r.e(304).then(r.bind(r,15304));return e.map((e,t)=>({...e,_id:e.id.toString(),slug:e.name.toLowerCase().replace(/\s+/g,"-"),rating:e.rating||5,status:"published",order:t+1,featured:t<3,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}))}return e.filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function c(){return(await d()).filter(e=>e.featured)}async function u(){return(await s("/clients")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function p(){return(await u()).filter(e=>e.featured)}function f(e){return`https://img.youtube.com/vi/${e}/maxresdefault.jpg`}function m(e){return`https://img.youtube.com/vi/${e}/maxresdefault.jpg`}function x(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>p,HM:()=>c,lG:()=>n,zM:()=>l});var s=r(60687);r(43210);var a=r(37908),i=r(11860),o=r(4780);function n({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({...e}){return(0,s.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function p({className:e,children:t,...r}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(u,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}},68550:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\app\\\\videos\\\\VideosPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\videos\\VideosPageClient.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81339:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),a=r(52343),i=r(68926),o=r(84712),n=r(33484),l=r(68550);let d={title:"Videos - Portfolio Showcase",description:"Watch all my video editing work, tutorials, and creative projects. Professional video editing portfolio by Uttam Rimal.",openGraph:{title:"Videos - Uttam Rimal",description:"Watch all my video editing work, tutorials, and creative projects. Professional video editing portfolio by Uttam Rimal."}};async function c(){let[e,t]=await Promise.all([(0,a.fY)(),(0,a.rT)()]);return(0,s.jsxs)("main",{children:[(0,s.jsx)(i.default,{}),(0,s.jsx)(l.default,{allVideos:e,featuredVideos:t}),(0,s.jsx)(o.default,{}),(0,s.jsx)(n.default,{})]})}},81821:(e,t,r)=>{Promise.resolve().then(r.bind(r,68550)),Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926)),Promise.resolve().then(r.bind(r,33484))},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),i=r(24224),o=r(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",success:"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40",warning:"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40",info:"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...i}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(n({variant:t}),e),...i})}},97840:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,176,474,908,282,849],()=>r(9436));module.exports=s})();