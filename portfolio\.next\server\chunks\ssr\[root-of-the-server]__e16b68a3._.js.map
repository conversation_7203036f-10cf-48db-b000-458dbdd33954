{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        success:\n          \"bg-success text-success-foreground shadow-xs hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40\",\n        warning:\n          \"bg-warning text-warning-foreground shadow-xs hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40\",\n        info:\n          \"bg-info text-info-foreground shadow-xs hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,SACE;YACF,SACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/common/WhatsAppButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { FaWhatsapp } from \"react-icons/fa\";\nimport { Button } from '@/components/ui/button';\n\nexport default function WhatsAppButton() {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isHovered, setIsHovered] = useState(false);\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  // Your WhatsApp number (replace with actual number)\n  const whatsappNumber = '+977XXXXXXXXXX'; // Replace with your actual WhatsApp number\n  const message = encodeURIComponent('Hi! I found your portfolio and would like to discuss a video editing project.');\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  useEffect(() => {\n    // Show tooltip after 3 seconds of being visible\n    if (isVisible) {\n      const timer = setTimeout(() => {\n        setShowTooltip(true);\n        // Hide tooltip after 5 seconds\n        setTimeout(() => setShowTooltip(false), 5000);\n      }, 3000);\n\n      return () => clearTimeout(timer);\n    }\n  }, [isVisible]);\n\n  const handleWhatsAppClick = () => {\n    const whatsappUrl = `https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${message}`;\n    window.open(whatsappUrl, '_blank', 'noopener,noreferrer');\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-24 right-8 z-50\">\n      {/* Auto-show Tooltip */}\n      <div className={`\n        absolute right-16 top-1/2 -translate-y-1/2 bg-success text-success-foreground px-4 py-3 rounded-lg text-sm whitespace-nowrap\n        transition-all duration-500 pointer-events-none shadow-lg\n        ${showTooltip && !isHovered ? 'opacity-100 translate-x-0 scale-100' : 'opacity-0 translate-x-4 scale-95'}\n      `}>\n        👋 Need help with video editing?\n        <div className=\"absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-success rotate-45\"></div>\n      </div>\n\n      <Button\n        variant=\"ghost\"\n        onClick={handleWhatsAppClick}\n        onMouseEnter={() => {\n          setIsHovered(true);\n          setShowTooltip(false);\n        }}\n        onMouseLeave={() => setIsHovered(false)}\n        className={`group transition-all duration-300 p-0 h-auto w-auto hover:bg-transparent ${\n          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'\n        }`}\n        aria-label=\"Contact via WhatsApp\"\n      >\n        {/* Main WhatsApp Button */}\n        <div className=\"relative\">\n          {/* Ripple Effect Background */}\n          <div className=\"absolute inset-0 rounded-full bg-success opacity-30 animate-ping\"></div>\n          <div className=\"absolute inset-0 rounded-full bg-success opacity-20 animate-ping animation-delay-1000\"></div>\n\n          <div className={`\n            relative bg-success hover:bg-success/90 text-success-foreground rounded-full p-4 shadow-lg hover:shadow-xl\n            transition-all duration-300 transform hover:scale-110 group-hover:rotate-12\n            ${isHovered ? 'shadow-success/50' : ''}\n          `}>\n            <FaWhatsapp  size={24} className=\"transition-transform duration-300\" />\n          </div>\n\n          {/* Notification Dot */}\n          <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full animate-ping\"></div>\n          <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full\"></div>\n        </div>\n\n        {/* Hover Tooltip */}\n        <div className={`\n          absolute right-16 top-1/2 -translate-y-1/2 bg-popover text-popover-foreground px-3 py-2 rounded-lg text-sm whitespace-nowrap\n          transition-all duration-300 pointer-events-none shadow-lg border border-border\n          ${isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'}\n        `}>\n          Chat with us on WhatsApp\n          <div className=\"absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-popover border-r border-b border-border rotate-45\"></div>\n        </div>\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oDAAoD;IACpD,MAAM,iBAAiB,kBAAkB,2CAA2C;IACpF,MAAM,UAAU,mBAAmB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,IAAI,WAAW;YACb,MAAM,QAAQ,WAAW;gBACvB,eAAe;gBACf,+BAA+B;gBAC/B,WAAW,IAAM,eAAe,QAAQ;YAC1C,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,sBAAsB;QAC1B,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,OAAO,CAAC,WAAW,IAAI,MAAM,EAAE,SAAS;QAC5F,OAAO,IAAI,CAAC,aAAa,UAAU;IACrC;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC;;;QAGf,EAAE,eAAe,CAAC,YAAY,wCAAwC,mCAAmC;MAC3G,CAAC;;oBAAE;kCAED,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,SAAS;gBACT,cAAc;oBACZ,aAAa;oBACb,eAAe;gBACjB;gBACA,cAAc,IAAM,aAAa;gBACjC,WAAW,CAAC,yEAAyE,EACnF,YAAY,8BAA8B,4BAC1C;gBACF,cAAW;;kCAGX,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAW,CAAC;;;YAGf,EAAE,YAAY,sBAAsB,GAAG;UACzC,CAAC;0CACC,cAAA,8OAAC,8IAAA,CAAA,aAAU;oCAAE,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAInC,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAW,CAAC;;;UAGf,EAAE,YAAY,8BAA8B,0BAA0B;QACxE,CAAC;;4BAAE;0CAED,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/providers/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  return (\n    <NextThemesProvider\n      attribute=\"class\"\n      defaultTheme=\"system\"\n      enableSystem\n    >\n      {children}\n    </NextThemesProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBACE,8OAAC,gJAAA,CAAA,gBAAkB;QACjB,WAAU;QACV,cAAa;QACb,YAAY;kBAEX;;;;;;AAGP", "debugId": null}}]}