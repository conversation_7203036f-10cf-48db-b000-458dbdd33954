"use strict";exports.id=4758,exports.ids=[4758],exports.modules={1359:(e,t,n)=>{n.d(t,{Oh:()=>l});var r=n(43210),o=0;function l(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],o=n[0];if(Array.isArray(r)&&Array.isArray(o)){if(r[0]!==o[0]||r[2]!==o[2])return!0}else if(r!==o)return!0;if(t[4])return!n[4];if(n[4])return!0;let l=Object.values(t[1])[0],a=Object.values(n[1])[0];return!l||!a||e(l,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let r=n(19169);function o(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},2943:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let r=n(51550),o=n(59656);var l=o._("_maxConcurrency"),a=o._("_runningCount"),i=o._("_queue"),u=o._("_processNext");class c{enqueue(e){let t,n,o=new Promise((e,r)=>{t=e,n=r}),l=async()=>{try{r._(this,a)[a]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,a)[a]--,r._(this,u)[u]()}};return r._(this,i)[i].push({promiseFn:o,task:l}),r._(this,u)[u](),o}bump(e){let t=r._(this,i)[i].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,i)[i].splice(t,1)[0];r._(this,i)[i].unshift(e),r._(this,u)[u](!0)}}constructor(e=5){Object.defineProperty(this,u,{value:s}),Object.defineProperty(this,l,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),r._(this,l)[l]=e,r._(this,a)[a]=0,r._(this,i)[i]=[]}}function s(e){if(void 0===e&&(e=!1),(r._(this,a)[a]<r._(this,l)[l]||e)&&r._(this,i)[i].length>0){var t;null==(t=r._(this,i)[i].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return c},getOrCreatePrefetchCacheEntry:function(){return u},prunePrefetchCache:function(){return f}});let r=n(59008),o=n(59154),l=n(75076);function a(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function i(e,t,n){return a(e,t===o.PrefetchKind.FULL,n)}function u(e){let{url:t,nextUrl:n,tree:r,prefetchCache:l,kind:i,allowAliasing:u=!0}=e,c=function(e,t,n,r,l){for(let i of(void 0===t&&(t=o.PrefetchKind.TEMPORARY),[n,null])){let n=a(e,!0,i),u=a(e,!1,i),c=e.search?n:u,s=r.get(c);if(s&&l){if(s.url.pathname===e.pathname&&s.url.search!==e.search)return{...s,aliased:!0};return s}let f=r.get(u);if(l&&e.search&&t!==o.PrefetchKind.FULL&&f&&!f.key.includes("%"))return{...f,aliased:!0}}if(t!==o.PrefetchKind.FULL&&l){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,i,n,l,u);return c?(c.status=h(c),c.kind!==o.PrefetchKind.FULL&&i===o.PrefetchKind.FULL&&c.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return s({tree:r,url:t,nextUrl:n,prefetchCache:l,kind:null!=i?i:o.PrefetchKind.TEMPORARY})}),i&&c.kind===o.PrefetchKind.TEMPORARY&&(c.kind=i),c):s({tree:r,url:t,nextUrl:n,prefetchCache:l,kind:i||o.PrefetchKind.TEMPORARY})}function c(e){let{nextUrl:t,tree:n,prefetchCache:r,url:l,data:a,kind:u}=e,c=a.couldBeIntercepted?i(l,u,t):i(l,u),s={treeAtTimeOfPrefetch:n,data:Promise.resolve(a),kind:u,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:c,status:o.PrefetchCacheEntryStatus.fresh,url:l};return r.set(c,s),s}function s(e){let{url:t,kind:n,tree:a,nextUrl:u,prefetchCache:c}=e,s=i(t,n),f=l.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:a,nextUrl:u,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:o}=e,l=r.get(o);if(!l)return;let a=i(t,l.kind,n);return r.set(a,{...l,key:a}),r.delete(o),a}({url:t,existingCacheKey:s,nextUrl:u,prefetchCache:c})),e.prerendered){let t=c.get(null!=n?n:s);t&&(t.kind=o.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:a,data:f,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:s,status:o.PrefetchCacheEntryStatus.fresh,url:t};return c.set(s,d),d}function f(e){for(let[t,n]of e)h(n)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:l}=e;return -1!==l?Date.now()<n+l?o.PrefetchCacheEntryStatus.fresh:o.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+d?r?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:t===o.PrefetchKind.AUTO&&Date.now()<n+p?o.PrefetchCacheEntryStatus.stale:t===o.PrefetchKind.FULL&&Date.now()<n+p?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let r=n(96127);function o(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return f},handleAliasedPrefetchEntry:function(){return s}});let r=n(83913),o=n(89752),l=n(86770),a=n(57391),i=n(33123),u=n(33898),c=n(59435);function s(e,t,n,s,d){let p,h=t.tree,y=t.cache,g=(0,a.createHrefFromUrl)(s);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=f(n,Object.fromEntries(s.searchParams));let{seedData:a,isRootRender:c,pathToSegment:d}=t,v=["",...d];n=f(n,Object.fromEntries(s.searchParams));let m=(0,l.applyRouterStatePatchToTree)(v,h,n,g),b=(0,o.createEmptyCacheNode)();if(c&&a){let t=a[1];b.loading=a[3],b.rsc=t,function e(t,n,o,l,a){if(0!==Object.keys(l[1]).length)for(let u in l[1]){let c,s=l[1][u],f=s[0],d=(0,i.createRouterCacheKey)(f),p=null!==a&&void 0!==a[2][u]?a[2][u]:null;if(null!==p){let e=p[1],n=p[3];c={lazyData:null,rsc:f.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=n.parallelRoutes.get(u);h?h.set(d,c):n.parallelRoutes.set(u,new Map([[d,c]])),e(t,c,o,s,p)}}(e,b,y,n,a)}else b.rsc=y.rsc,b.prefetchRsc=y.prefetchRsc,b.loading=y.loading,b.parallelRoutes=new Map(y.parallelRoutes),(0,u.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,y,t);m&&(h=m,y=b,p=!0)}return!!p&&(d.patchedTree=h,d.cache=y,d.canonicalUrl=g,d.hashFragment=s.hash,(0,c.handleMutable)(t,d))}function f(e,t){let[n,o,...l]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),o,...l];let a={};for(let[e,n]of Object.entries(o))a[e]=f(n,t);return[n,a,...l]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11096:(e,t,n)=>{n.d(t,{H4:()=>E,bL:()=>R});var r=n(43210),o=n(11273),l=n(13495),a=n(66156),i=n(14163),u=n(57379);function c(){return()=>{}}var s=n(60687),f="Avatar",[d,p]=(0,o.A)(f),[h,y]=d(f),g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[l,a]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:l,onImageLoadingStatusChange:a,children:(0,s.jsx)(i.sG.span,{...o,ref:t})})});g.displayName=f;var v="AvatarImage";r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:f=()=>{},...d}=e,p=y(v,n),h=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),l=r.useRef(null),i=o?(l.current||(l.current=new window.Image),l.current):null,[s,f]=r.useState(()=>w(i,e));return(0,a.N)(()=>{f(w(i,e))},[i,e]),(0,a.N)(()=>{let e=e=>()=>{f(e)};if(!i)return;let r=e("loaded"),o=e("error");return i.addEventListener("load",r),i.addEventListener("error",o),t&&(i.referrerPolicy=t),"string"==typeof n&&(i.crossOrigin=n),()=>{i.removeEventListener("load",r),i.removeEventListener("error",o)}},[i,n,t]),s}(o,d),g=(0,l.c)(e=>{f(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==h&&g(h)},[h,g]),"loaded"===h?(0,s.jsx)(i.sG.img,{...d,ref:t,src:o}):null}).displayName=v;var m="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...l}=e,a=y(m,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(i.sG.span,{...l,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=m;var R=g,E=b},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>l});var r=n(43210),o=n(60687);function l(e,t){let n=r.createContext(t),l=e=>{let{children:t,...l}=e,a=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(n.Provider,{value:a,children:t})};return l.displayName=e+"Provider",[l,function(o){let l=r.useContext(n);if(l)return l;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],l=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return l.scopeName=e,[function(t,l){let a=r.createContext(l),i=n.length;n=[...n,l];let u=t=>{let{scope:n,children:l,...u}=t,c=n?.[e]?.[i]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:l})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[i]||a,c=r.useContext(u);if(c)return c;if(void 0!==l)return l;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(l,...t)]}},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(43210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},13861:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>i});var r=n(43210),o=n(51215),l=n(8730),a=n(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,l.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...l,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},16189:(e,t,n)=>{var r=n(65773);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},18468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,l){let a=l.length<=2,[i,u]=l,c=(0,r.createRouterCacheKey)(u),s=n.parallelRoutes.get(i);if(!s)return;let f=t.parallelRoutes.get(i);if(f&&f!==s||(f=new Map(s),t.parallelRoutes.set(i,f)),a)return void f.delete(c);let d=s.get(c),p=f.get(c);p&&d&&(p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},f.set(c,p)),e(p,d,(0,o.getNextFlightSegmentPath)(l)))}}});let r=n(33123),o=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18853:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(43210),o=n(66156);function l(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},19169:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},20158:(e,t,n)=>{n.d(t,{i3:()=>X,UC:()=>$,ZL:()=>Y,Kq:()=>G,bL:()=>V,l9:()=>q});var r=n(43210),o=n(70569),l=n(98599),a=n(11273),i=n(31355),u=n(96963),c=n(55509),s=n(25028),f=n(46059),d=n(14163),p=n(8730),h=n(65551),y=n(60687),g=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),v=r.forwardRef((e,t)=>(0,y.jsx)(d.sG.span,{...e,ref:t,style:{...g,...e.style}}));v.displayName="VisuallyHidden";var[m,b]=(0,a.A)("Tooltip",[c.Bk]),w=(0,c.Bk)(),R="TooltipProvider",E="tooltip.open",[_,x]=m(R),P=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:a}=e,i=r.useRef(!0),u=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,y.jsx)(_,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),i.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:l,children:a})};P.displayName=R;var T="Tooltip",[O,j]=m(T),S=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:l,onOpenChange:a,disableHoverableContent:i,delayDuration:s}=e,f=x(T,e.__scopeTooltip),d=w(t),[p,g]=r.useState(null),v=(0,u.B)(),m=r.useRef(0),b=i??f.disableHoverableContent,R=s??f.delayDuration,_=r.useRef(!1),[P,j]=(0,h.i)({prop:o,defaultProp:l??!1,onChange:e=>{e?(f.onOpen(),document.dispatchEvent(new CustomEvent(E))):f.onClose(),a?.(e)},caller:T}),S=r.useMemo(()=>P?_.current?"delayed-open":"instant-open":"closed",[P]),M=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,_.current=!1,j(!0)},[j]),C=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,j(!1)},[j]),A=r.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{_.current=!0,j(!0),m.current=0},R)},[R,j]);return r.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,y.jsx)(c.bL,{...d,children:(0,y.jsx)(O,{scope:t,contentId:v,open:P,stateAttribute:S,trigger:p,onTriggerChange:g,onTriggerEnter:r.useCallback(()=>{f.isOpenDelayedRef.current?A():M()},[f.isOpenDelayedRef,A,M]),onTriggerLeave:r.useCallback(()=>{b?C():(window.clearTimeout(m.current),m.current=0)},[C,b]),onOpen:M,onClose:C,disableHoverableContent:b,children:n})})};S.displayName=T;var M="TooltipTrigger",C=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,i=j(M,n),u=x(M,n),s=w(n),f=r.useRef(null),p=(0,l.s)(t,f,i.onTriggerChange),h=r.useRef(!1),g=r.useRef(!1),v=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,y.jsx)(c.Mz,{asChild:!0,...s,children:(0,y.jsx)(d.sG.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...a,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(g.current||u.isPointerInTransitRef.current||(i.onTriggerEnter(),g.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{i.onTriggerLeave(),g.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{i.open&&i.onClose(),h.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||i.onOpen()}),onBlur:(0,o.m)(e.onBlur,i.onClose),onClick:(0,o.m)(e.onClick,i.onClose)})})});C.displayName=M;var A="TooltipPortal",[N,L]=m(A,{forceMount:void 0}),k=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,l=j(A,t);return(0,y.jsx)(N,{scope:t,forceMount:n,children:(0,y.jsx)(f.C,{present:n||l.open,children:(0,y.jsx)(s.Z,{asChild:!0,container:o,children:r})})})};k.displayName=A;var D="TooltipContent",I=r.forwardRef((e,t)=>{let n=L(D,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...l}=e,a=j(D,e.__scopeTooltip);return(0,y.jsx)(f.C,{present:r||a.open,children:a.disableHoverableContent?(0,y.jsx)(z,{side:o,...l,ref:t}):(0,y.jsx)(U,{side:o,...l,ref:t})})}),U=r.forwardRef((e,t)=>{let n=j(D,e.__scopeTooltip),o=x(D,e.__scopeTooltip),a=r.useRef(null),i=(0,l.s)(t,a),[u,c]=r.useState(null),{trigger:s,onClose:f}=n,d=a.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{c(null),p(!1)},[p]),g=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(n,r,o,l)){case l:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(s&&d){let e=e=>g(e,d),t=e=>g(e,s);return s.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{s.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[s,d,g,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=s?.contains(t)||d?.contains(t),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let a=t[e],i=t[l],u=a.x,c=a.y,s=i.x,f=i.y;c>r!=f>r&&n<(s-u)*(r-c)/(f-c)+u&&(o=!o)}return o}(n,u);r?h():o&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[s,d,u,f,h]),(0,y.jsx)(z,{...e,ref:i})}),[F,H]=m(T,{isInside:!1}),B=(0,p.Dc)("TooltipContent"),z=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":l,onEscapeKeyDown:a,onPointerDownOutside:u,...s}=e,f=j(D,n),d=w(n),{onClose:p}=f;return r.useEffect(()=>(document.addEventListener(E,p),()=>document.removeEventListener(E,p)),[p]),r.useEffect(()=>{if(f.trigger){let e=e=>{let t=e.target;t?.contains(f.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[f.trigger,p]),(0,y.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,y.jsxs)(c.UC,{"data-state":f.stateAttribute,...d,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,y.jsx)(B,{children:o}),(0,y.jsx)(F,{scope:n,isInside:!0,children:(0,y.jsx)(v,{id:f.contentId,role:"tooltip",children:l||o})})]})})});I.displayName=D;var W="TooltipArrow",K=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=w(n);return H(W,n).isInside?null:(0,y.jsx)(c.i3,{...o,...r,ref:t})});K.displayName=W;var G=P,V=S,q=C,Y=k,$=I,X=K},22308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,o,,a]=t;for(let i in r.includes(l.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=n,t[3]="refresh"),o)e(o[i],n)}},refreshInactiveParallelSegments:function(){return a}});let r=n(56928),o=n(59008),l=n(83913);async function a(e){let t=new Set;await i({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function i(e){let{navigatedAt:t,state:n,updatedTree:l,updatedCache:a,includeNextUrl:u,fetchedSegments:c,rootTree:s=l,canonicalUrl:f}=e,[,d,p,h]=l,y=[];if(p&&p!==f&&"refresh"===h&&!c.has(p)){c.add(p);let e=(0,o.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[s[0],s[1],s[2],"refetch"],nextUrl:u?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,a,a,e)});y.push(e)}for(let e in d){let r=i({navigatedAt:t,state:n,updatedTree:d[e],updatedCache:a,includeNextUrl:u,fetchedSegments:c,rootTree:s,canonicalUrl:f});y.push(r)}await Promise.all(y)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),o=n(51215),l=n(14163),a=n(66156),i=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let f=n||c&&globalThis?.document?.body;return f?o.createPortal((0,i.jsx)(l.sG.div,{...u,ref:t}),f):null});u.displayName="Portal"},25232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:R,isExternalUrl:E,navigateType:_,shouldScroll:x,allowAliasing:P}=n,T={},{hash:O}=R,j=(0,o.createHrefFromUrl)(R),S="push"===_;if((0,g.prunePrefetchCache)(t.prefetchCache),T.preserveCustomHistoryState=!1,T.pendingPush=S,E)return b(t,T,R.toString(),S);if(document.getElementById("__next-page-redirect"))return b(t,T,j,S);let M=(0,g.getOrCreatePrefetchCacheEntry)({url:R,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:P}),{treeAtTimeOfPrefetch:C,data:A}=M;return d.prefetchQueue.bump(A),A.then(d=>{let{flightData:g,canonicalUrl:E,postponed:_}=d,P=Date.now(),A=!1;if(M.lastUsedTime||(M.lastUsedTime=P,A=!0),M.aliased){let r=(0,m.handleAliasedPrefetchEntry)(P,t,g,R,T);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return b(t,T,g,S);let N=E?(0,o.createHrefFromUrl)(E):j;if(O&&t.canonicalUrl.split("#",1)[0]===N.split("#",1)[0])return T.onlyHashChange=!0,T.canonicalUrl=N,T.shouldScroll=x,T.hashFragment=O,T.scrollableSegments=[],(0,s.handleMutable)(t,T);let L=t.tree,k=t.cache,D=[];for(let e of g){let{pathToSegment:n,seedData:o,head:s,isHeadPartial:d,isRootRender:g}=e,m=e.tree,E=["",...n],x=(0,a.applyRouterStatePatchToTree)(E,L,m,j);if(null===x&&(x=(0,a.applyRouterStatePatchToTree)(E,C,m,j)),null!==x){if(o&&g&&_){let e=(0,y.startPPRNavigation)(P,k,L,m,o,s,d,!1,D);if(null!==e){if(null===e.route)return b(t,T,j,S);x=e.route;let n=e.node;null!==n&&(T.cache=n);let o=e.dynamicRequestTree;if(null!==o){let n=(0,r.fetchServerResponse)(R,{flightRouterState:o,nextUrl:t.nextUrl});(0,y.listenForDynamicRequest)(e,n)}}else x=m}else{if((0,u.isNavigatingToNewRootLayout)(L,x))return b(t,T,j,S);let r=(0,p.createEmptyCacheNode)(),o=!1;for(let t of(M.status!==c.PrefetchCacheEntryStatus.stale||A?o=(0,f.applyFlightData)(P,k,r,e,M):(o=function(e,t,n,r){let o=!1;for(let l of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),w(r).map(e=>[...n,...e])))(0,v.clearCacheNodeDataForSegmentPath)(e,t,l),o=!0;return o}(r,k,n,m),M.lastUsedTime=P),(0,i.shouldHardNavigate)(E,L)?(r.rsc=k.rsc,r.prefetchRsc=k.prefetchRsc,(0,l.invalidateCacheBelowFlightSegmentPath)(r,k,n),T.cache=r):o&&(T.cache=r,k=r),w(m))){let e=[...n,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&D.push(e)}}L=x}}return T.patchedTree=L,T.canonicalUrl=N,T.scrollableSegments=D,T.hashFragment=O,T.shouldScroll=x,(0,s.handleMutable)(t,T)},()=>t)}}});let r=n(59008),o=n(57391),l=n(18468),a=n(86770),i=n(65951),u=n(2030),c=n(59154),s=n(59435),f=n(56928),d=n(75076),p=n(89752),h=n(83913),y=n(65956),g=n(5334),v=n(97464),m=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,s.handleMutable)(e,t)}function w(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,o]of Object.entries(r))for(let r of w(o))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26134:(e,t,n)=>{n.d(t,{UC:()=>ee,VY:()=>en,ZL:()=>J,bL:()=>Z,bm:()=>er,hE:()=>et,hJ:()=>Q});var r=n(43210),o=n(70569),l=n(98599),a=n(11273),i=n(96963),u=n(65551),c=n(31355),s=n(32547),f=n(25028),d=n(46059),p=n(14163),h=n(1359),y=n(42247),g=n(63376),v=n(8730),m=n(60687),b="Dialog",[w,R]=(0,a.A)(b),[E,_]=w(b),x=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:c=!0}=e,s=r.useRef(null),f=r.useRef(null),[d,p]=(0,u.i)({prop:o,defaultProp:l??!1,onChange:a,caller:b});return(0,m.jsx)(E,{scope:t,triggerRef:s,contentRef:f,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:d,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};x.displayName=b;var P="DialogTrigger";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=_(P,n),i=(0,l.s)(t,a.triggerRef);return(0,m.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":G(a.open),...r,ref:i,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})}).displayName=P;var T="DialogPortal",[O,j]=w(T,{forceMount:void 0}),S=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,a=_(T,t);return(0,m.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,m.jsx)(d.C,{present:n||a.open,children:(0,m.jsx)(f.Z,{asChild:!0,container:l,children:e})}))})};S.displayName=T;var M="DialogOverlay",C=r.forwardRef((e,t)=>{let n=j(M,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=_(M,e.__scopeDialog);return l.modal?(0,m.jsx)(d.C,{present:r||l.open,children:(0,m.jsx)(N,{...o,ref:t})}):null});C.displayName=M;var A=(0,v.TL)("DialogOverlay.RemoveScroll"),N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=_(M,n);return(0,m.jsx)(y.A,{as:A,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(p.sG.div,{"data-state":G(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),L="DialogContent",k=r.forwardRef((e,t)=>{let n=j(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=_(L,e.__scopeDialog);return(0,m.jsx)(d.C,{present:r||l.open,children:l.modal?(0,m.jsx)(D,{...o,ref:t}):(0,m.jsx)(I,{...o,ref:t})})});k.displayName=L;var D=r.forwardRef((e,t)=>{let n=_(L,e.__scopeDialog),a=r.useRef(null),i=(0,l.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,g.Eq)(e)},[]),(0,m.jsx)(U,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),I=r.forwardRef((e,t)=>{let n=_(L,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,m.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),U=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...u}=e,f=_(L,n),d=r.useRef(null),p=(0,l.s)(t,d);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,m.jsx)(c.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":G(f.open),...u,ref:p,onDismiss:()=>f.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)($,{titleId:f.titleId}),(0,m.jsx)(X,{contentRef:d,descriptionId:f.descriptionId})]})]})}),F="DialogTitle",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=_(F,n);return(0,m.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});H.displayName=F;var B="DialogDescription",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=_(B,n);return(0,m.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});z.displayName=B;var W="DialogClose",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=_(W,n);return(0,m.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function G(e){return e?"open":"closed"}K.displayName=W;var V="DialogTitleWarning",[q,Y]=(0,a.q)(V,{contentName:L,titleName:F,docsSlug:"dialog"}),$=({titleId:e})=>{let t=Y(V),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},X=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Z=x,J=S,Q=C,ee=k,et=H,en=z,er=K},26736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let r=n(2255);function o(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return l}});let r=n(57391),o=n(70642);function l(e,t){var n;let{url:l,tree:a}=t,i=(0,r.createHrefFromUrl)(l),u=a||e.tree,c=e.cache;return{canonicalUrl:i,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:u,nextUrl:null!=(n=(0,o.extractPathFromFlightRouterState)(u))?n:l.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return s}});let r=n(57391),o=n(86770),l=n(2030),a=n(25232),i=n(56928),u=n(59435),c=n(89752);function s(e,t){let{serverResponse:{flightData:n,canonicalUrl:s},navigatedAt:f}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof n)return(0,a.handleExternalUrl)(e,d,n,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of n){let{segmentPath:n,tree:u}=t,y=(0,o.applyRouterStatePatchToTree)(["",...n],p,u,e.canonicalUrl);if(null===y)return e;if((0,l.isNavigatingToNewRootLayout)(p,y))return(0,a.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let g=s?(0,r.createHrefFromUrl)(s):void 0;g&&(d.canonicalUrl=g);let v=(0,c.createEmptyCacheNode)();(0,i.applyFlightData)(f,h,v,t),d.patchedTree=y,d.cache=v,h=v,p=y}return(0,u.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return l},formatWithValidation:function(){return i},urlObjectKeys:function(){return a}});let r=n(40740)._(n(76715)),o=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:n}=e,l=e.protocol||"",a=e.pathname||"",i=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return l&&!l.endsWith(":")&&(l+=":"),e.slashes||(!l||o.test(l))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),s&&"?"!==s[0]&&(s="?"+s),""+l+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+i}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return l(e)}},31355:(e,t,n)=>{n.d(t,{qW:()=>d});var r,o=n(43210),l=n(70569),a=n(14163),i=n(98599),u=n(13495),c=n(60687),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:d,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:v,onDismiss:m,...b}=e,w=o.useContext(f),[R,E]=o.useState(null),_=R?.ownerDocument??globalThis?.document,[,x]=o.useState({}),P=(0,i.s)(t,e=>E(e)),T=Array.from(w.layers),[O]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),j=T.indexOf(O),S=R?T.indexOf(R):-1,M=w.layersWithOutsidePointerEventsDisabled.size>0,C=S>=j,A=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",l.current),l.current=r,t.addEventListener("click",l.current,{once:!0})):r()}else t.removeEventListener("click",l.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",l.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));C&&!n&&(y?.(e),v?.(e),e.defaultPrevented||m?.())},_),N=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(g?.(e),v?.(e),e.defaultPrevented||m?.())},_);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S===w.layers.size-1&&(d?.(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},_),o.useEffect(()=>{if(R)return n&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=_.body.style.pointerEvents,_.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(R)),w.layers.add(R),p(),()=>{n&&1===w.layersWithOutsidePointerEventsDisabled.size&&(_.body.style.pointerEvents=r)}},[R,_,n,w]),o.useEffect(()=>()=>{R&&(w.layers.delete(R),w.layersWithOutsidePointerEventsDisabled.delete(R),p())},[R,w]),o.useEffect(()=>{let e=()=>x({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...b,ref:P,style:{pointerEvents:M?C?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,l):o.dispatchEvent(l)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),l=(0,i.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:l})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(43210),o=n(98599),l=n(14163),a=n(13495),i=n(60687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:g,onUnmountAutoFocus:v,...m}=e,[b,w]=r.useState(null),R=(0,a.c)(g),E=(0,a.c)(v),_=r.useRef(null),x=(0,o.s)(t,e=>w(e)),P=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(P.paused||!b)return;let t=e.target;b.contains(t)?_.current=t:h(_.current,{select:!0})},t=function(e){if(P.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||h(_.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,b,P.paused]),r.useEffect(()=>{if(b){y.add(P);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,s);b.addEventListener(u,R),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(u,R),setTimeout(()=>{let t=new CustomEvent(c,s);b.addEventListener(c,E),b.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),b.removeEventListener(c,E),y.remove(P)},0)}}},[b,R,E,P]);let T=r.useCallback(e=>{if(!n&&!f||P.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,l]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&l?e.shiftKey||r!==l?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(l,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,P.paused]);return(0,i.jsx)(l.sG.div,{tabIndex:-1,...m,ref:x,onKeyDown:T})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var y=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=g(e,t)).unshift(t)},remove(t){e=g(e,t),e[0]?.resume()}}}();function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},32708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return u},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let r=n(34400),o=n(41500),l=n(33123),a=n(83913);function i(e,t,n,i,u,c){let{segmentPath:s,seedData:f,tree:d,head:p}=i,h=t,y=n;for(let t=0;t<s.length;t+=2){let n=s[t],i=s[t+1],g=t===s.length-2,v=(0,l.createRouterCacheKey)(i),m=y.parallelRoutes.get(n);if(!m)continue;let b=h.parallelRoutes.get(n);b&&b!==m||(b=new Map(m),h.parallelRoutes.set(n,b));let w=m.get(v),R=b.get(v);if(g){if(f&&(!R||!R.lazyData||R===w)){let t=f[0],n=f[1],l=f[3];R={lazyData:null,rsc:c||t!==a.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:l,parallelRoutes:c&&w?new Map(w.parallelRoutes):new Map,navigatedAt:e},w&&c&&(0,r.invalidateCacheByRouterState)(R,w,d),c&&(0,o.fillLazyItemsTillLeafWithHead)(e,R,w,d,f,p,u),b.set(v,R)}continue}R&&w&&(R===w&&(R={lazyData:R.lazyData,rsc:R.rsc,prefetchRsc:R.prefetchRsc,head:R.head,prefetchHead:R.prefetchHead,parallelRoutes:new Map(R.parallelRoutes),loading:R.loading},b.set(v,R)),h=R,y=w)}}function u(e,t,n,r,o){i(e,t,n,r,o,!0)}function c(e,t,n,r,o){i(e,t,n,r,o,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34318:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},34400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let r=n(33123);function o(e,t,n){for(let o in n[1]){let l=n[1][o][0],a=(0,r.createRouterCacheKey)(l),i=t.parallelRoutes.get(o);if(i){let t=new Map(i);t.delete(a),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return l},getBotType:function(){return u},isBot:function(){return i}});let r=n(95796),o=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,l=r.HTML_LIMITED_BOT_UA_RE.source;function a(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function i(e){return o.test(e)||a(e)}function u(e){return o.test(e)?"dom":a(e)?"html":void 0}},35429:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return S}});let r=n(11264),o=n(11448),l=n(91563),a=n(59154),i=n(6361),u=n(57391),c=n(25232),s=n(86770),f=n(2030),d=n(59435),p=n(41500),h=n(89752),y=n(68214),g=n(96493),v=n(22308),m=n(74007),b=n(36875),w=n(97860),R=n(5334),E=n(25942),_=n(26736),x=n(24642);n(50593);let{createFromFetch:P,createTemporaryReferenceSet:T,encodeReply:O}=n(19357);async function j(e,t,n){let a,u,{actionId:c,actionArgs:s}=n,f=T(),d=(0,x.extractInfoFromServerReferenceId)(c),p="use-cache"===d.type?(0,x.omitUnusedArgs)(s,d):s,h=await O(p,{temporaryReferences:f}),y=await fetch("",{method:"POST",headers:{Accept:l.RSC_CONTENT_TYPE_HEADER,[l.ACTION_HEADER]:c,[l.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[l.NEXT_URL]:t}:{}},body:h}),g=y.headers.get("x-action-redirect"),[v,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":a=w.RedirectType.push;break;case"replace":a=w.RedirectType.replace;break;default:a=void 0}let R=!!y.headers.get(l.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(y.headers.get("x-action-revalidated")||"[[],0,0]");u={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){u={paths:[],tag:!1,cookie:!1}}let E=v?(0,i.assignLocation)(v,new URL(e.canonicalUrl,window.location.href)):void 0,_=y.headers.get("content-type");if(null==_?void 0:_.startsWith(l.RSC_CONTENT_TYPE_HEADER)){let e=await P(Promise.resolve(y),{callServer:r.callServer,findSourceMapURL:o.findSourceMapURL,temporaryReferences:f});return v?{actionFlightData:(0,m.normalizeFlightData)(e.f),redirectLocation:E,redirectType:a,revalidatedParts:u,isPrerender:R}:{actionResult:e.a,actionFlightData:(0,m.normalizeFlightData)(e.f),redirectLocation:E,redirectType:a,revalidatedParts:u,isPrerender:R}}if(y.status>=400)throw Object.defineProperty(Error("text/plain"===_?await y.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:E,redirectType:a,revalidatedParts:u,isPrerender:R}}function S(e,t){let{resolve:n,reject:r}=t,o={},l=e.tree;o.preserveCustomHistoryState=!1;let i=e.nextUrl&&(0,y.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,m=Date.now();return j(e,i,t).then(async y=>{let x,{actionResult:P,actionFlightData:T,redirectLocation:O,redirectType:j,isPrerender:S,revalidatedParts:M}=y;if(O&&(j===w.RedirectType.replace?(e.pushRef.pendingPush=!1,o.pendingPush=!1):(e.pushRef.pendingPush=!0,o.pendingPush=!0),o.canonicalUrl=x=(0,u.createHrefFromUrl)(O,!1)),!T)return(n(P),O)?(0,c.handleExternalUrl)(e,o,O.href,e.pushRef.pendingPush):e;if("string"==typeof T)return n(P),(0,c.handleExternalUrl)(e,o,T,e.pushRef.pendingPush);let C=M.paths.length>0||M.tag||M.cookie;for(let r of T){let{tree:a,seedData:u,head:d,isRootRender:y}=r;if(!y)return console.log("SERVER ACTION APPLY FAILED"),n(P),e;let b=(0,s.applyRouterStatePatchToTree)([""],l,a,x||e.canonicalUrl);if(null===b)return n(P),(0,g.handleSegmentMismatch)(e,t,a);if((0,f.isNavigatingToNewRootLayout)(l,b))return n(P),(0,c.handleExternalUrl)(e,o,x||e.canonicalUrl,e.pushRef.pendingPush);if(null!==u){let t=u[1],n=(0,h.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=u[3],(0,p.fillLazyItemsTillLeafWithHead)(m,n,void 0,a,u,d,void 0),o.cache=n,o.prefetchCache=new Map,C&&await (0,v.refreshInactiveParallelSegments)({navigatedAt:m,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!i,canonicalUrl:o.canonicalUrl||e.canonicalUrl})}o.patchedTree=b,l=b}return O&&x?(C||((0,R.createSeededPrefetchCacheEntry)({url:O,data:{flightData:T,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:S?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),o.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,_.hasBasePath)(x)?(0,E.removeBasePath)(x):x,j||w.RedirectType.push))):n(P),(0,d.handleMutable)(e,o)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,l,a,i,u,c){if(0===Object.keys(a[1]).length){n.head=u;return}for(let s in a[1]){let f,d=a[1][s],p=d[0],h=(0,r.createRouterCacheKey)(p),y=null!==i&&void 0!==i[2][s]?i[2][s]:null;if(l){let r=l.parallelRoutes.get(s);if(r){let l,a=(null==c?void 0:c.kind)==="auto"&&c.status===o.PrefetchCacheEntryStatus.reusable,i=new Map(r),f=i.get(h);l=null!==y?{lazyData:null,rsc:y[1],prefetchRsc:null,head:null,prefetchHead:null,loading:y[3],parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),navigatedAt:t}:a&&f?{lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),loading:null,navigatedAt:t},i.set(h,l),e(t,l,f,d,y||null,u,c),n.parallelRoutes.set(s,i);continue}}if(null!==y){let e=y[1],n=y[3];f={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else f={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=n.parallelRoutes.get(s);g?g.set(h,f):n.parallelRoutes.set(s,new Map([[h,f]])),e(t,f,void 0,d,y,u,c)}}}});let r=n(33123),o=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42247:(e,t,n)=>{n.d(t,{A:()=>V});var r,o,l=function(){return(l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,n(43210)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var f="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,d=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var l=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(l)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=l({async:!0,ssr:!1},e),a}(),y=function(){},g=i.forwardRef(function(e,t){var n,r,o,u,c=i.useRef(null),p=i.useState({onScrollCapture:y,onWheelCapture:y,onTouchMoveCapture:y}),g=p[0],v=p[1],m=e.forwardProps,b=e.children,w=e.className,R=e.removeScrollBar,E=e.enabled,_=e.shards,x=e.sideCar,P=e.noRelative,T=e.noIsolation,O=e.inert,j=e.allowPinchZoom,S=e.as,M=e.gapMode,C=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),A=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,f(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}d.set(u,n)},[n]),u),N=l(l({},C),g);return i.createElement(i.Fragment,null,E&&i.createElement(x,{sideCar:h,removeScrollBar:R,shards:_,noRelative:P,noIsolation:T,inert:O,setCallbacks:v,allowPinchZoom:!!j,lockRef:c,gapMode:M}),m?i.cloneElement(i.Children.only(b),l(l({},N),{ref:A})):i.createElement(void 0===S?"div":S,l({},N,{className:w,ref:A}),b))});g.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},g.classNames={fullWidth:c,zeroRight:u};var v=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,l({},n))};v.isSideCarExport=!0;var m=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var l,a;(l=t).styleSheet?l.styleSheet.cssText=r:l.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=m();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},R={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},_=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},x=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return R;var t=_(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},P=w(),T="data-scroll-locked",O=function(e,t,n,r){var o=e.left,l=e.top,a=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(T,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(T,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},j=function(){var e=parseInt(document.body.getAttribute(T)||"0",10);return isFinite(e)?e:0},S=function(){i.useEffect(function(){return document.body.setAttribute(T,(j()+1).toString()),function(){var e=j()-1;e<=0?document.body.removeAttribute(T):document.body.setAttribute(T,e.toString())}},[])},M=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;S();var l=i.useMemo(function(){return x(o)},[o]);return i.createElement(P,{styles:O(l,!t,o,n?"":"!important")})},C=!1;if("undefined"!=typeof window)try{var A=Object.defineProperty({},"passive",{get:function(){return C=!0,!0}});window.addEventListener("test",A,A),window.removeEventListener("test",A,A)}catch(e){C=!1}var N=!!C&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},k=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),D(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},D=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},U=function(e,t,n,r,o){var l,a=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),i=a*r,u=n.target,c=t.contains(u),s=!1,f=i>0,d=0,p=0;do{var h=I(e,u),y=h[0],g=h[1]-h[2]-a*y;(y||g)&&D(e,u)&&(d+=g,p+=y),u=u.parentNode.host||u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&i>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-i>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},z=0,W=[];let K=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(z++)[0],l=i.useState(w)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,l=F(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-l[0],c="deltaY"in e?e.deltaY:i[1]-l[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=k(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=k(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return U(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(W.length&&W[W.length-1]===l){var n="deltaY"in e?H(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),f=i.useCallback(function(e){n.current=F(e),r.current=void 0},[]),d=i.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return W.push(l),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,N),document.addEventListener("touchmove",c,N),document.addEventListener("touchstart",f,N),function(){W=W.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,N),document.removeEventListener("touchmove",c,N),document.removeEventListener("touchstart",f,N)}},[]);var h=e.removeScrollBar,y=e.inert;return i.createElement(i.Fragment,null,y?i.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),v);var G=i.forwardRef(function(e,t){return i.createElement(g,l({},e,{ref:t,sideCar:K}))});G.classNames=g.classNames;let V=G},44397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return o}});let r=n(33123);function o(e,t){return function e(t,n,o){if(0===Object.keys(n).length)return[t,o];if(n.children){let[l,a]=n.children,i=t.parallelRoutes.get("children");if(i){let t=(0,r.createRouterCacheKey)(l),n=i.get(t);if(n){let r=e(n,a,o+"/"+t);if(r)return r}}}for(let l in n){if("children"===l)continue;let[a,i]=n[l],u=t.parallelRoutes.get(l);if(!u)continue;let c=(0,r.createRouterCacheKey)(a),s=u.get(c);if(!s)continue;let f=e(s,i,o+"/"+c);if(f)return f}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),o=n(98599),l=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=i(u.current);s.current="mounted"===f?e:"none"},[f]),(0,l.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=i(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,l.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=i(u.current).includes(n.animationName);if(n.target===o&&r&&(d("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=i(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),c=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:c}):null};function i(e){return e?.animationName||"none"}a.displayName="Presence"},49625:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},50593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return f},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return u},createCacheKey:function(){return s},getCurrentCacheVersion:function(){return a},navigate:function(){return o},prefetch:function(){return r},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return l},schedulePrefetchTask:function(){return i}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,o=n,l=n,a=n,i=n,u=n,c=n,s=n;var f=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51214:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},51550:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53038:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(43210);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=l(e,r)),t&&(o.current=l(t,r))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53332:(e,t,n)=>{var r=n(43210),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useState,a=r.useEffect,i=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=l({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return i(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),a(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},54674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return l}});let r=n(84949),o=n(19169),l=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:l}=(0,o.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+l};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55509:(e,t,n)=>{n.d(t,{Mz:()=>eX,i3:()=>eJ,UC:()=>eZ,bL:()=>e$,Bk:()=>eN});var r=n(43210);let o=["top","right","bottom","left"],l=Math.min,a=Math.max,i=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function y(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(p(e))?"y":"x"}function m(e){return e.replace(/start|end/g,e=>f[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function R(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:l}=e,a=v(t),i=y(v(t)),u=g(i),c=p(t),s="y"===a,f=o.x+o.width/2-l.width/2,d=o.y+o.height/2-l.height/2,m=o[u]/2-l[u]/2;switch(c){case"top":r={x:f,y:o.y-l.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-l.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[i]-=m*(n&&s?-1:1);break;case"end":r[i]+=m*(n&&s?-1:1)}return r}let _=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:a}=n,i=l.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(c,r,u),d=r,p={},h=0;for(let n=0;n<i.length;n++){let{name:l,fn:y}=i[n],{x:g,y:v,data:m,reset:b}=await y({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=g?g:s,f=null!=v?v:f,p={...p,[l]:{...p[l],...m}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(c=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:s,y:f}=E(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function x(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:a,elements:i,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),y=w(h),g=i[p?"floating"===f?"reference":"floating":f],v=R(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(g)))||n?g:g.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(i.floating)),boundary:c,rootBoundary:s,strategy:u})),m="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(i.floating)),E=await (null==l.isElement?void 0:l.isElement(b))&&await (null==l.getScale?void 0:l.getScale(b))||{x:1,y:1},_=R(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:m,offsetParent:b,strategy:u}):m);return{top:(v.top-_.top+y.top)/E.y,bottom:(_.bottom-v.bottom+y.bottom)/E.y,left:(v.left-_.left+y.left)/E.x,right:(_.right-v.right+y.right)/E.x}}function P(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return o.some(t=>e[t]>=0)}async function O(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),i=h(n),u="y"===v(n),c=["left","top"].includes(a)?-1:1,s=l&&u?-1:1,f=d(t,e),{mainAxis:y,crossAxis:g,alignmentAxis:m}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return i&&"number"==typeof m&&(g="end"===i?-1*m:m),u?{x:g*s,y:y*c}:{x:y*c,y:g*s}}function j(){return"undefined"!=typeof window}function S(e){return A(e)?(e.nodeName||"").toLowerCase():"#document"}function M(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function C(e){var t;return null==(t=(A(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function A(e){return!!j()&&(e instanceof Node||e instanceof M(e).Node)}function N(e){return!!j()&&(e instanceof Element||e instanceof M(e).Element)}function L(e){return!!j()&&(e instanceof HTMLElement||e instanceof M(e).HTMLElement)}function k(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof M(e).ShadowRoot)}function D(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=B(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function U(e){let t=F(),n=N(e)?B(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(S(e))}function B(e){return M(e).getComputedStyle(e)}function z(e){return N(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function W(e){if("html"===S(e))return e;let t=e.assignedSlot||e.parentNode||k(e)&&e.host||C(e);return k(t)?t.host:t}function K(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=W(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:L(n)&&D(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),a=M(o);if(l){let e=G(a);return t.concat(a,a.visualViewport||[],D(o)?o:[],e&&n?K(e):[])}return t.concat(o,K(o,[],n))}function G(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function V(e){let t=B(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=L(e),l=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=i(n)!==l||i(r)!==a;return u&&(n=l,r=a),{width:n,height:r,$:u}}function q(e){return N(e)?e:e.contextElement}function Y(e){let t=q(e);if(!L(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=V(t),a=(l?i(n.width):n.width)/r,u=(l?i(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let $=c(0);function X(e){let t=M(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:$}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),a=q(e),i=c(1);t&&(r?N(r)&&(i=Y(r)):i=Y(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===M(a))&&o)?X(a):c(0),s=(l.left+u.x)/i.x,f=(l.top+u.y)/i.y,d=l.width/i.x,p=l.height/i.y;if(a){let e=M(a),t=r&&N(r)?M(r):r,n=e,o=G(n);for(;o&&r&&t!==n;){let e=Y(o),t=o.getBoundingClientRect(),r=B(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=l,f+=a,o=G(n=M(o))}}return R({width:d,height:p,x:s,y:f})}function J(e,t){let n=z(e).scrollLeft;return t?t.left+n:Z(C(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=M(e),r=C(e),o=n.visualViewport,l=r.clientWidth,a=r.clientHeight,i=0,u=0;if(o){l=o.width,a=o.height;let e=F();(!e||e&&"fixed"===t)&&(i=o.offsetLeft,u=o.offsetTop)}return{width:l,height:a,x:i,y:u}}(e,n);else if("document"===t)r=function(e){let t=C(e),n=z(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===B(r).direction&&(i+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:u}}(C(e));else if(N(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=L(e)?Y(e):c(1),a=e.clientWidth*l.x,i=e.clientHeight*l.y;return{width:a,height:i,x:o*l.x,y:r*l.y}}(t,n);else{let n=X(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return R(r)}function et(e){return"static"===B(e).position}function en(e,t){if(!L(e)||"fixed"===B(e).position)return null;if(t)return t(e);let n=e.offsetParent;return C(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=M(e);if(I(e))return n;if(!L(e)){let t=W(e);for(;t&&!H(t);){if(N(t)&&!et(t))return t;t=W(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(S(r))&&et(r);)r=en(r,t);return r&&H(r)&&et(r)&&!U(r)?n:r||function(e){let t=W(e);for(;L(t)&&!H(t);){if(U(t))return t;if(I(t))break;t=W(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=L(t),o=C(t),l="fixed"===n,a=Z(e,!0,l,t),i={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!l)if(("body"!==S(t)||D(o))&&(i=z(t)),r){let e=Z(t,!0,l,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o));l&&!r&&o&&(u.x=J(o));let s=!o||r||l?c(0):Q(o,i);return{x:a.left+i.scrollLeft-u.x-s.x,y:a.top+i.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},el={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,a=C(r),i=!!t&&I(t.floating);if(r===a||i&&l)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=L(r);if((d||!d&&!l)&&(("body"!==S(r)||D(a))&&(u=z(r)),L(r))){let e=Z(r);s=Y(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||l?c(0):Q(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:C,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?I(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=K(e,[],!1).filter(e=>N(e)&&"body"!==S(e)),o=null,l="fixed"===B(e).position,a=l?W(e):e;for(;N(a)&&!H(a);){let t=B(a),n=U(a);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||D(a)&&!n&&function e(t,n){let r=W(t);return!(r===n||!N(r)||H(r))&&("fixed"===B(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=W(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=i[0],c=i.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=l(r.right,e.right),e.bottom=l(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=V(e);return{width:t,height:n}},getScale:Y,isElement:N,isRTL:function(e){return"rtl"===B(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ei=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let m=w(p),b={x:n,y:r},R=y(v(o)),E=g(R),_=await u.getDimensions(f),x="y"===R,P=x?"clientHeight":"clientWidth",T=i.reference[E]+i.reference[R]-b[R]-i.floating[E],O=b[R]-i.reference[R],j=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),S=j?j[P]:0;S&&await (null==u.isElement?void 0:u.isElement(j))||(S=c.floating[P]||i.floating[E]);let M=S/2-_[E]/2-1,C=l(m[x?"top":"left"],M),A=l(m[x?"bottom":"right"],M),N=S-_[E]-A,L=S/2-_[E]/2+(T/2-O/2),k=a(C,l(L,N)),D=!s.arrow&&null!=h(o)&&L!==k&&i.reference[E]/2-(L<C?C:A)-_[E]/2<0,I=D?L<C?L-C:L-N:0;return{[R]:b[R]+I,data:{[R]:k,centerOffset:L-k-I,...D&&{alignmentOffset:I}},reset:D}}}),eu=(e,t,n)=>{let r=new Map,o={platform:el,...n},l={...o.platform,_c:r};return _(e,t,{...o,platform:l})};var ec=n(51215),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let ey=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ei({element:n.current,padding:r}).fn(t):{}:n?ei({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:a,middlewareData:i}=t,u=await O(t,e);return a===(null==(n=i.offset)?void 0:n.placement)&&null!=(r=i.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:l+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},h=await x(t,s),g=v(p(o)),m=y(g),b=f[m],w=f[g];if(i){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,l(b,r))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,l(w,r))}let R=c.fn({...t,[m]:b,[g]:w});return{...R,data:{x:R.x-n,y:R.y-r,enabled:{[m]:i,[g]:u}}}}}}(e),options:[e,t]}),em=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:a}=t,{offset:i=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},f=v(o),h=y(f),g=s[h],m=s[f],b=d(i,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===h?"height":"width",t=l.reference[h]-l.floating[e]+w.mainAxis,n=l.reference[h]+l.reference[e]-w.mainAxis;g<t?g=t:g>n&&(g=n)}if(c){var R,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=l.reference[f]-l.floating[e]+(t&&(null==(R=a.offset)?void 0:R[f])||0)+(t?0:w.crossAxis),r=l.reference[f]+l.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?w.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[h]:g,[f]:m}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,a,i;let{placement:u,middlewareData:c,rects:s,initialPlacement:f,platform:w,elements:R}=t,{mainAxis:E=!0,crossAxis:_=!0,fallbackPlacements:P,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:j=!0,...S}=d(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let M=p(u),C=v(f),A=p(f)===f,N=await (null==w.isRTL?void 0:w.isRTL(R.floating)),L=P||(A||!j?[b(f)]:function(e){let t=b(e);return[m(e),t,m(t)]}(f)),k="none"!==O;!P&&k&&L.push(...function(e,t,n,r){let o=h(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(m)))),l}(f,j,O,N));let D=[f,...L],I=await x(t,S),U=[],F=(null==(r=c.flip)?void 0:r.overflows)||[];if(E&&U.push(I[M]),_){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=y(v(e)),l=g(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(a=b(a)),[a,b(a)]}(u,s,N);U.push(I[e[0]],I[e[1]])}if(F=[...F,{placement:u,overflows:U}],!U.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=D[e];if(t){let n="alignment"===_&&C!==v(t),r=(null==(a=F[0])?void 0:a.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:F},reset:{placement:t}}}let n=null==(l=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(T){case"bestFit":{let e=null==(i=F.filter(e=>{if(k){let t=v(e.placement);return t===C||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=f}if(u!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:u,rects:c,platform:s,elements:f}=t,{apply:y=()=>{},...g}=d(e,t),m=await x(t,g),b=p(u),w=h(u),R="y"===v(u),{width:E,height:_}=c.floating;"top"===b||"bottom"===b?(o=b,i=w===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(i=b,o="end"===w?"top":"bottom");let P=_-m.top-m.bottom,T=E-m.left-m.right,O=l(_-m[o],P),j=l(E-m[i],T),S=!t.middlewareData.shift,M=O,C=j;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(M=P),S&&!w){let e=a(m.left,0),t=a(m.right,0),n=a(m.top,0),r=a(m.bottom,0);R?C=E-2*(0!==e||0!==t?e+t:a(m.left,m.right)):M=_-2*(0!==n||0!==r?n+r:a(m.top,m.bottom))}await y({...t,availableWidth:C,availableHeight:M});let A=await s.getDimensions(f.floating);return E!==A.width||_!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eR=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=P(await x(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=P(await x(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...ey(e),options:[e,t]});var e_=n(14163),ex=n(60687),eP=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,ex.jsx)(e_.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,ex.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eP.displayName="Arrow";var eT=n(98599),eO=n(11273),ej=n(13495),eS=n(66156),eM=n(18853),eC="Popper",[eA,eN]=(0,eO.A)(eC),[eL,ek]=eA(eC),eD=e=>{let{__scopePopper:t,children:n}=e,[o,l]=r.useState(null);return(0,ex.jsx)(eL,{scope:t,anchor:o,onAnchorChange:l,children:n})};eD.displayName=eC;var eI="PopperAnchor",eU=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...l}=e,a=ek(eI,n),i=r.useRef(null),u=(0,eT.s)(t,i);return r.useEffect(()=>{a.onAnchorChange(o?.current||i.current)}),o?null:(0,ex.jsx)(e_.sG.div,{...l,ref:u})});eU.displayName=eI;var eF="PopperContent",[eH,eB]=eA(eF),ez=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:i=0,align:c="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:y="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:m,...b}=e,w=ek(eF,n),[R,E]=r.useState(null),_=(0,eT.s)(t,e=>E(e)),[x,P]=r.useState(null),T=(0,eM.X)(x),O=T?.width??0,j=T?.height??0,S="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],A=M.length>0,N={padding:S,boundary:M.filter(eV),altBoundary:A},{refs:L,floatingStyles:k,placement:D,isPositioned:I,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:l,elements:{reference:a,floating:i}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ef(p,o)||h(o);let[y,g]=r.useState(null),[v,m]=r.useState(null),b=r.useCallback(e=>{e!==_.current&&(_.current=e,g(e))},[]),w=r.useCallback(e=>{e!==x.current&&(x.current=e,m(e))},[]),R=a||y,E=i||v,_=r.useRef(null),x=r.useRef(null),P=r.useRef(f),T=null!=c,O=eh(c),j=eh(l),S=eh(s),M=r.useCallback(()=>{if(!_.current||!x.current)return;let e={placement:t,strategy:n,middleware:p};j.current&&(e.platform=j.current),eu(_.current,x.current,e).then(e=>{let t={...e,isPositioned:!1!==S.current};C.current&&!ef(P.current,t)&&(P.current=t,ec.flushSync(()=>{d(t)}))})},[p,t,n,j,S]);es(()=>{!1===s&&P.current.isPositioned&&(P.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let C=r.useRef(!1);es(()=>(C.current=!0,()=>{C.current=!1}),[]),es(()=>{if(R&&(_.current=R),E&&(x.current=E),R&&E){if(O.current)return O.current(R,E,M);M()}},[R,E,M,O,T]);let A=r.useMemo(()=>({reference:_,floating:x,setReference:b,setFloating:w}),[b,w]),N=r.useMemo(()=>({reference:R,floating:E}),[R,E]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=ep(N.floating,f.x),r=ep(N.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,N.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:M,refs:A,elements:N,floatingStyles:L}),[f,M,A,N,L])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(e),h=i||c?[...p?K(p):[],...K(t)]:[];h.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let y=p&&f?function(e,t){let n,r=null,o=C(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),i();let d=e.getBoundingClientRect(),{left:p,top:h,width:y,height:g}=d;if(s||t(),!y||!g)return;let v=u(h),m=u(o.clientWidth-(p+y)),b={rootMargin:-v+"px "+-m+"px "+-u(o.clientHeight-(h+g))+"px "+-u(p)+"px",threshold:a(0,l(1,f))||1},w=!0;function R(t){let r=t[0].intersectionRatio;if(r!==f){if(!w)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ea(d,e.getBoundingClientRect())||c(),w=!1}try{r=new IntersectionObserver(R,{...b,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(R,b)}r.observe(e)}(!0),i}(p,n):null,g=-1,v=null;s&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!d&&v.observe(p),v.observe(t));let m=d?Z(e):null;return d&&function t(){let r=Z(e);m&&!ea(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{i&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==y||y(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:w.anchor},middleware:[eg({mainAxis:i+j,alignmentAxis:s}),d&&ev({mainAxis:!0,crossAxis:!1,limiter:"partial"===y?em():void 0,...N}),d&&eb({...N}),ew({...N,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:l}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${l}px`)}}),x&&eE({element:x,padding:f}),eq({arrowWidth:O,arrowHeight:j}),g&&eR({strategy:"referenceHidden",...N})]}),[F,H]=eY(D),B=(0,ej.c)(m);(0,eS.N)(()=>{I&&B?.()},[I,B]);let z=U.arrow?.x,W=U.arrow?.y,G=U.arrow?.centerOffset!==0,[V,Y]=r.useState();return(0,eS.N)(()=>{R&&Y(window.getComputedStyle(R).zIndex)},[R]),(0,ex.jsx)("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...k,transform:I?k.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:V,"--radix-popper-transform-origin":[U.transformOrigin?.x,U.transformOrigin?.y].join(" "),...U.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,ex.jsx)(eH,{scope:n,placedSide:F,onArrowChange:P,arrowX:z,arrowY:W,shouldHideArrow:G,children:(0,ex.jsx)(e_.sG.div,{"data-side":F,"data-align":H,...b,ref:_,style:{...b.style,animation:I?void 0:"none"}})})})});ez.displayName=eF;var eW="PopperArrow",eK={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eB(eW,n),l=eK[o.placedSide];return(0,ex.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,ex.jsx)(eP,{...r,ref:t,style:{...r.style,display:"block"}})})});function eV(e){return null!==e}eG.displayName=eW;var eq=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,l=o.arrow?.centerOffset!==0,a=l?0:e.arrowWidth,i=l?0:e.arrowHeight,[u,c]=eY(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+i/2,p="",h="";return"bottom"===u?(p=l?s:`${f}px`,h=`${-i}px`):"top"===u?(p=l?s:`${f}px`,h=`${r.floating.height+i}px`):"right"===u?(p=`${-i}px`,h=l?s:`${d}px`):"left"===u&&(p=`${r.floating.width+i}px`,h=l?s:`${d}px`),{data:{x:p,y:h}}}});function eY(e){let[t,n="center"]=e.split("-");return[t,n]}var e$=eD,eX=eU,eZ=ez,eJ=eG},56928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return l}});let r=n(41500),o=n(33898);function l(e,t,n,l,a){let{tree:i,seedData:u,head:c,isRootRender:s}=l;if(null===u)return!1;if(s){let o=u[1];n.loading=u[3],n.rsc=o,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,i,u,c,a)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,o.fillCacheWithNewSubTreeData)(e,n,t,l,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57379:(e,t,n)=>{e.exports=n(53332)},58887:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return l}});let r=n(70642);function o(e){return void 0!==e}function l(e,t){var n,l;let a=null==(n=t.shouldScroll)||n,i=e.nextUrl;if(o(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?i=n:i||(i=e.canonicalUrl)}return{canonicalUrl:o(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:o(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:o(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:o(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!o(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(l=null==t?void 0:t.scrollableSegments)?l:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:o(t.patchedTree)?t.patchedTree:e.tree,nextUrl:i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,n)=>{n.r(t),n.d(t,{_:()=>o});var r=0;function o(e){return"__private_"+r+++"_"+e}},61794:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return l}});let r=n(79289),o=n(26736);function l(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},62369:(e,t,n)=>{n.d(t,{b:()=>c});var r=n(43210),o=n(14163),l=n(60687),a="horizontal",i=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=a,...c}=e,s=(n=u,i.includes(n))?u:a;return(0,l.jsx)(o.sG.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...c,ref:t})});u.displayName="Separator";var c=u},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,l=new WeakMap,a={},i=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],f=[],d=new Set,p=new Set(c),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};c.forEach(h);var y=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))y(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,i=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,i),s.set(e,u),f.push(e),1===i&&a&&l.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return y(t),d.clear(),i++,function(){f.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(l.has(e)||e.removeAttribute(r),l.delete(e)),a||e.removeAttribute(n)}),--i||(o=new WeakMap,o=new WeakMap,l=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),l=t||r(e);return l?(o.push.apply(o,Array.from(l.querySelectorAll("[aria-live], script"))),c(o,l,n,"aria-hidden")):function(){return null}}},63690:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return v},dispatchTraverseAction:function(){return m},getCurrentAppRouterState:function(){return y},publicAppRouterInstance:function(){return b}});let r=n(59154),o=n(8830),l=n(43210),a=n(91992);n(50593);let i=n(19129),u=n(96127),c=n(89752),s=n(75076),f=n(73406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:n,setState:r}=e,o=t.state;t.pending=n;let l=n.payload,i=t.action(o,l);function u(e){n.discarded||(t.state=e,d(t,r),n.resolve(e))}(0,a.isThenable)(i)?i.then(u,e=>{d(t,r),n.reject(e)}):u(i)}function h(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let o={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{o={resolve:e,reject:t}});(0,l.startTransition)(()=>{n(e)})}let a={payload:t,next:null,resolve:o.resolve,reject:o.reject};null===e.pending?(e.last=a,p({actionQueue:e,action:a,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:a,setState:n})):(null!==e.last&&(e.last.next=a),e.last=a)})(n,e,t),action:async(e,t)=>(0,o.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function y(){return null}function g(){return null}function v(e,t,n,o){let l=new URL((0,u.addBasePath)(e),location.href);(0,f.setLinkForCurrentNavigation)(o);(0,i.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:l,isExternalUrl:(0,c.isExternalURL)(l),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function m(e,t){(0,i.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),o=(0,c.createPrefetchURL)(e);if(null!==o){var l;(0,s.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:o,kind:null!=(l=null==t?void 0:t.kind)?l:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,l.startTransition)(()=>{var n;v(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,l.startTransition)(()=>{var n;v(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,l.startTransition)(()=>{(0,i.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64398:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65551:(e,t,n)=>{n.d(t,{i:()=>i});var r,o=n(43210),l=n(66156),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function i({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[l,i,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),l=o.useRef(n),i=o.useRef(t);return a(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==n&&(i.current?.(n),l.current=n)},[n,l]),[n,r,i]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else i(t)},[c,e,i,u])]}Symbol("RADIX:SYNC_STATE")},65951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[l,a]=n,[i,u]=t;return(0,o.matchSegment)(i,l)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),a[u]):!!Array.isArray(i)}}});let r=n(74007),o=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],o=t.parallelRoutes,a=new Map(o);for(let t in r){let n=r[t],i=n[0],u=(0,l.createRouterCacheKey)(i),c=o.get(t);if(void 0!==c){let r=c.get(u);if(void 0!==r){let o=e(r,n),l=new Map(c);l.set(u,o),a.set(t,l)}}}let i=t.rsc,u=v(i)&&"pending"===i.status;return{lazyData:null,rsc:i,head:t.head,prefetchHead:u?t.prefetchHead:[null,null],prefetchRsc:u?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let r=n(83913),o=n(14077),l=n(33123),a=n(2030),i=n(5334),u={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,n,a,i,c,d,p,h){return function e(t,n,a,i,c,d,p,h,y,g,v){let m=a[1],b=i[1],w=null!==d?d[2]:null;c||!0===i[4]&&(c=!0);let R=n.parallelRoutes,E=new Map(R),_={},x=null,P=!1,T={};for(let n in b){let a,i=b[n],f=m[n],d=R.get(n),O=null!==w?w[n]:null,j=i[0],S=g.concat([n,j]),M=(0,l.createRouterCacheKey)(j),C=void 0!==f?f[0]:void 0,A=void 0!==d?d.get(M):void 0;if(null!==(a=j===r.DEFAULT_SEGMENT_KEY?void 0!==f?{route:f,node:null,dynamicRequestTree:null,children:null}:s(t,f,i,A,c,void 0!==O?O:null,p,h,S,v):y&&0===Object.keys(i[1]).length?s(t,f,i,A,c,void 0!==O?O:null,p,h,S,v):void 0!==f&&void 0!==C&&(0,o.matchSegment)(j,C)&&void 0!==A&&void 0!==f?e(t,A,f,i,c,O,p,h,y,S,v):s(t,f,i,A,c,void 0!==O?O:null,p,h,S,v))){if(null===a.route)return u;null===x&&(x=new Map),x.set(n,a);let e=a.node;if(null!==e){let t=new Map(d);t.set(M,e),E.set(n,t)}let t=a.route;_[n]=t;let r=a.dynamicRequestTree;null!==r?(P=!0,T[n]=r):T[n]=t}else _[n]=i,T[n]=i}if(null===x)return null;let O={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:E,navigatedAt:t};return{route:f(i,_),node:O,dynamicRequestTree:P?f(i,T):null,children:x}}(e,t,n,a,!1,i,c,d,p,[],h)}function s(e,t,n,r,o,c,s,p,h,y){return!o&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,n))?u:function e(t,n,r,o,a,u,c,s){let p,h,y,g,v=n[1],m=0===Object.keys(v).length;if(void 0!==r&&r.navigatedAt+i.DYNAMIC_STALETIME_MS>t)p=r.rsc,h=r.loading,y=r.head,g=r.navigatedAt;else if(null===o)return d(t,n,null,a,u,c,s);else if(p=o[1],h=o[3],y=m?a:null,g=t,o[4]||u&&m)return d(t,n,o,a,u,c,s);let b=null!==o?o[2]:null,w=new Map,R=void 0!==r?r.parallelRoutes:null,E=new Map(R),_={},x=!1;if(m)s.push(c);else for(let n in v){let r=v[n],o=null!==b?b[n]:null,i=null!==R?R.get(n):void 0,f=r[0],d=c.concat([n,f]),p=(0,l.createRouterCacheKey)(f),h=e(t,r,void 0!==i?i.get(p):void 0,o,a,u,d,s);w.set(n,h);let y=h.dynamicRequestTree;null!==y?(x=!0,_[n]=y):_[n]=r;let g=h.node;if(null!==g){let e=new Map;e.set(p,g),E.set(n,e)}}return{route:n,node:{lazyData:null,rsc:p,prefetchRsc:null,head:y,prefetchHead:null,loading:h,parallelRoutes:E,navigatedAt:g},dynamicRequestTree:x?f(n,_):null,children:w}}(e,n,r,c,s,p,h,y)}function f(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function d(e,t,n,r,o,a,i){let u=f(t,t[1]);return u[3]="refetch",{route:t,node:function e(t,n,r,o,a,i,u){let c=n[1],s=null!==r?r[2]:null,f=new Map;for(let n in c){let r=c[n],d=null!==s?s[n]:null,p=r[0],h=i.concat([n,p]),y=(0,l.createRouterCacheKey)(p),g=e(t,r,void 0===d?null:d,o,a,h,u),v=new Map;v.set(y,g),f.set(n,v)}let d=0===f.size;d&&u.push(i);let p=null!==r?r[1]:null,h=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:f,prefetchRsc:void 0!==p?p:null,prefetchHead:d?o:[null,null],loading:void 0!==h?h:null,rsc:m(),head:d?m():null,navigatedAt:t}}(e,t,n,r,o,a,i),dynamicRequestTree:u,children:null}}function p(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:a,head:i}=t;a&&function(e,t,n,r,a){let i=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],l=i.children;if(null!==l){let e=l.get(n);if(void 0!==e){let t=e.route[0];if((0,o.matchSegment)(r,t)){i=e;continue}}}return}!function e(t,n,r,a){if(null===t.dynamicRequestTree)return;let i=t.children,u=t.node;if(null===i){null!==u&&(function e(t,n,r,a,i){let u=n[1],c=r[1],s=a[2],f=t.parallelRoutes;for(let t in u){let n=u[t],r=c[t],a=s[t],d=f.get(t),p=n[0],h=(0,l.createRouterCacheKey)(p),g=void 0!==d?d.get(h):void 0;void 0!==g&&(void 0!==r&&(0,o.matchSegment)(p,r[0])&&null!=a?e(g,n,r,a,i):y(n,g,null))}let d=t.rsc,p=a[1];null===d?t.rsc=p:v(d)&&d.resolve(p);let h=t.head;v(h)&&h.resolve(i)}(u,t.route,n,r,a),t.dynamicRequestTree=null);return}let c=n[1],s=r[2];for(let t in n){let n=c[t],r=s[t],l=i.get(t);if(void 0!==l){let t=l.route[0];if((0,o.matchSegment)(n[0],t)&&null!=r)return e(l,n,r,a)}}}(i,n,r,a)}(e,n,r,a,i)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)y(e.route,n,t);else for(let e of r.values())h(e,t);e.dynamicRequestTree=null}function y(e,t,n){let r=e[1],o=t.parallelRoutes;for(let e in r){let t=r[e],a=o.get(e);if(void 0===a)continue;let i=t[0],u=(0,l.createRouterCacheKey)(i),c=a.get(u);void 0!==c&&y(t,c,n)}let a=t.rsc;v(a)&&(null===n?a.resolve(null):a.reject(n));let i=t.head;v(i)&&i.resolve(null)}let g=Symbol();function v(e){return e&&e.tag===g}function m(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=g,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{}},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},70642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return s},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],l=Array.isArray(t),a=l?t[1]:t;!a||a.startsWith(o.PAGE_SEGMENT_KEY)||(l&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):l&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),o=n(83913),l=n(14077),a=e=>"/"===e[0]?e.slice(1):e,i=e=>"string"==typeof e?"children"===e?"":e:e[1];function u(e){return e.reduce((e,t)=>""===(t=a(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===o.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(o.PAGE_SEGMENT_KEY))return"";let l=[i(n)],a=null!=(t=e[1])?t:{},s=a.children?c(a.children):void 0;if(void 0!==s)l.push(s);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let n=c(t);void 0!==n&&l.push(n)}return u(l)}function s(e,t){let n=function e(t,n){let[o,a]=t,[u,s]=n,f=i(o),d=i(u);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>f.startsWith(e)||d.startsWith(e)))return"";if(!(0,l.matchSegment)(o,u)){var p;return null!=(p=c(n))?p:""}for(let t in a)if(s[t]){let n=e(a[t],s[t]);if(null!==n)return i(u)+"/"+n}return null}(e,t);return null==n||"/"===n?n:u(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return u},mountFormInstance:function(){return m},mountLinkInstance:function(){return v},onLinkVisibilityChanged:function(){return w},onNavigationIntent:function(){return R},pingVisibleLinks:function(){return _},setLinkForCurrentNavigation:function(){return s},unmountLinkForCurrentNavigation:function(){return f},unmountPrefetchableInstance:function(){return b}}),n(63690);let r=n(89752),o=n(59154),l=n(50593),a=n(43210),i=null,u={pending:!0},c={pending:!1};function s(e){(0,a.startTransition)(()=>{null==i||i.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(u),i=e})}function f(e){i===e&&(i=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;w(t.target,e)}},{rootMargin:"200px"}):null;function y(e,t){void 0!==d.get(e)&&b(e),d.set(e,t),null!==h&&h.observe(e)}function g(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function v(e,t,n,r,o,l){if(o){let o=g(t);if(null!==o){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:l};return y(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:l}}function m(e,t,n,r){let o=g(t);null!==o&&y(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:null})}function b(e){let t=d.get(e);if(void 0!==t){d.delete(e),p.delete(t);let n=t.prefetchTask;null!==n&&(0,l.cancelPrefetchTask)(n)}null!==h&&h.unobserve(e)}function w(e,t){let n=d.get(e);void 0!==n&&(n.isVisible=t,t?p.add(n):p.delete(n),E(n))}function R(e,t){let n=d.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,E(n))}function E(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,l.cancelPrefetchTask)(t);return}}function _(e,t){let n=(0,l.getCurrentCacheVersion)();for(let r of p){let a=r.prefetchTask;if(null!==a&&r.cacheVersion===n&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,l.cancelPrefetchTask)(a);let i=(0,l.createCacheKey)(r.prefetchHref,e),u=r.wasHoveredOrTouched?l.PrefetchPriority.Intent:l.PrefetchPriority.Default;r.prefetchTask=(0,l.schedulePrefetchTask)(i,t,r.kind===o.PrefetchKind.FULL,u),r.cacheVersion=(0,l.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return l},prefetchReducer:function(){return a}});let r=n(5144),o=n(5334),l=new r.PromiseQueue(5),a=function(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,o.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function l(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return l},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},77022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let r=n(43210),o=n(51215),l="next-route-announcer";function a(e){let{tree:t}=e,[n,a]=(0,r.useState)(null);(0,r.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(l)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(l);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(l)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,u]=(0,r.useState)(""),c=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&u(e),c.current=e},[t]),n?(0,o.createPortal)(i,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let r=n(59008),o=n(57391),l=n(86770),a=n(2030),i=n(25232),u=n(59435),c=n(41500),s=n(89752),f=n(96493),d=n(68214),p=n(22308);function h(e,t){let{origin:n}=t,h={},y=e.canonicalUrl,g=e.tree;h.preserveCustomHistoryState=!1;let v=(0,s.createEmptyCacheNode)(),m=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);v.lazyData=(0,r.fetchServerResponse)(new URL(y,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:m?e.nextUrl:null});let b=Date.now();return v.lazyData.then(async n=>{let{flightData:r,canonicalUrl:s}=n;if("string"==typeof r)return(0,i.handleExternalUrl)(e,h,r,e.pushRef.pendingPush);for(let n of(v.lazyData=null,r)){let{tree:r,seedData:u,head:d,isRootRender:w}=n;if(!w)return console.log("REFRESH FAILED"),e;let R=(0,l.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===R)return(0,f.handleSegmentMismatch)(e,t,r);if((0,a.isNavigatingToNewRootLayout)(g,R))return(0,i.handleExternalUrl)(e,h,y,e.pushRef.pendingPush);let E=s?(0,o.createHrefFromUrl)(s):void 0;if(s&&(h.canonicalUrl=E),null!==u){let e=u[1],t=u[3];v.rsc=e,v.prefetchRsc=null,v.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(b,v,void 0,r,u,d,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:R,updatedCache:v,includeNextUrl:m,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=v,h.patchedTree=R,g=R}return(0,u.handleMutable)(e,h)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return m},MissingStaticPage:function(){return v},NormalizeError:function(){return y},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return i},isAbsoluteUrl:function(){return l},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),l=0;l<r;l++)o[l]=arguments[l];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class m extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},79410:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},84949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return m}});let r=n(40740),o=n(60687),l=r._(n(43210)),a=n(30195),i=n(22142),u=n(59154),c=n(53038),s=n(79289),f=n(96127);n(50148);let d=n(73406),p=n(61794),h=n(63690);function y(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){let t,n,r,[a,g]=(0,l.useOptimistic)(d.IDLE_LINK_STATUS),m=(0,l.useRef)(null),{href:b,as:w,children:R,prefetch:E=null,passHref:_,replace:x,shallow:P,scroll:T,onClick:O,onMouseEnter:j,onTouchStart:S,legacyBehavior:M=!1,onNavigate:C,ref:A,unstable_dynamicOnHover:N,...L}=e;t=R,M&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let k=l.default.useContext(i.AppRouterContext),D=!1!==E,I=null===E?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:U,as:F}=l.default.useMemo(()=>{let e=y(b);return{href:e,as:w?y(w):e}},[b,w]);M&&(n=l.default.Children.only(t));let H=M?n&&"object"==typeof n&&n.ref:A,B=l.default.useCallback(e=>(null!==k&&(m.current=(0,d.mountLinkInstance)(e,U,k,I,D,g)),()=>{m.current&&((0,d.unmountLinkForCurrentNavigation)(m.current),m.current=null),(0,d.unmountPrefetchableInstance)(e)}),[D,U,k,I,g]),z={ref:(0,c.useMergedRef)(B,H),onClick(e){M||"function"!=typeof O||O(e),M&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),k&&(e.defaultPrevented||function(e,t,n,r,o,a,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),l.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==a||a,r.current)})}}(e,U,F,m,x,T,C))},onMouseEnter(e){M||"function"!=typeof j||j(e),M&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),k&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){M||"function"!=typeof S||S(e),M&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),k&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,s.isAbsoluteUrl)(F)?z.href=F:M&&!_&&("a"!==n.type||"href"in n.props)||(z.href=(0,f.addBasePath)(F)),r=M?l.default.cloneElement(n,z):(0,o.jsx)("a",{...L,...z,children:t}),(0,o.jsx)(v.Provider,{value:a,children:r})}n(32708);let v=(0,l.createContext)(d.IDLE_LINK_STATUS),m=()=>(0,l.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,u){let c,[s,f,d,p,h]=n;if(1===t.length){let e=i(n,r);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,u),e}let[y,g]=t;if(!(0,l.matchSegment)(y,s))return null;if(2===t.length)c=i(f[g],r);else if(null===(c=e((0,o.getNextFlightSegmentPath)(t),f[g],r,u)))return null;let v=[t[0],{...f,[g]:c},d,p];return h&&(v[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(v,u),v}}});let r=n(83913),o=n(74007),l=n(14077),a=n(22308);function i(e,t){let[n,o]=e,[a,u]=t;if(a===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,l.matchSegment)(n,a)){let t={};for(let e in o)void 0!==u[e]?t[e]=i(o[e],u[e]):t[e]=o[e];for(let e in u)t[e]||(t[e]=u[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return O},default:function(){return N},isExternalURL:function(){return T}});let r=n(40740),o=n(60687),l=r._(n(43210)),a=n(22142),i=n(59154),u=n(57391),c=n(10449),s=n(19129),f=r._(n(35656)),d=n(35416),p=n(96127),h=n(77022),y=n(67086),g=n(44397),v=n(89330),m=n(25942),b=n(26736),w=n(70642),R=n(12776),E=n(63690),_=n(36875),x=n(97860);n(73406);let P={};function T(e){return e.origin!==window.location.origin}function O(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return T(t)?null:t}function j(e){let{appRouterState:t}=e;return(0,l.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,o={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(o,"",r)):window.history.replaceState(o,"",r)},[t]),(0,l.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function M(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function C(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,o=null!==r?r:n;return(0,l.useDeferredValue)(n,o)}function A(e){let t,{actionQueue:n,assetPrefix:r,globalError:u}=e,d=(0,s.useActionQueue)(n),{canonicalUrl:p}=d,{searchParams:R,pathname:T}=(0,l.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,m.removeBasePath)(e.pathname):e.pathname}},[p]);(0,l.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(P.pendingMpaPath=void 0,(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,l.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,x.isRedirectError)(t)){e.preventDefault();let n=(0,_.getURLFromRedirectError)(t);(0,_.getRedirectTypeFromError)(t)===x.RedirectType.push?E.publicAppRouterInstance.push(n,{}):E.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:O}=d;if(O.mpaNavigation){if(P.pendingMpaPath!==p){let e=window.location;O.pendingPush?e.assign(p):e.replace(p),P.pendingMpaPath=p}(0,l.use)(v.unresolvedThenable)}(0,l.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,l.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=M(t),o&&n(o)),e(t,r,o)},window.history.replaceState=function(e,r,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=M(e),o&&n(o)),t(e,r,o)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,l.startTransition)(()=>{(0,E.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:S,tree:A,nextUrl:N,focusAndScrollRef:L}=d,k=(0,l.useMemo)(()=>(0,g.findHeadInCache)(S,A[1]),[S,A]),I=(0,l.useMemo)(()=>(0,w.getSelectedParams)(A),[A]),U=(0,l.useMemo)(()=>({parentTree:A,parentCacheNode:S,parentSegmentPath:null,url:p}),[A,S,p]),F=(0,l.useMemo)(()=>({tree:A,focusAndScrollRef:L,nextUrl:N}),[A,L,N]);if(null!==k){let[e,n]=k;t=(0,o.jsx)(C,{headCacheNode:e},n)}else t=null;let H=(0,o.jsxs)(y.RedirectBoundary,{children:[t,S.rsc,(0,o.jsx)(h.AppRouterAnnouncer,{tree:A})]});return H=(0,o.jsx)(f.ErrorBoundary,{errorComponent:u[0],errorStyles:u[1],children:H}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(j,{appRouterState:d}),(0,o.jsx)(D,{}),(0,o.jsx)(c.PathParamsContext.Provider,{value:I,children:(0,o.jsx)(c.PathnameContext.Provider,{value:T,children:(0,o.jsx)(c.SearchParamsContext.Provider,{value:R,children:(0,o.jsx)(a.GlobalLayoutRouterContext.Provider,{value:F,children:(0,o.jsx)(a.AppRouterContext.Provider,{value:E.publicAppRouterInstance,children:(0,o.jsx)(a.LayoutRouterContext.Provider,{value:U,children:H})})})})})})]})}function N(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:l}=e;return(0,R.useNavFailureHandler)(),(0,o.jsx)(f.ErrorBoundary,{errorComponent:f.default,children:(0,o.jsx)(A,{actionQueue:t,assetPrefix:l,globalError:[n,r]})})}let L=new Set,k=new Set;function D(){let[,e]=l.default.useState(0),t=L.size;return(0,l.useEffect)(()=>{let n=()=>e(e=>e+1);return k.add(n),t!==L.size&&n(),()=>{k.delete(n)}},[t,e]),[...L].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&k.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95682:(e,t,n)=>{n.d(t,{Ke:()=>E,R6:()=>w,bL:()=>P});var r=n(43210),o=n(70569),l=n(11273),a=n(65551),i=n(66156),u=n(98599),c=n(14163),s=n(46059),f=n(96963),d=n(60687),p="Collapsible",[h,y]=(0,l.A)(p),[g,v]=h(p),m=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:l,disabled:i,onOpenChange:u,...s}=e,[h,y]=(0,a.i)({prop:o,defaultProp:l??!1,onChange:u,caller:p});return(0,d.jsx)(g,{scope:n,disabled:i,contentId:(0,f.B)(),open:h,onOpenToggle:r.useCallback(()=>y(e=>!e),[y]),children:(0,d.jsx)(c.sG.div,{"data-state":x(h),"data-disabled":i?"":void 0,...s,ref:t})})});m.displayName=p;var b="CollapsibleTrigger",w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,l=v(b,n);return(0,d.jsx)(c.sG.button,{type:"button","aria-controls":l.contentId,"aria-expanded":l.open||!1,"data-state":x(l.open),"data-disabled":l.disabled?"":void 0,disabled:l.disabled,...r,ref:t,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});w.displayName=b;var R="CollapsibleContent",E=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=v(R,e.__scopeCollapsible);return(0,d.jsx)(s.C,{present:n||o.open,children:({present:e})=>(0,d.jsx)(_,{...r,ref:t,present:e})})});E.displayName=R;var _=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:l,...a}=e,s=v(R,n),[f,p]=r.useState(o),h=r.useRef(null),y=(0,u.s)(t,h),g=r.useRef(0),m=g.current,b=r.useRef(0),w=b.current,E=s.open||f,_=r.useRef(E),P=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>_.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.N)(()=>{let e=h.current;if(e){P.current=P.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();g.current=t.height,b.current=t.width,_.current||(e.style.transitionDuration=P.current.transitionDuration,e.style.animationName=P.current.animationName),p(o)}},[s.open,o]),(0,d.jsx)(c.sG.div,{"data-state":x(s.open),"data-disabled":s.disabled?"":void 0,id:s.contentId,hidden:!E,...a,ref:y,style:{"--radix-collapsible-content-height":m?`${m}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:E&&l})});function x(e){return e?"open":"closed"}var P=m},95796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return l}});let r=n(98834),o=n(54674);function l(e,t){return(0,o.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return o}});let r=n(25232);function o(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(43210),l=n(66156),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function u(e){let[t,n]=o.useState(a());return(0,l.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},97464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,l){let a=l.length<=2,[i,u]=l,c=(0,o.createRouterCacheKey)(u),s=n.parallelRoutes.get(i),f=t.parallelRoutes.get(i);f&&f!==s||(f=new Map(s),t.parallelRoutes.set(i,f));let d=null==s?void 0:s.get(c),p=f.get(c);if(a){p&&p.lazyData&&p!==d||f.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!d){p||f.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},f.set(c,p)),e(p,d,(0,r.getNextFlightSegmentPath)(l))}}});let r=n(74007),o=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97840:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},97936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let r=n(19169);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:o,hash:l}=(0,r.parsePath)(e);return""+t+n+o+l}}};