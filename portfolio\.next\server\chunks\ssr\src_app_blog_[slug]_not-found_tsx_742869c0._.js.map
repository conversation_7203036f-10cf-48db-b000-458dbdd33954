{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/app/blog/%5Bslug%5D/not-found.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { ArrowLeft, FileX } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-background flex items-center justify-center\">\n      <div className=\"text-center max-w-md mx-auto px-4\">\n        <div className=\"mb-8\">\n          <FileX size={80} className=\"mx-auto text-primary/50 mb-4\" />\n          <h1 className=\"text-4xl font-heading font-bold text-primary mb-2\">\n            Post Not Found\n          </h1>\n          <p className=\"text-lg text-muted-foreground\">\n            The blog post you&apos;re looking for doesn&apos;t exist or has been moved.\n          </p>\n        </div>\n        \n        <div className=\"space-y-4\">\n          <Link href=\"/blog\">\n            <Button className=\"bg-primary hover:bg-primary/90 text-primary-foreground\">\n              <ArrowLeft size={16} className=\"mr-2\" />\n              Back to Blog\n            </Button>\n          </Link>\n          \n          <div>\n            <Link \n              href=\"/#blog\" \n              className=\"text-secondary hover:text-primary transition-colors\"\n            >\n              Or browse featured posts\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wMAAA,CAAA,QAAK;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC3B,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAK/C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;sCAK5C,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}