// API integration for fetching content from CMS
const CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || "http://localhost:3002";

// Types for API responses
export interface BlogPost {
  _id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  thumbnail: string;
  category: string;
  tags: string[];
  featured: boolean;
  status: "draft" | "published" | "archived";
  readTime: number;
  createdAt: string;
  updatedAt: string;
}

export interface Video {
  _id: string;
  id: string;
  title: string;
  slug: string;
  description?: string;
  category?: string;
  tags: string[];
  featured: boolean;
  status: "draft" | "published" | "archived";
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Reel {
  _id: string;
  id: string;
  title: string;
  slug: string;
  thumbnail?: string; // Generated dynamically from YouTube API
  description?: string;
  platform: "youtube";
  embedUrl?: string;
  featured: boolean;
  status: "draft" | "published" | "archived";
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Testimonial {
  _id: string;
  name: string;
  slug: string;
  avatar?: string;
  role: string;
  company?: string;
  email?: string;
  linkedinUrl?: string;
  content: string;
  rating: number;
  featured: boolean;
  status: "draft" | "published" | "archived";
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Client {
  _id: string;
  name: string;
  slug: string;
  logo: string;
  description: string;
  website?: string;
  industry?: string;
  projectType?: string;
  featured: boolean;
  status: "draft" | "published" | "archived";
  order: number;
  createdAt: string;
  updatedAt: string;
}

// API response wrapper
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// Generic fetch function with error handling
async function fetchFromCMS<T>(endpoint: string): Promise<T[]> {
  try {
    const response = await fetch(`${CMS_BASE_URL}/api${endpoint}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      // Add cache control for better performance
      next: { revalidate: 300 }, // Revalidate every 5 minutes
    });

    if (!response.ok) {
      console.error(
        `Failed to fetch ${endpoint}:`,
        response.status,
        response.statusText
      );
      return [];
    }

    const result: ApiResponse<T[]> = await response.json();

    if (!result.success) {
      console.error(`API error for ${endpoint}:`, result.message);
      return [];
    }

    return result.data || [];
  } catch (error) {
    console.error(`Network error fetching ${endpoint}:`, error);
    return [];
  }
}

// Blog API functions
export async function getBlogPosts(): Promise<BlogPost[]> {
  const posts = await fetchFromCMS<BlogPost>("/blog");
  // Only return published posts, sorted by creation date
  return posts
    .filter((post) => post.status === "published")
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
}

export async function getFeaturedBlogPosts(): Promise<BlogPost[]> {
  const posts = await getBlogPosts();
  return posts.filter((post) => post.featured);
}

export async function getBlogPostBySlug(
  slug: string
): Promise<BlogPost | null> {
  try {
    const response = await fetch(`${CMS_BASE_URL}/api/blog/slug/${slug}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      next: { revalidate: 300 },
    });

    if (!response.ok) {
      return null;
    }

    const result: ApiResponse<BlogPost> = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error("Error fetching blog post by slug:", error);
    return null;
  }
}

// Videos API functions
export async function getVideos(): Promise<Video[]> {
  const videos = await fetchFromCMS<Video>("/videos");
  return videos
    .filter((video) => video.status === "published")
    .sort((a, b) => a.order - b.order);
}

export async function getFeaturedVideos(): Promise<Video[]> {
  const videos = await getVideos();
  return videos.filter((video) => video.featured);
}

// Reels API functions
export async function getReels(): Promise<Reel[]> {
  const reels = await fetchFromCMS<Reel>("/reels");
  return reels
    .filter((reel) => reel.status === "published")
    .sort((a, b) => a.order - b.order);
}

export async function getFeaturedReels(): Promise<Reel[]> {
  const reels = await getReels();
  return reels.filter((reel) => reel.featured);
}

// Testimonials API functions
export async function getTestimonials(): Promise<Testimonial[]> {
  const testimonials = await fetchFromCMS<Testimonial>("/testimonials");

  // If CMS returns empty data, use mock data as fallback
  if (testimonials.length === 0) {
    const { testimonials: mockTestimonials } = await import('../data/testimonials');
    return mockTestimonials.map((testimonial, index) => ({
      ...testimonial,
      _id: testimonial.id.toString(),
      slug: testimonial.name.toLowerCase().replace(/\s+/g, '-'),
      rating: testimonial.rating || 5,
      status: 'published' as const,
      order: index + 1,
      featured: index < 3, // First 3 are featured
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));
  }

  return testimonials
    .filter((testimonial) => testimonial.status === "published")
    .sort((a, b) => a.order - b.order);
}

export async function getFeaturedTestimonials(): Promise<Testimonial[]> {
  const testimonials = await getTestimonials();
  return testimonials.filter((testimonial) => testimonial.featured);
}

// Clients API functions
export async function getClients(): Promise<Client[]> {
  const clients = await fetchFromCMS<Client>("/clients");
  return clients
    .filter((client) => client.status === "published")
    .sort((a, b) => a.order - b.order);
}

export async function getFeaturedClients(): Promise<Client[]> {
  const clients = await getClients();
  return clients.filter((client) => client.featured);
}

// Utility functions
export function getYouTubeThumbnail(videoId: string): string {
  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
}

export function generateEmbedUrl(id: string): string {
  // Only YouTube Shorts are supported now
  return `https://www.youtube.com/embed/${id}`;
}

export function getYouTubeShortsThumbnail(videoId: string): string {
  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
}

// Helper function to ensure reels have thumbnails
export function ensureReelThumbnail(reel: Reel): Reel {
  return {
    ...reel,
    thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id),
  };
}

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

export function calculateReadTime(content: string): number {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}
