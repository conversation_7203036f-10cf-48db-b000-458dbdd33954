{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "6U-R90AQmq94v3oZhrW79", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "z2WXuwZtdGk/LJ62+5rY6tUPjYA9705wjsFO5fRpOeU=", "__NEXT_PREVIEW_MODE_ID": "aa87694408ab7174166d3a1df59a3db6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a09fad772fcc435ba4ee5fb8864d58f15be8f6361460a3fa31dc0b8377713dee", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2b4be604c2fcca934451c5e53b5768bc0e3d5af59f7ee21b9107620bc774d64e"}}}, "functions": {}, "sortedMiddleware": ["/"]}