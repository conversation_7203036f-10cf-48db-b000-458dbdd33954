(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{283:(e,r,t)=>{"use strict";t.d(r,{A:()=>l,AuthProvider:()=>s});var n=t(5155),o=t(2115);let a=(0,o.createContext)(void 0);function s(e){let{children:r}=e,[t,s]=(0,o.useState)(null),[l,i]=(0,o.useState)(!0),c=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let r=await e.json();s(r.user)}else s(null)}catch(e){console.error("Auth check failed:",e),s(null)}finally{i(!1)}},u=async(e,r)=>{try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:r})}),n=await t.json();if(t.ok)return s(n.user),{success:!0};return{success:!1,error:n.error||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error"}}},h=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{s(null)}};return(0,o.useEffect)(()=>{c()},[]),(0,n.jsx)(a.Provider,{value:{user:t,loading:l,login:u,logout:h,checkAuth:c},children:r})}function l(){let e=(0,o.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},324:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,1666,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,283))},347:()=>{},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}}},e=>{var r=r=>e(e.s=r);e.O(0,[2258,8441,1684,7358],()=>r(324)),_N_E=e.O()}]);