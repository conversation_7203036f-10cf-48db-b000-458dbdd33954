'use client';

import { useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronDown, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Hero() {
  const typedTextRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    // Simple typing animation
    const texts = ['Video Editor', 'Storyteller', 'Motion Graphics Artist', 'Visual Craftsman'];
    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    let currentText = '';

    const typeText = () => {
      const fullText = texts[textIndex];

      if (isDeleting) {
        currentText = fullText.substring(0, charIndex - 1);
        charIndex--;
      } else {
        currentText = fullText.substring(0, charIndex + 1);
        charIndex++;
      }

      if (typedTextRef.current) {
        typedTextRef.current.textContent = currentText;
      }

      let typeSpeed = isDeleting ? 40 : 60;

      if (!isDeleting && charIndex === fullText.length) {
        typeSpeed = 1500; // Pause at end
        isDeleting = true;
      } else if (isDeleting && charIndex === 0) {
        isDeleting = false;
        textIndex = (textIndex + 1) % texts.length;
        typeSpeed = 500; // Pause before next word
      }

      setTimeout(typeText, typeSpeed);
    };

    typeText();
  }, []);

  const handleScrollToWork = () => {
    const element = document.getElementById('featured-work');
    if (element) {
      const headerOffset = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  const handleScrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      const headerOffset = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center bg-background overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-accent/20 to-transparent transform skew-x-12"></div>
      </div>

      <div className="container mx-auto container-padding section-padding relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          {/* Text Content */}
          <div className="flex-1 max-w-2xl text-center lg:text-left">
            <h1 className="heading-primary mb-4 animate-in slide-in-from-left duration-1000">
              Uttam Rimal
            </h1>
            <h2 className="heading-tertiary text-accent mb-6 animate-in slide-in-from-left duration-1000 delay-200">
              Creative Video Editor & Storyteller
            </h2>
            <p className="text-body-large mb-8 animate-in slide-in-from-left duration-1000 delay-400">
              Transforming footage into compelling narratives. I&apos;m a{' '}
              <span ref={typedTextRef} className="text-accent font-semibold"></span>
              <span className="animate-pulse">_</span>
              {' '}with over a year of experience crafting visually stunning videos that engage and inspire.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-8 animate-in slide-in-from-left duration-1000 delay-600">
              <Button
                onClick={handleScrollToWork}
                variant="secondary"
                className="btn-secondary"
              >
                View My Work
              </Button>
              <Button
                variant="outline"
                onClick={handleScrollToContact}
                className="hover-lift"
              >
                Get a Quote
              </Button>
            </div>

            {/* Social Links */}
            <div className="flex justify-center lg:justify-start gap-4 animate-in slide-in-from-left duration-1000 delay-800">
              {[
                { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn', color: 'hover:text-[#0077B5]' },
                { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram', color: 'hover:text-[#E4405F]' },
                { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube', color: 'hover:text-[#FF0000]' },
                { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook', color: 'hover:text-[#1877F2]' },
              ].map(({ icon: Icon, href, label, color }) => (
                <Link
                  key={label}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`w-12 h-12 bg-foreground/10 hover:bg-accent text-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg ${color}`}
                  aria-label={label}
                >
                  <Icon size={20} />
                </Link>
              ))}
            </div>

          </div>

          {/* Profile Image */}
          <div className="flex-1 max-w-md animate-in slide-in-from-right duration-1000 delay-300">
            <div className="relative">
              <div className="w-80 h-80 mx-auto relative">
                <Image
                  src="/images/uttam_rimal.jpg"
                  alt="Uttam Rimal - Professional Video Editor"
                  fill
                  className="rounded-full object-cover border-4 border-foreground/20 shadow-2xl animate-float"
                  priority
                />
              </div>
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-accent/20 rounded-full blur-xl animate-pulse-slow"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-accent/30 rounded-full blur-xl animate-glow"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Down Arrow */}
      <Button
        variant="ghost"
        size="icon"
        onClick={handleScrollToWork}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-foreground/80 hover:text-foreground transition-colors duration-300 animate-bounce-slow w-auto h-auto p-2"
        aria-label="Scroll down"
      >
        <ChevronDown size={32} />
      </Button>
    </section>
  );
}
