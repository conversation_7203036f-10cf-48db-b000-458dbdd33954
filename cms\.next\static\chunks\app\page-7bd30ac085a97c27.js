(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>c,AuthProvider:()=>u});var r=s(5155),a=s(2115);let n=(0,a.createContext)(void 0);function u(e){let{children:t}=e,[s,u]=(0,a.useState)(null),[c,i]=(0,a.useState)(!0),o=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();u(t.user)}else u(null)}catch(e){console.error("Auth check failed:",e),u(null)}finally{i(!1)}},l=async(e,t)=>{try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await s.json();if(s.ok)return u(r.user),{success:!0};return{success:!1,error:r.error||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error"}}},d=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{u(null)}};return(0,a.useEffect)(()=>{o()},[]),(0,r.jsx)(n.Provider,{value:{user:s,loading:c,login:l,logout:d,checkAuth:o},children:t})}function c(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(5155),a=s(2115),n=s(5695),u=s(283);function c(){let{user:e,loading:t}=(0,u.A)(),s=(0,n.useRouter)(),[c,i]=(0,a.useState)("checking");(0,a.useEffect)(()=>{o()},[]);let o=async()=>{try{let e=await fetch("/api/status"),t=await e.json();t.success&&"connected"===t.status.database&&"exists"===t.status.adminUser?i("ready"):i("setup-needed")}catch(e){i("setup-needed")}};return((0,a.useEffect)(()=>{t||"checking"===c||("setup-needed"===c?s.push("/setup"):e?s.push("/dashboard"):s.push("/login"))},[e,t,c,s]),t||"checking"===c)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"checking"===c?"Checking system status...":"Loading..."})]})}):null}},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},6521:(e,t,s)=>{Promise.resolve().then(s.bind(s,3792))}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(6521)),_N_E=e.O()}]);