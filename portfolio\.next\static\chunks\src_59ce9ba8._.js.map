{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/ScrollToTop.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <Button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n      aria-label=\"Scroll to top\"\n    >\n      <ArrowUp size={20} />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,OAAO,WAAW,GAAG,KAAK;wBAC5B,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,6LAAC,+MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;;;;;;AAGrB;GApCwB;KAAA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/api.ts"], "sourcesContent": ["// API integration for fetching content from CMS\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || \"http://localhost:3002\";\n\n// Types for API responses\nexport interface BlogPost {\n  _id: string;\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  thumbnail: string;\n  category: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  readTime: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Video {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  description?: string;\n  category?: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reel {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  thumbnail?: string; // Generated dynamically from YouTube API\n  description?: string;\n  platform: \"youtube\";\n  embedUrl?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Testimonial {\n  _id: string;\n  name: string;\n  slug: string;\n  avatar?: string;\n  role: string;\n  company?: string;\n  email?: string;\n  linkedinUrl?: string;\n  content: string;\n  rating: number;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Client {\n  _id: string;\n  name: string;\n  slug: string;\n  logo: string;\n  description: string;\n  website?: string;\n  industry?: string;\n  projectType?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n}\n\n// Generic fetch function with error handling\nasync function fetchFromCMS<T>(endpoint: string): Promise<T[]> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api${endpoint}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      // Add cache control for better performance\n      next: { revalidate: 300 }, // Revalidate every 5 minutes\n    });\n\n    if (!response.ok) {\n      console.error(\n        `Failed to fetch ${endpoint}:`,\n        response.status,\n        response.statusText\n      );\n      return [];\n    }\n\n    const result: ApiResponse<T[]> = await response.json();\n\n    if (!result.success) {\n      console.error(`API error for ${endpoint}:`, result.message);\n      return [];\n    }\n\n    return result.data || [];\n  } catch (error) {\n    console.error(`Network error fetching ${endpoint}:`, error);\n    return [];\n  }\n}\n\n// Blog API functions\nexport async function getBlogPosts(): Promise<BlogPost[]> {\n  const posts = await fetchFromCMS<BlogPost>(\"/blog\");\n  // Only return published posts, sorted by creation date\n  return posts\n    .filter((post) => post.status === \"published\")\n    .sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n}\n\nexport async function getFeaturedBlogPosts(): Promise<BlogPost[]> {\n  const posts = await getBlogPosts();\n  return posts.filter((post) => post.featured);\n}\n\nexport async function getBlogPostBySlug(\n  slug: string\n): Promise<BlogPost | null> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/slug/${slug}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      next: { revalidate: 300 },\n    });\n\n    if (!response.ok) {\n      return null;\n    }\n\n    const result: ApiResponse<BlogPost> = await response.json();\n    return result.success ? result.data : null;\n  } catch (error) {\n    console.error(\"Error fetching blog post by slug:\", error);\n    return null;\n  }\n}\n\n// Videos API functions\nexport async function getVideos(): Promise<Video[]> {\n  const videos = await fetchFromCMS<Video>(\"/videos\");\n  return videos\n    .filter((video) => video.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedVideos(): Promise<Video[]> {\n  const videos = await getVideos();\n  return videos.filter((video) => video.featured);\n}\n\n// Reels API functions\nexport async function getReels(): Promise<Reel[]> {\n  const reels = await fetchFromCMS<Reel>(\"/reels\");\n  return reels\n    .filter((reel) => reel.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedReels(): Promise<Reel[]> {\n  const reels = await getReels();\n  return reels.filter((reel) => reel.featured);\n}\n\n// Testimonials API functions\nexport async function getTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await fetchFromCMS<Testimonial>(\"/testimonials\");\n\n  // If CMS returns empty data, use mock data as fallback\n  if (testimonials.length === 0) {\n    const { testimonials: mockTestimonials } = await import('../data/testimonials');\n    return mockTestimonials.map((testimonial, index) => ({\n      ...testimonial,\n      _id: testimonial.id.toString(),\n      slug: testimonial.name.toLowerCase().replace(/\\s+/g, '-'),\n      rating: testimonial.rating || 5,\n      status: 'published' as const,\n      order: index + 1,\n      featured: index < 3, // First 3 are featured\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }));\n  }\n\n  return testimonials\n    .filter((testimonial) => testimonial.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await getTestimonials();\n  return testimonials.filter((testimonial) => testimonial.featured);\n}\n\n// Clients API functions\nexport async function getClients(): Promise<Client[]> {\n  const clients = await fetchFromCMS<Client>(\"/clients\");\n  return clients\n    .filter((client) => client.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedClients(): Promise<Client[]> {\n  const clients = await getClients();\n  return clients.filter((client) => client.featured);\n}\n\n// Utility functions\nexport function getYouTubeThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\nexport function generateEmbedUrl(id: string): string {\n  // Only YouTube Shorts are supported now\n  return `https://www.youtube.com/embed/${id}`;\n}\n\nexport function getYouTubeShortsThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\n// Helper function to ensure reels have thumbnails\nexport function ensureReelThumbnail(reel: Reel): Reel {\n  return {\n    ...reel,\n    thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id),\n  };\n}\n\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  });\n}\n\nexport function calculateReadTime(content: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = content.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;;;;;;;;;;;;;;;AAC3B;AAArB,MAAM,eAAe,wEAAmC;AA2FxD,6CAA6C;AAC7C,eAAe,aAAgB,QAAgB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,2CAA2C;YAC3C,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CACX,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAC9B,SAAS,MAAM,EACf,SAAS,UAAU;YAErB,OAAO,EAAE;QACX;QAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;QAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,OAAO;YAC1D,OAAO,EAAE;QACX;QAEA,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAuB;IAC3C,uDAAuD;IACvD,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CACH,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;AAEvE;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAEO,eAAe,kBACpB,IAAY;IAEZ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,EAAE,MAAM,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,SAAgC,MAAM,SAAS,IAAI;QACzD,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,GAAG;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM,aAAoB;IACzC,OAAO,OACJ,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK,aACnC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,QAAQ;AAChD;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAmB;IACvC,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAGO,eAAe;IACpB,MAAM,eAAe,MAAM,aAA0B;IAErD,uDAAuD;IACvD,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,MAAM,EAAE,cAAc,gBAAgB,EAAE,GAAG;QAC3C,OAAO,iBAAiB,GAAG,CAAC,CAAC,aAAa,QAAU,CAAC;gBACnD,GAAG,WAAW;gBACd,KAAK,YAAY,EAAE,CAAC,QAAQ;gBAC5B,MAAM,YAAY,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;gBACrD,QAAQ,YAAY,MAAM,IAAI;gBAC9B,QAAQ;gBACR,OAAO,QAAQ;gBACf,UAAU,QAAQ;gBAClB,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEA,OAAO,aACJ,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK,aAC/C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,eAAe,MAAM;IAC3B,OAAO,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,QAAQ;AAClE;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM,aAAqB;IAC3C,OAAO,QACJ,MAAM,CAAC,CAAC,SAAW,OAAO,MAAM,KAAK,aACrC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,OAAO,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,QAAQ;AACnD;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAEO,SAAS,iBAAiB,EAAU;IACzC,wCAAwC;IACxC,OAAO,CAAC,8BAA8B,EAAE,IAAI;AAC9C;AAEO,SAAS,0BAA0B,OAAe;IACvD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAGO,SAAS,oBAAoB,IAAU;IAC5C,OAAO;QACL,GAAG,IAAI;QACP,WAAW,KAAK,SAAS,IAAI,0BAA0B,KAAK,EAAE;IAChE;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,iBAAiB;IACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogDetailHeader.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { ArrowLeft, Calendar, Clock, User, Star } from 'lucide-react';\n// Badge is imported but not used in this component\nimport { formatDate, type BlogPost } from '@/lib/api';\n\ninterface BlogDetailHeaderProps {\n  post: BlogPost;\n}\n\nexport default function BlogDetailHeader({ post }: BlogDetailHeaderProps) {\n  return (\n    <div className=\"relative bg-background border-b border-border py-20\">\n      <div className=\"container mx-auto px-4\">\n        <Link\n          href=\"/blog\"\n          className=\"inline-flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors mb-8 group\"\n        >\n          <ArrowLeft size={20} className=\"group-hover:-translate-x-1 transition-transform duration-300\" />\n          Back to Blog\n        </Link>\n\n        <div className=\"max-w-4xl\">\n          {/* Category and Featured Badges */}\n          <div className=\"flex flex-wrap gap-3 mb-6\">\n            <div className=\"flex items-center gap-2 bg-muted px-4 py-2 rounded-full border\">\n              <span className=\"text-sm font-medium text-foreground\">📝 {post.category}</span>\n            </div>\n            {post.featured && (\n              <div className=\"flex items-center gap-2 bg-accent px-4 py-2 rounded-full border border-accent\">\n                <Star size={14} className=\"text-accent-foreground\" />\n                <span className=\"text-sm font-semibold text-accent-foreground\">Featured</span>\n              </div>\n            )}\n          </div>\n\n          {/* Title */}\n          <h1 className=\"text-3xl md:text-4xl lg:text-5xl font-heading font-bold mb-6 leading-tight text-foreground\">\n            {post.title}\n          </h1>\n\n          {/* Meta Information */}\n          <div className=\"flex flex-wrap items-center gap-4 text-muted-foreground mb-6\">\n            <div className=\"flex items-center gap-2 bg-muted px-3 py-2 rounded-lg border\">\n              <Calendar size={16} />\n              <span className=\"text-sm\">{formatDate(post.createdAt)}</span>\n            </div>\n            <div className=\"flex items-center gap-2 bg-muted px-3 py-2 rounded-lg border\">\n              <Clock size={16} />\n              <span className=\"text-sm\">{post.readTime} min read</span>\n            </div>\n            <div className=\"flex items-center gap-2 bg-muted px-3 py-2 rounded-lg border\">\n              <User size={16} />\n              <span className=\"text-sm\">Uttam Rimal</span>\n            </div>\n          </div>\n\n          {/* Excerpt */}\n          <p className=\"text-xl md:text-2xl text-muted-foreground max-w-3xl leading-relaxed\">\n            {post.excerpt}\n          </p>\n\n          {/* Tags Preview */}\n          <div className=\"flex flex-wrap gap-2 mt-8\">\n            {post.tags.slice(0, 4).map((tag) => (\n              <div key={tag} className=\"bg-secondary/10 border border-secondary/20 px-3 py-1 rounded-full\">\n                <span className=\"text-sm text-secondary\">#{tag}</span>\n              </div>\n            ))}\n            {post.tags.length > 4 && (\n              <div className=\"bg-secondary/10 border border-secondary/20 px-3 py-1 rounded-full\">\n                <span className=\"text-sm text-secondary\">+{post.tags.length - 4} more</span>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA,mDAAmD;AACnD;;;;;AAMe,SAAS,iBAAiB,EAAE,IAAI,EAAyB;IACtE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,6LAAC,mNAAA,CAAA,YAAS;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAiE;;;;;;;8BAIlG,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;4CAAsC;4CAAI,KAAK,QAAQ;;;;;;;;;;;;gCAExE,KAAK,QAAQ,kBACZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;sCAMrE,6LAAC;4BAAG,WAAU;sCACX,KAAK,KAAK;;;;;;sCAIb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAW,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,6LAAC;4CAAK,WAAU;;gDAAW,KAAK,QAAQ;gDAAC;;;;;;;;;;;;;8CAE3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAK9B,6LAAC;4BAAE,WAAU;sCACV,KAAK,OAAO;;;;;;sCAIf,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC;wCAAc,WAAU;kDACvB,cAAA,6LAAC;4CAAK,WAAU;;gDAAyB;gDAAE;;;;;;;uCADnC;;;;;gCAIX,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;4CAAyB;4CAAE,KAAK,IAAI,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhF;KArEwB", "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogFeaturedImage.tsx"], "sourcesContent": ["import Image from 'next/image';\nimport { type BlogPost } from '@/lib/api';\n\ninterface BlogFeaturedImageProps {\n  post: BlogPost;\n}\n\nexport default function BlogFeaturedImage({ post }: BlogFeaturedImageProps) {\n  if (!post.thumbnail) {\n    return null;\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 -mt-8\">\n      {/* Featured Image Container */}\n      <div className=\"relative aspect-video max-h-[500px] overflow-hidden rounded-xl border shadow-lg bg-card\">\n        <Image\n          src={post.thumbnail}\n          alt={post.title}\n          fill\n          className=\"object-cover\"\n          priority\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px\"\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOe,SAAS,kBAAkB,EAAE,IAAI,EAA0B;IACxE,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gBACJ,KAAK,KAAK,SAAS;gBACnB,KAAK,KAAK,KAAK;gBACf,IAAI;gBACJ,WAAU;gBACV,QAAQ;gBACR,OAAM;;;;;;;;;;;;;;;;AAKhB;KApBwB", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40\",\n        info:\n          \"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogDetailContent.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Share2, Check } from 'lucide-react';\nimport { useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { type BlogPost } from '@/lib/api';\n\ninterface BlogDetailContentProps {\n  post: BlogPost;\n}\n\nexport default function BlogDetailContent({ post }: BlogDetailContentProps) {\n  const [copied, setCopied] = useState(false);\n\n  const sharePost = async () => {\n    if (navigator.share) {\n      try {\n        await navigator.share({\n          title: post.title,\n          text: post.excerpt,\n          url: window.location.href,\n        });\n      } catch (error) {\n        console.log('Error sharing:', error);\n      }\n    } else {\n      // Fallback to copying URL to clipboard\n      try {\n        await navigator.clipboard.writeText(window.location.href);\n        setCopied(true);\n        setTimeout(() => setCopied(false), 2000);\n      } catch (error) {\n        console.log('Error copying to clipboard:', error);\n      }\n    }\n  };\n\n  // Enhanced content processing for better typography\n  const processContent = (content: string) => {\n    return content\n      .replace(/\\n\\n/g, '</p><p class=\"mb-6\">')\n      .replace(/\\n/g, '<br />')\n      .replace(/#{1,6}\\s(.+)/g, '<h2 class=\"text-2xl md:text-3xl font-heading font-bold text-primary mt-12 mb-6 first:mt-0\">$1</h2>')\n      .replace(/\\*\\*(.+?)\\*\\*/g, '<strong class=\"font-semibold text-primary\">$1</strong>')\n      .replace(/\\*(.+?)\\*/g, '<em class=\"italic\">$1</em>')\n      .replace(/`(.+?)`/g, '<code class=\"bg-gray-100 px-2 py-1 rounded text-sm font-mono\">$1</code>');\n  };\n\n  return (\n    <article className=\"flex-1\">\n      {/* Main Content */}\n      <div className=\"prose prose-lg max-w-none\">\n        <div\n          className=\"text-foreground leading-relaxed text-lg\"\n          dangerouslySetInnerHTML={{\n            __html: `<p class=\"mb-6\">${processContent(post.content)}</p>`\n          }}\n        />\n      </div>\n\n      {/* Content Footer */}\n      <div className=\"mt-16 space-y-8\">\n        {/* Tags Section */}\n        <div className=\"bg-card rounded-xl shadow-sm border p-6\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"p-2 bg-primary/10 rounded-lg\">\n              <span className=\"text-primary text-lg\">🏷️</span>\n            </div>\n            <h3 className=\"text-xl font-heading font-semibold text-primary\">\n              Article Tags\n            </h3>\n          </div>\n          <div className=\"flex flex-wrap gap-3\">\n            {post.tags.map((tag) => (\n              <Badge\n                key={tag}\n                variant=\"outline\"\n                className=\"hover:bg-primary hover:text-primary-foreground transition-all duration-300 hover:scale-105 cursor-pointer px-4 py-2\"\n              >\n                #{tag}\n              </Badge>\n            ))}\n          </div>\n        </div>\n\n        {/* Share Section */}\n        <div className=\"bg-gradient-to-r from-muted to-background rounded-xl shadow-sm border p-6\">\n          <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\">\n            <div>\n              <h3 className=\"text-xl font-heading font-semibold text-primary mb-2\">\n                Share this article\n              </h3>\n              <p className=\"text-muted-foreground\">\n                Found this helpful? Share it with others who might benefit!\n              </p>\n            </div>\n            <Button\n              onClick={sharePost}\n              className=\"bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-2 min-w-[120px]\"\n            >\n              {copied ? (\n                <>\n                  <Check size={16} />\n                  Copied!\n                </>\n              ) : (\n                <>\n                  <Share2 size={16} />\n                  Share\n                </>\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"bg-gradient-to-r from-primary to-accent text-primary-foreground rounded-xl p-8 text-center\">\n          <h3 className=\"text-2xl font-heading font-bold mb-4\">\n            Ready to Transform Your Content?\n          </h3>\n          <p className=\"text-white/90 mb-6 max-w-2xl mx-auto\">\n            Let&apos;s work together to create compelling videos that engage your audience and tell your story effectively.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              asChild\n              variant=\"secondary\"\n              className=\"bg-background text-foreground hover:bg-background/90\"\n            >\n              <Link href=\"/#contact\">Get in Touch</Link>\n            </Button>\n            <Button\n              asChild\n              variant=\"outline\"\n              className=\"border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary\"\n            >\n              <a href=\"/videos\">View Portfolio</a>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAae,SAAS,kBAAkB,EAAE,IAAI,EAA0B;;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,YAAY;QAChB,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,OAAO;oBAClB,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,kBAAkB;YAChC;QACF,OAAO;YACL,uCAAuC;YACvC,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;gBACxD,UAAU;gBACV,WAAW,IAAM,UAAU,QAAQ;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,+BAA+B;YAC7C;QACF;IACF;IAEA,oDAAoD;IACpD,MAAM,iBAAiB,CAAC;QACtB,OAAO,QACJ,OAAO,CAAC,SAAS,wBACjB,OAAO,CAAC,OAAO,UACf,OAAO,CAAC,iBAAiB,sGACzB,OAAO,CAAC,kBAAkB,0DAC1B,OAAO,CAAC,cAAc,8BACtB,OAAO,CAAC,YAAY;IACzB;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,yBAAyB;wBACvB,QAAQ,CAAC,gBAAgB,EAAE,eAAe,KAAK,OAAO,EAAE,IAAI,CAAC;oBAC/D;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;kDAEzC,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;0CAIlE,6LAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,6LAAC,oIAAA,CAAA,QAAK;wCAEJ,SAAQ;wCACR,WAAU;;4CACX;4CACG;;uCAJG;;;;;;;;;;;;;;;;kCAWb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAGrE,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;8CAET,uBACC;;0DACE,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;4CAAM;;qEAIrB;;0DACE,6LAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;kCAS9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,SAAQ;wCACR,WAAU;kDAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAY;;;;;;;;;;;kDAEzB,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,SAAQ;wCACR,WAAU;kDAEV,cAAA,6LAAC;4CAAE,MAAK;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;GApIwB;KAAA", "debugId": null}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/blog/BlogDetailSidebar.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Calendar, Clock, ArrowRight, User, Award, BookOpen } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { formatDate, type BlogPost } from '@/lib/api';\n\ninterface BlogDetailSidebarProps {\n  relatedPosts: BlogPost[];\n}\n\nexport default function BlogDetailSidebar({ relatedPosts }: BlogDetailSidebarProps) {\n  return (\n    <aside className=\"lg:w-80\">\n      <div className=\"sticky top-8 space-y-6\">\n        {/* Author Card */}\n        <div className=\"bg-card rounded-xl border p-6\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"p-2 bg-primary/10 rounded-lg\">\n              <User size={20} className=\"text-primary\" />\n            </div>\n            <h3 className=\"text-lg font-heading font-semibold text-primary\">\n              About the Author\n            </h3>\n          </div>\n\n          <div className=\"flex items-center gap-4 mb-4\">\n            <div className=\"w-16 h-16 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-heading font-bold text-xl border-2 border-primary/20\">\n              U\n            </div>\n            <div>\n              <div className=\"font-semibold text-primary text-lg\">Uttam Rimal</div>\n              <div className=\"text-sm text-muted-foreground\">Video Editor & Storyteller</div>\n            </div>\n          </div>\n          \n          <p className=\"text-sm text-muted-foreground mb-4 leading-relaxed\">\n            Professional video editor with years of experience crafting visually stunning videos that engage and inspire audiences worldwide.\n          </p>\n          \n          <div className=\"flex items-center gap-4 text-xs text-muted-foreground mb-4\">\n            <div className=\"flex items-center gap-1\">\n              <Award size={12} />\n              <span>5+ Years Experience</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <BookOpen size={12} />\n              <span>50+ Articles</span>\n            </div>\n          </div>\n          \n          <Button asChild className=\"w-full bg-primary hover:bg-primary/90 text-primary-foreground\">\n            <Link href=\"/#contact\">\n              Connect with Uttam\n            </Link>\n          </Button>\n        </div>\n\n        {/* Related Posts */}\n        {relatedPosts.length > 0 && (\n          <div className=\"bg-card rounded-xl border p-6\">\n            <div className=\"flex items-center gap-3 mb-6\">\n              <div className=\"p-2 bg-secondary/10 rounded-lg\">\n                <BookOpen size={20} className=\"text-secondary\" />\n              </div>\n              <h3 className=\"text-lg font-heading font-semibold text-primary\">\n                Related Articles\n              </h3>\n            </div>\n            \n            <div className=\"space-y-4\">\n              {relatedPosts.map((relatedPost, index) => (\n                <Link\n                  key={relatedPost._id}\n                  href={`/blog/${relatedPost.slug}`}\n                  className=\"block group p-4 rounded-lg hover:bg-muted transition-all duration-300 border border-transparent hover:border-primary/20\"\n                  style={{\n                    animationDelay: `${index * 100}ms`,\n                  }}\n                >\n                  <div className=\"flex items-start justify-between gap-3\">\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-semibold text-primary group-hover:text-secondary transition-colors text-sm mb-2 line-clamp-2\">\n                        {relatedPost.title}\n                      </h4>\n                      <div className=\"flex items-center gap-3 text-xs text-muted-foreground\">\n                        <div className=\"flex items-center gap-1\">\n                          <Calendar size={10} />\n                          <span>{formatDate(relatedPost.createdAt)}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Clock size={10} />\n                          <span>{relatedPost.readTime} min</span>\n                        </div>\n                      </div>\n                    </div>\n                    <ArrowRight \n                      size={16} \n                      className=\"text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-300 flex-shrink-0\"\n                    />\n                  </div>\n                </Link>\n              ))}\n            </div>\n            \n            <div className=\"mt-6 pt-4 border-t border-gray-100\">\n              <Button asChild variant=\"outline\" className=\"w-full\">\n                <Link href=\"/blog\">\n                  View All Articles\n                </Link>\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {/* Newsletter Signup */}\n        <div className=\"bg-primary text-primary-foreground rounded-xl p-6 border border-primary/20\">\n          <div className=\"text-center\">\n            <div className=\"w-12 h-12 bg-primary-foreground/20 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">📧</span>\n            </div>\n            <h3 className=\"text-lg font-heading font-semibold mb-2\">\n              Stay Updated\n            </h3>\n            <p className=\"text-primary-foreground/90 text-sm mb-4\">\n              Get the latest video editing tips and insights delivered to your inbox.\n            </p>\n            <Button\n              asChild\n              variant=\"secondary\"\n              className=\"w-full bg-primary-foreground text-primary hover:bg-primary-foreground/90\"\n            >\n              <Link href=\"/#contact\">\n                Subscribe Now\n              </Link>\n            </Button>\n          </div>\n        </div>\n\n        {/* Quick Links */}\n        <div className=\"bg-card rounded-xl border p-6\">\n          <h3 className=\"text-lg font-heading font-semibold text-primary mb-4\">\n            Quick Links\n          </h3>\n          <div className=\"space-y-3\">\n            <Link\n              href=\"/videos\"\n              className=\"flex items-center gap-3 text-sm text-muted-foreground hover:text-primary transition-colors group p-2 rounded-lg hover:bg-muted\"\n            >\n              <span className=\"text-lg\">🎬</span>\n              <span className=\"group-hover:translate-x-1 transition-transform duration-300\">Video Portfolio</span>\n            </Link>\n            <Link\n              href=\"/reels\"\n              className=\"flex items-center gap-3 text-sm text-muted-foreground hover:text-primary transition-colors group p-2 rounded-lg hover:bg-muted\"\n            >\n              <span className=\"text-lg\">📱</span>\n              <span className=\"group-hover:translate-x-1 transition-transform duration-300\">YouTube Shorts</span>\n            </Link>\n            <Link\n              href=\"/clients\"\n              className=\"flex items-center gap-3 text-sm text-muted-foreground hover:text-primary transition-colors group p-2 rounded-lg hover:bg-muted\"\n            >\n              <span className=\"text-lg\">🤝</span>\n              <span className=\"group-hover:translate-x-1 transition-transform duration-300\">Client Success Stories</span>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAMe,SAAS,kBAAkB,EAAE,YAAY,EAA0B;IAChF,qBACE,6LAAC;QAAM,WAAU;kBACf,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAE5B,6LAAC;oCAAG,WAAU;8CAAkD;;;;;;;;;;;;sCAKlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAuJ;;;;;;8CAGtK,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAInD,6LAAC;4BAAE,WAAU;sCAAqD;;;;;;sCAIlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAIV,6LAAC,qIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,WAAU;sCACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAY;;;;;;;;;;;;;;;;;gBAO1B,aAAa,MAAM,GAAG,mBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAEhC,6LAAC;oCAAG,WAAU;8CAAkD;;;;;;;;;;;;sCAKlE,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE;oCACjC,WAAU;oCACV,OAAO;wCACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;oCACpC;8CAEA,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,YAAY,KAAK;;;;;;kEAEpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,MAAM;;;;;;kFAChB,6LAAC;kFAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,SAAS;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,MAAM;;;;;;kFACb,6LAAC;;4EAAM,YAAY,QAAQ;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;0DAIlC,6LAAC,qNAAA,CAAA,aAAU;gDACT,MAAM;gDACN,WAAU;;;;;;;;;;;;mCAzBT,YAAY,GAAG;;;;;;;;;;sCAgC1B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,WAAU;0CAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAQ;;;;;;;;;;;;;;;;;;;;;;8BAS3B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAGxD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;0CAGvD,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,SAAQ;gCACR,WAAU;0CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAY;;;;;;;;;;;;;;;;;;;;;;8BAQ7B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAA8D;;;;;;;;;;;;8CAEhF,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAA8D;;;;;;;;;;;;8CAEhF,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5F;KAhKwB", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/app/blog/%5Bslug%5D/BlogDetailClient.tsx"], "sourcesContent": ["'use client';\n\nimport BlogDetailHeader from '@/components/features/blog/BlogDetailHeader';\nimport BlogFeaturedImage from '@/components/features/blog/BlogFeaturedImage';\nimport BlogDetailContent from '@/components/features/blog/BlogDetailContent';\nimport BlogDetailSidebar from '@/components/features/blog/BlogDetailSidebar';\nimport { type BlogPost } from '@/lib/api';\n\ninterface BlogDetailClientProps {\n  post: BlogPost;\n  relatedPosts: BlogPost[];\n}\n\nexport default function BlogDetailClient({ post, relatedPosts }: BlogDetailClientProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <BlogDetailHeader post={post} />\n\n      {/* Featured Image */}\n      <BlogFeaturedImage post={post} />\n\n      {/* Content */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex flex-col lg:flex-row gap-12\">\n            {/* Main Content */}\n            <BlogDetailContent post={post} />\n\n            {/* Sidebar */}\n            <BlogDetailSidebar relatedPosts={relatedPosts} />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS,iBAAiB,EAAE,IAAI,EAAE,YAAY,EAAyB;IACpF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6JAAA,CAAA,UAAgB;gBAAC,MAAM;;;;;;0BAGxB,6LAAC,8JAAA,CAAA,UAAiB;gBAAC,MAAM;;;;;;0BAGzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,8JAAA,CAAA,UAAiB;gCAAC,MAAM;;;;;;0CAGzB,6LAAC,8JAAA,CAAA,UAAiB;gCAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7C;KAvBwB", "debugId": null}}]}