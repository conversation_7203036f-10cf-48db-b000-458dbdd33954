(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7156],{333:(e,s,t)=>{"use strict";t.d(s,{d:()=>n});var i=t(5155);t(2115);var a=t(239),r=t(9434);function n(e){let{className:s,...t}=e;return(0,i.jsx)(a.bL,{"data-slot":"switch",className:(0,r.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...t,children:(0,i.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,r.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},3750:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var i=t(5155),a=t(2115),r=t(5695),n=t(6913),l=t(8280),c=t(285),d=t(2523),o=t(8539),u=t(5057),x=t(6695),h=t(333),g=t(6126),p=t(5365),m=t(1154),v=t(7550),j=t(4416),f=t(4229),b=t(2657),y=t(6874),k=t.n(y);function w(){let e=(0,r.useRouter)(),s=(0,r.useParams)().id,[t,y]=(0,a.useState)(null),[w,A]=(0,a.useState)(!0),[N,C]=(0,a.useState)(!1),[z,T]=(0,a.useState)(""),[S,$]=(0,a.useState)(""),[E,L]=(0,a.useState)({title:"",excerpt:"",content:"",category:"",tags:[],featured:!1,status:"draft",thumbnail:"",seoTitle:"",seoDescription:"",seoKeywords:[]}),[P,D]=(0,a.useState)(""),[F,B]=(0,a.useState)("");(0,a.useEffect)(()=>{s&&J()},[s]);let J=async()=>{try{let e=await fetch("/api/blog/".concat(s),{credentials:"include"});if(e.ok){let s=(await e.json()).data;y(s),L({title:s.title||"",excerpt:s.excerpt||"",content:s.content||"",category:s.category||"",tags:s.tags||[],featured:s.featured||!1,status:s.status||"draft",thumbnail:s.thumbnail||"",seoTitle:s.seoTitle||"",seoDescription:s.seoDescription||"",seoKeywords:s.seoKeywords||[]})}else T("Failed to load blog post")}catch(e){T("Network error occurred")}finally{A(!1)}},K=(e,s)=>{L(t=>({...t,[e]:s}))},O=()=>{P.trim()&&!E.tags.includes(P.trim())&&(L(e=>({...e,tags:[...e.tags,P.trim()]})),D(""))},R=e=>{L(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},U=()=>{F.trim()&&!E.seoKeywords.includes(F.trim())&&(L(e=>({...e,seoKeywords:[...e.seoKeywords,F.trim()]})),B(""))},Z=e=>{L(s=>({...s,seoKeywords:s.seoKeywords.filter(s=>s!==e)}))},_=async t=>{C(!0),T(""),$("");try{let i=await fetch("/api/blog/".concat(s),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...E,status:t})}),a=await i.json();i.ok?($("Blog post ".concat("published"===t?"published":"archived"===t?"archived":"saved"," successfully!")),y(a.data),setTimeout(()=>{e.push("/dashboard/blog")},1500)):T(a.error||"Failed to update blog post")}catch(e){T("Network error occurred")}finally{C(!1)}};return w?(0,i.jsx)(n.A,{children:(0,i.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(m.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,i.jsx)("p",{children:"Loading blog post..."})]})})}):t?(0,i.jsx)(n.A,{children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(k(),{href:"/dashboard/blog",children:(0,i.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back to Posts"]})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Blog Post"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Update your blog post content"})]})]})}),z&&(0,i.jsx)(p.Fc,{variant:"destructive",children:(0,i.jsx)(p.TN,{children:z})}),S&&(0,i.jsx)(p.Fc,{children:(0,i.jsx)(p.TN,{children:S})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)(x.Zp,{children:[(0,i.jsx)(x.aR,{children:(0,i.jsx)(x.ZB,{children:"Post Content"})}),(0,i.jsxs)(x.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{htmlFor:"title",children:"Title *"}),(0,i.jsx)(d.p,{id:"title",value:E.title,onChange:e=>K("title",e.target.value),placeholder:"Enter post title...",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{htmlFor:"excerpt",children:"Excerpt *"}),(0,i.jsx)(o.T,{id:"excerpt",value:E.excerpt,onChange:e=>K("excerpt",e.target.value),placeholder:"Brief description of the post...",rows:3,required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{children:"Content *"}),(0,i.jsx)(l.A,{content:E.content,onChange:e=>K("content",e),placeholder:"Start writing your blog post..."})]})]})]}),(0,i.jsxs)(x.Zp,{children:[(0,i.jsx)(x.aR,{children:(0,i.jsx)(x.ZB,{children:"SEO Settings"})}),(0,i.jsxs)(x.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{htmlFor:"seoTitle",children:"SEO Title"}),(0,i.jsx)(d.p,{id:"seoTitle",value:E.seoTitle,onChange:e=>K("seoTitle",e.target.value),placeholder:"SEO optimized title (max 60 chars)",maxLength:60}),(0,i.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[E.seoTitle.length,"/60 characters"]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{htmlFor:"seoDescription",children:"SEO Description"}),(0,i.jsx)(o.T,{id:"seoDescription",value:E.seoDescription,onChange:e=>K("seoDescription",e.target.value),placeholder:"SEO meta description (max 160 chars)",rows:3,maxLength:160}),(0,i.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[E.seoDescription.length,"/160 characters"]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{children:"SEO Keywords"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(d.p,{value:F,onChange:e=>B(e.target.value),placeholder:"Add SEO keyword...",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),U())}),(0,i.jsx)(c.$,{type:"button",onClick:U,variant:"outline",children:"Add"})]}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:E.seoKeywords.map((e,s)=>(0,i.jsxs)(g.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,i.jsx)(j.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>Z(e)})]},s))})]})]})]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)(x.Zp,{children:[(0,i.jsx)(x.aR,{children:(0,i.jsx)(x.ZB,{children:"Update Post"})}),(0,i.jsxs)(x.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(h.d,{id:"featured",checked:E.featured,onCheckedChange:e=>K("featured",e)}),(0,i.jsx)(u.J,{htmlFor:"featured",children:"Featured Post"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(c.$,{onClick:()=>_("draft"),variant:"outline",className:"w-full",disabled:N,children:[N?(0,i.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,i.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Save as Draft"]}),(0,i.jsxs)(c.$,{onClick:()=>_("published"),className:"w-full",disabled:N||!E.title||!E.excerpt||!E.content,children:[N?(0,i.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,i.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"published"===t.status?"Update Published":"Publish"]}),"published"===t.status&&(0,i.jsx)(c.$,{onClick:()=>_("archived"),variant:"destructive",className:"w-full",disabled:N,children:"Archive Post"})]})]})]}),(0,i.jsxs)(x.Zp,{children:[(0,i.jsx)(x.aR,{children:(0,i.jsx)(x.ZB,{children:"Post Settings"})}),(0,i.jsxs)(x.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{htmlFor:"category",children:"Category *"}),(0,i.jsx)(d.p,{id:"category",value:E.category,onChange:e=>K("category",e.target.value),placeholder:"e.g., Tutorial, Tips, Review",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{children:"Tags"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(d.p,{value:P,onChange:e=>D(e.target.value),placeholder:"Add tag...",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),O())}),(0,i.jsx)(c.$,{type:"button",onClick:O,variant:"outline",children:"Add"})]}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:E.tags.map((e,s)=>(0,i.jsxs)(g.E,{variant:"outline",className:"flex items-center gap-1",children:[e,(0,i.jsx)(j.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>R(e)})]},s))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(u.J,{htmlFor:"thumbnail",children:"Featured Image URL"}),(0,i.jsx)(d.p,{id:"thumbnail",value:E.thumbnail,onChange:e=>K("thumbnail",e.target.value),placeholder:"https://example.com/image.jpg"})]}),(0,i.jsxs)("div",{className:"pt-4 border-t",children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Post Information"}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,i.jsxs)("p",{children:["Status: ",(0,i.jsx)(g.E,{variant:"outline",children:t.status})]}),(0,i.jsxs)("p",{children:["Views: ",t.views]}),(0,i.jsxs)("p",{children:["Read Time: ",t.readTime," min"]}),(0,i.jsxs)("p",{children:["Created: ",new Date(t.createdAt).toLocaleDateString()]}),(0,i.jsxs)("p",{children:["Updated: ",new Date(t.updatedAt).toLocaleDateString()]})]})]})]})]})]})]})]})}):(0,i.jsx)(n.A,{children:(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Post Not Found"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6",children:"The blog post you're looking for doesn't exist."}),(0,i.jsx)(k(),{href:"/dashboard/blog",children:(0,i.jsxs)(c.$,{children:[(0,i.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back to Posts"]})})]})})}},4666:(e,s,t)=>{Promise.resolve().then(t.bind(t,3750))},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>n});var i=t(5155);t(2115);var a=t(968),r=t(9434);function n(e){let{className:s,...t}=e;return(0,i.jsx)(a.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},5365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>l,TN:()=>c});var i=t(5155);t(2115);var a=t(2085),r=t(9434);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,r.cn)(n({variant:t}),s),...a})}function c(e){let{className:s,...t}=e;return(0,i.jsx)("div",{"data-slot":"alert-description",className:(0,r.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...t})}},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var i=t(5155);t(2115);var a=t(9708),r=t(2085),n=t(9434);let l=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:r=!1,...c}=e,d=r?a.DX:"span";return(0,i.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...c})}},8280:(e,s,t)=>{"use strict";t.d(s,{A:()=>E});var i=t(5155),a=t(5109),r=t(8292),n=t(1891),l=t(6761),c=t(4109),d=t(6377),o=t(4589),u=t(4652),x=t(285),h=t(9727),g=t(9144),p=t(2643),m=t(9621),v=t(8440),j=t(2705),f=t(2406),b=t(5968),y=t(9140),k=t(224),w=t(7325),A=t(7733),N=t(8702),C=t(8164),z=t(7213),T=t(3654),S=t(8932),$=t(2115);function E(e){let{content:s,onChange:t,placeholder:E="Start writing...",className:L=""}=e,P=(0,a.hG)({extensions:[r.A,n.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),l.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-600 hover:text-blue-800 underline"}}),c.A.configure({types:["heading","paragraph"]}),d.A,o.A,u.Ay.configure({multicolor:!0})],content:s,onUpdate:e=>{let{editor:s}=e;t(s.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[300px] p-4"}}}),D=(0,$.useCallback)(()=>{let e=window.prompt("Enter image URL:");e&&P&&P.chain().focus().setImage({src:e}).run()},[P]),F=(0,$.useCallback)(()=>{let e=null==P?void 0:P.getAttributes("link").href,s=window.prompt("Enter URL:",e);if(null!==s){if(""===s){null==P||P.chain().focus().extendMarkRange("link").unsetLink().run();return}null==P||P.chain().focus().extendMarkRange("link").setLink({href:s}).run()}},[P]);return P?(0,i.jsxs)("div",{className:"border border-gray-300 rounded-lg ".concat(L),children:[(0,i.jsxs)("div",{className:"border-b border-gray-300 p-2 flex flex-wrap gap-1",children:[(0,i.jsx)(x.$,{variant:P.isActive("bold")?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleBold().run(),children:(0,i.jsx)(h.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive("italic")?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleItalic().run(),children:(0,i.jsx)(g.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive("strike")?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleStrike().run(),children:(0,i.jsx)(p.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive("code")?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleCode().run(),children:(0,i.jsx)(m.A,{size:16})}),(0,i.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,i.jsx)(x.$,{variant:P.isActive("heading",{level:1})?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleHeading({level:1}).run(),children:(0,i.jsx)(v.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive("heading",{level:2})?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleHeading({level:2}).run(),children:(0,i.jsx)(j.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive("heading",{level:3})?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleHeading({level:3}).run(),children:(0,i.jsx)(f.A,{size:16})}),(0,i.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,i.jsx)(x.$,{variant:P.isActive("bulletList")?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleBulletList().run(),children:(0,i.jsx)(b.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive("orderedList")?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleOrderedList().run(),children:(0,i.jsx)(y.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive("blockquote")?"default":"outline",size:"sm",onClick:()=>P.chain().focus().toggleBlockquote().run(),children:(0,i.jsx)(k.A,{size:16})}),(0,i.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,i.jsx)(x.$,{variant:P.isActive({textAlign:"left"})?"default":"outline",size:"sm",onClick:()=>P.chain().focus().setTextAlign("left").run(),children:(0,i.jsx)(w.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive({textAlign:"center"})?"default":"outline",size:"sm",onClick:()=>P.chain().focus().setTextAlign("center").run(),children:(0,i.jsx)(A.A,{size:16})}),(0,i.jsx)(x.$,{variant:P.isActive({textAlign:"right"})?"default":"outline",size:"sm",onClick:()=>P.chain().focus().setTextAlign("right").run(),children:(0,i.jsx)(N.A,{size:16})}),(0,i.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:F,children:(0,i.jsx)(C.A,{size:16})}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:D,children:(0,i.jsx)(z.A,{size:16})}),(0,i.jsx)("div",{className:"w-px h-6 bg-gray-300 mx-1"}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>P.chain().focus().undo().run(),disabled:!P.can().undo(),children:(0,i.jsx)(T.A,{size:16})}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>P.chain().focus().redo().run(),disabled:!P.can().redo(),children:(0,i.jsx)(S.A,{size:16})})]}),(0,i.jsx)(a.$Z,{editor:P,className:"min-h-[300px] max-h-[600px] overflow-y-auto"})]}):null}},8539:(e,s,t)=>{"use strict";t.d(s,{T:()=>r});var i=t(5155);t(2115);var a=t(9434);function r(e){let{className:s,...t}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...t})}}},e=>{var s=s=>e(e.s=s);e.O(0,[5004,277,3455,5812,239,6823,8441,1684,7358],()=>s(4666)),_N_E=e.O()}]);