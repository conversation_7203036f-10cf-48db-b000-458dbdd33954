# CORS Error Fix Guide

## Problem Analysis

The CORS errors in your Next.js portfolio are occurring because:

1. **Hardcoded placeholder domains** in CMS CORS configuration
2. **Missing production domain** in allowed origins
3. **Environment variable mismatch** between development and production

## Solution Implemented

### 1. Updated CMS CORS Configuration

**Files Modified:**
- `cms/src/lib/cors.ts` - Enhanced CORS utility functions
- `cms/src/middleware.ts` - Updated middleware with proper origins
- `cms/src/app/api/public/blog/route.ts` - Fixed public blog API CORS
- `cms/src/app/api/public/blog/[slug]/route.ts` - Fixed blog slug API CORS

**Key Changes:**
- Added production domains: `uttamrimal.com.np`, `uttam-portfolio.vercel.app`
- Dynamic environment variable support
- Fallback to `*` for unmatched origins (development flexibility)
- Added `X-Requested-With` header support

### 2. Environment Variables Setup

**For CMS (Production):**
```env
# Portfolio Frontend URL (for CORS)
FRONTEND_URL=https://uttamrimal.com.np
# Alternative environment variable
NEXT_PUBLIC_FRONTEND_URL=https://uttamrimal.com.np
```

**For Portfolio (Production):**
```env
# CMS Backend URL
NEXT_PUBLIC_CMS_URL=https://uttam-backend.vercel.app
```

### 3. Allowed Origins List

The following domains are now allowed:
- `http://localhost:3000` (Portfolio dev)
- `http://localhost:3001` (CMS production port)
- `http://localhost:3002` (CMS dev)
- `http://localhost:3003` (Additional dev)
- `https://uttamrimal.com.np` (Production portfolio)
- `https://www.uttamrimal.com.np` (Production portfolio with www)
- `https://uttam-portfolio.vercel.app` (Vercel deployment)
- `https://uttam-portfolio-git-main.vercel.app` (Vercel preview)
- Any custom domain from `FRONTEND_URL` environment variable

## Deployment Steps

### Step 1: Update CMS Environment Variables

In your CMS deployment platform (Vercel/Netlify/etc.), add:
```
FRONTEND_URL=https://uttamrimal.com.np
```

### Step 2: Redeploy CMS

After updating environment variables, redeploy your CMS to apply the changes.

### Step 3: Verify Portfolio Environment

Ensure your portfolio has:
```
NEXT_PUBLIC_CMS_URL=https://uttam-backend.vercel.app
```

### Step 4: Test CORS

Test the following endpoints from your portfolio domain:
- `GET https://uttam-backend.vercel.app/api/public/blog`
- `GET https://uttam-backend.vercel.app/api/videos`
- `GET https://uttam-backend.vercel.app/api/testimonials`
- `GET https://uttam-backend.vercel.app/api/clients`

## Troubleshooting

### If CORS errors persist:

1. **Check browser network tab** for the exact error message
2. **Verify Origin header** in the request
3. **Check CMS logs** for CORS-related errors
4. **Test with curl** to isolate client vs server issues:

```bash
curl -H "Origin: https://uttamrimal.com.np" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://uttam-backend.vercel.app/api/public/blog
```

### Common Issues:

1. **Wrong domain in environment variables**
   - Solution: Double-check the exact domain used in production

2. **Environment variables not loaded**
   - Solution: Restart/redeploy the application after setting env vars

3. **Caching issues**
   - Solution: Clear browser cache and try in incognito mode

4. **Subdomain mismatch**
   - Solution: Ensure both `domain.com` and `www.domain.com` are in allowed origins

## Testing Checklist

- [ ] CMS environment variables updated
- [ ] CMS redeployed
- [ ] Portfolio can fetch blog posts
- [ ] Portfolio can fetch videos
- [ ] Portfolio can fetch testimonials
- [ ] Portfolio can fetch clients
- [ ] No CORS errors in browser console
- [ ] All client components load data successfully

## Next Steps

After implementing this fix:
1. Monitor for any remaining CORS issues
2. Consider implementing proper error boundaries for API failures
3. Add retry logic for failed API calls
4. Implement caching strategies for better performance
