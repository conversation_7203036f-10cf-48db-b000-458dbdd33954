(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>n});var a=r(5155),s=r(2115);let i=(0,s.createContext)(void 0);function n(e){let{children:t}=e,[r,n]=(0,s.useState)(null),[d,l]=(0,s.useState)(!0),o=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();n(t.user)}else n(null)}catch(e){console.error("Auth check failed:",e),n(null)}finally{l(!1)}},c=async(e,t)=>{try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),a=await r.json();if(r.ok)return n(a.user),{success:!0};return{success:!1,error:a.error||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error"}}},u=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{n(null)}};return(0,s.useEffect)(()=>{o()},[]),(0,a.jsx)(i.Provider,{value:{user:r,loading:d,login:c,logout:u,checkAuth:o},children:t})}function d(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155);r(2115);var s=r(9708),i=r(2085),n=r(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...o}=e,c=l?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:r,size:i,className:t})),...o})}},958:(e,t,r)=>{Promise.resolve().then(r.bind(r,9985))},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>d});var a=r(2115),s=r(3655),i=r(5155),n=a.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var d=n},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(5155);r(2115);var s=r(9434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>d});var a=r(2115),s=r(7650),i=r(9708),n=r(5155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),s=a.forwardRef((e,a)=>{let{asChild:s,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?r:t,{...i,ref:a})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function l(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var a=r(5155);r(2115);var s=r(968),i=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>l});var a=r(5155);r(2115);var s=r(2085),i=r(9434);let n=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:r}),t),...s})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=r(5155);r(2115);var s=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(2596),s=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},9985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(5155),s=r(2115),i=r(5695),n=r(283),d=r(285),l=r(2523),o=r(5057),c=r(6695),u=r(5365),v=r(9946);let f=(0,v.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var h=r(2657);let p=(0,v.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function m(){let[e,t]=(0,s.useState)(""),[r,v]=(0,s.useState)(""),[m,x]=(0,s.useState)(!1),[g,b]=(0,s.useState)(""),[y,j]=(0,s.useState)(!1),{user:w,login:N,loading:k}=(0,n.A)(),P=(0,i.useRouter)();(0,s.useEffect)(()=>{!k&&w&&P.push("/dashboard")},[w,k,P]);let S=async t=>{if(t.preventDefault(),b(""),j(!0),!e||!r){b("Please fill in all fields"),j(!1);return}let a=await N(e,r);a.success?P.push("/dashboard"):b(a.error||"Login failed"),j(!1)};return k?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(c.aR,{className:"text-center",children:[(0,a.jsx)(c.ZB,{className:"text-2xl font-bold",children:"Portfolio CMS"}),(0,a.jsx)(c.BT,{children:"Sign in to manage your portfolio content"})]}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[g&&(0,a.jsx)(u.Fc,{variant:"destructive",children:(0,a.jsx)(u.TN,{children:g})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(l.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>t(e.target.value),required:!0,disabled:y})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.p,{id:"password",type:m?"text":"password",placeholder:"Enter your password",value:r,onChange:e=>v(e.target.value),required:!0,disabled:y}),(0,a.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>x(!m),disabled:y,children:m?(0,a.jsx)(f,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full",disabled:y,children:y?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Signing in..."]}):(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p,{className:"h-4 w-4"}),"Sign In"]})})]}),(0,a.jsxs)("div",{className:"mt-6 text-center text-sm text-gray-600",children:[(0,a.jsx)("p",{children:"Default credentials:"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"}),(0,a.jsx)("p",{children:"Password: admin123"})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,8441,1684,7358],()=>t(958)),_N_E=e.O()}]);