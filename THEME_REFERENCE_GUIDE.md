# 🎨 Theme Reference Guide

## 🎯 **Quick Reference for Developers**

### **Color Usage**
```tsx
// ✅ DO: Use semantic theme variables
className="text-primary"        // Main text color
className="text-secondary"      // Secondary text color
className="text-accent"         // Accent/brand color
className="text-success"        // Success states
className="text-warning"        // Warning states
className="text-destructive"    // Error states
className="text-muted-foreground" // Subtle text

// ❌ DON'T: Use hardcoded colors
className="text-yellow-400"
className="text-green-600"
className="text-red-500"
```

### **Background Colors**
```tsx
// ✅ DO: Use theme backgrounds
className="bg-background"       // Main background
className="bg-card"            // Card background
className="bg-primary"         // Primary button
className="bg-secondary"       // Secondary button
className="bg-accent"          // Accent elements
className="bg-success"         // Success states
className="bg-warning"         // Warning states

// ❌ DON'T: Use hardcoded backgrounds
className="bg-yellow-50"
className="bg-green-100"
```

### **Gradients**
```tsx
// ✅ DO: Use theme gradients
className="gradient-primary"    // Main brand gradient
className="gradient-accent"     // Accent gradient
className="gradient-featured"   // Featured content
className="gradient-success"    // Success gradient
className="text-gradient-primary" // Text gradient

// ❌ DON'T: Use hardcoded gradients
className="bg-gradient-to-r from-yellow-400 to-orange-500"
```

### **Typography**
```tsx
// ✅ DO: Use standardized headings
className="heading-primary"     // h1 - 4xl md:5xl lg:6xl
className="heading-secondary"   // h2 - 3xl md:4xl lg:5xl
className="heading-tertiary"    // h3 - 2xl md:3xl
className="text-body-large"     // Large body text
className="text-body"          // Regular body text

// ❌ DON'T: Use custom heading sizes
className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold"
```

### **Spacing**
```tsx
// ✅ DO: Use standardized spacing
className="section-padding"     // py-16 md:py-20
className="container-padding"   // px-4 md:px-6 lg:px-8

// ❌ DON'T: Use custom spacing
className="py-20 px-4"
```

### **Interactive Elements**
```tsx
// ✅ DO: Use standardized hover effects
className="hover-lift"          // Scale + shadow
className="hover-lift-sm"       // Subtle scale + shadow
className="hover-glow"          // Glow effect

// ❌ DON'T: Create custom hover effects
className="hover:scale-105 hover:shadow-lg transition-all duration-300"
```

### **Cards**
```tsx
// ✅ DO: Use standardized card styles
className="card-interactive"    // Standard interactive card
className="card-featured"       // Featured content card

// ❌ DON'T: Create custom card styles
className="bg-card rounded-xl shadow-lg hover:shadow-xl"
```

### **Buttons**
```tsx
// ✅ DO: Use standardized button styles
className="btn-primary"         // Primary action button
className="btn-secondary"       // Secondary action button
className="btn-accent"          // Accent button

// ❌ DON'T: Create custom button styles
className="px-6 py-3 rounded-full font-semibold transition-all duration-300"
```

## 🔧 **Adding New Components**

### **1. Use Theme Variables First**
```tsx
// ✅ Start with theme variables
const MyComponent = () => (
  <div className="bg-card text-card-foreground border border-border">
    <h2 className="heading-secondary text-primary">Title</h2>
    <p className="text-body text-muted-foreground">Description</p>
  </div>
);
```

### **2. Apply Standardized Classes**
```tsx
// ✅ Add standardized utilities
const MyCard = () => (
  <div className="card-interactive hover-lift">
    <div className="section-padding container-padding">
      <h2 className="heading-tertiary">Card Title</h2>
      <button className="btn-primary">Action</button>
    </div>
  </div>
);
```

### **3. Avoid Hardcoded Values**
```tsx
// ❌ DON'T do this
const BadComponent = () => (
  <div className="bg-yellow-100 text-green-800 p-8">
    <h2 className="text-3xl font-bold text-blue-600">Title</h2>
  </div>
);

// ✅ DO this instead
const GoodComponent = () => (
  <div className="bg-warning/10 text-warning section-padding">
    <h2 className="heading-tertiary text-primary">Title</h2>
  </div>
);
```

## 🎨 **Extending the Theme**

### **Adding New Colors**
```css
/* Add to globals.css */
:root {
  --info: #3b82f6;
  --info-foreground: #ffffff;
}

.dark {
  --info: #2563eb;
  --info-foreground: #ffffff;
}
```

### **Adding New Gradients**
```css
/* Add to globals.css utilities */
.gradient-info {
  background: linear-gradient(135deg, var(--info) 0%, #1d4ed8 100%);
}
```

### **Adding New Utilities**
```css
/* Add to globals.css utilities */
.hover-bounce {
  @apply transition-transform duration-300 hover:scale-110;
}

.card-elevated {
  @apply bg-card border border-border rounded-2xl shadow-xl;
}
```

## 📱 **Responsive Design**

### **Use Built-in Responsive Classes**
```tsx
// ✅ Responsive typography
className="heading-primary"     // Automatically responsive

// ✅ Responsive spacing
className="section-padding"     // py-16 md:py-20
className="container-padding"   // px-4 md:px-6 lg:px-8

// ✅ Custom responsive (when needed)
className="text-sm md:text-base lg:text-lg"
```

## 🌙 **Dark Mode Support**

### **All Theme Variables Work Automatically**
```tsx
// ✅ These automatically adapt to dark/light mode
className="bg-background text-foreground"
className="bg-card text-card-foreground"
className="gradient-primary"
className="text-success"
```

## ✅ **Best Practices Checklist**

- [ ] Use semantic color variables (`text-primary` not `text-black`)
- [ ] Apply standardized spacing (`section-padding` not `py-20`)
- [ ] Use consistent typography (`heading-primary` not custom sizes)
- [ ] Apply standard hover effects (`hover-lift` not custom)
- [ ] Use theme gradients (`gradient-featured` not hardcoded)
- [ ] Test in both light and dark modes
- [ ] Ensure responsive design works
- [ ] Maintain accessibility standards

## 🚀 **Quick Commands**

```bash
# Build and test
npm run build

# Check for theme consistency
grep -r "yellow-\|orange-\|green-\|red-\|blue-" src/components

# Lint check
npm run lint
```

Your theme system is now production-ready and fully consistent! 🎉
