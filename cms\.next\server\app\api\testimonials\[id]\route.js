(()=>{var e={};e.id=6640,e.ids=[6640],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>i,HU:()=>u,b9:()=>l,oC:()=>m,rL:()=>d});var s=r(43205),n=r.n(s),o=r(85663);let a=process.env.JWT_SECRET;if(!a)throw Error("Please define the JWT_SECRET environment variable");let i=async e=>await o.Ay.hash(e,12),l=async(e,t)=>await o.Ay.compare(e,t),u=e=>n().sign(e,a,{expiresIn:"7d"}),c=e=>{try{return n().verify(e,a)}catch(e){return null}},p=e=>{let t=e.headers.get("authorization");return t&&t.startsWith("Bearer ")?t.substring(7):e.cookies.get("auth-token")?.value||null},d=async e=>{let t=p(e);return t?c(t):null},m=e=>async(t,r)=>{let s=await d(t);return s?(t.user=s,e(t,r)):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}},27746:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,In:()=>n,Sw:()=>a,gx:()=>i});var s=r(32190);function n(){let e=process.env.FRONTEND_URL||process.env.NEXT_PUBLIC_FRONTEND_URL;return[...new Set(["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","https://uttamrimal.com.np","https://www.uttamrimal.com.np","https://uttam-portfolio.vercel.app","https://uttam-portfolio-git-main.vercel.app",...e?[e]:[],...process.env.ADDITIONAL_CORS_ORIGINS?.split(",").map(e=>e.trim())||[]].filter(Boolean))]}function o(e,t){return!!e&&(!!t.includes(e)||t.some(t=>{if(t.includes("*")){let r=t.replace(/\*/g,".*");return RegExp(`^${r}$`).test(e)}return!1}))}function a(e,t){let r,s=o(t||"",n()),a=process.env.FRONTEND_URL||process.env.NEXT_PUBLIC_FRONTEND_URL;r=s?t||"*":a?"null":"*",e.headers.set("Access-Control-Allow-Origin",r),e.headers.set("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),e.headers.set("Access-Control-Allow-Headers","Content-Type, Authorization, X-Requested-With"),e.headers.set("Access-Control-Allow-Credentials",s||"*"===r?"true":"false")}function i(e){return async t=>{let r=function(e){let t,r=o(e||"",n()),s=process.env.FRONTEND_URL||process.env.NEXT_PUBLIC_FRONTEND_URL;return{"Access-Control-Allow-Origin":t=r?e||"*":s?"null":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":r||"*"===t?"true":"false"}}(t.headers.get("origin")||void 0);if("OPTIONS"===t.method)return new s.NextResponse(null,{status:200,headers:r});try{let s=await e(t);return Object.entries(r).forEach(([e,t])=>{s.headers.set(e,t)}),s}catch(t){console.error("API Error:",t);let e=s.NextResponse.json({success:!1,message:"Internal server error"},{status:500});return Object.entries(r).forEach(([t,r])=>{e.headers.set(t,r)}),e}}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>d,PUT:()=>m});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),l=r(75745),u=r(63649),c=r(12909),p=r(27746);async function d(e,{params:t}){try{await (0,l.A)();let{id:r}=await t,s=await u.A.findById(r).lean();if(!s){let t=i.NextResponse.json({error:"Testimonial not found"},{status:404});return(0,p.Sw)(t,e.headers.get("origin")),t}let n=i.NextResponse.json({success:!0,data:s});return(0,p.Sw)(n,e.headers.get("origin")),n}catch(r){console.error("Get testimonial error:",r);let t=i.NextResponse.json({error:"Internal server error"},{status:500});return(0,p.Sw)(t,e.headers.get("origin")),t}}let m=(0,c.oC)(async(e,{params:t})=>{try{await (0,l.A)();let{id:r}=await t,s=await e.json();if(!await u.A.findById(r))return i.NextResponse.json({error:"Testimonial not found"},{status:404});if(s.rating&&(s.rating<1||s.rating>5))return i.NextResponse.json({error:"Rating must be between 1 and 5"},{status:400});let n=await u.A.findByIdAndUpdate(r,{...s,updatedAt:new Date},{new:!0,runValidators:!0});return i.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Update testimonial error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}),h=(0,c.oC)(async(e,{params:t})=>{try{await (0,l.A)();let{id:e}=await t;if(!await u.A.findById(e))return i.NextResponse.json({error:"Testimonial not found"},{status:404});return await u.A.findByIdAndDelete(e),i.NextResponse.json({success:!0,message:"Testimonial deleted successfully"})}catch(e){return console.error("Delete testimonial error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}),g=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/testimonials/[id]/route",pathname:"/api/testimonials/[id]",filename:"route",bundlePath:"app/api/testimonials/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\testimonials\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:y}=g;function x(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63649:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(56037),n=r.n(s);let o=new s.Schema({name:{type:String,required:!0,trim:!0,maxlength:100},role:{type:String,required:!0,trim:!0,maxlength:100},company:{type:String,trim:!0,maxlength:100},content:{type:String,required:!0,maxlength:1e3},avatar:{type:String},rating:{type:Number,required:!0,min:1,max:5,default:5},featured:{type:Boolean,default:!1},status:{type:String,enum:["draft","published","archived"],default:"draft"},order:{type:Number,default:0},email:{type:String,trim:!0,lowercase:!0},linkedinUrl:{type:String,trim:!0}},{timestamps:!0});o.index({status:1,order:1}),o.index({featured:1}),o.index({rating:-1});let a=n().models.Testimonial||n().model("Testimonial",o)},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(56037),n=r.n(s);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose||{conn:null,promise:null};global.mongoose||(global.mongoose=a);let i=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(o,{bufferCommands:!1}).then(e=>(console.log("✅ Connected to MongoDB"),e)));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315],()=>r(30586));module.exports=s})();