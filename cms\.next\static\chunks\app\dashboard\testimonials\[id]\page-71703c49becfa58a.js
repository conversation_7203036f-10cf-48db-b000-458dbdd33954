(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7780],{239:(e,t,a)=>{"use strict";a.d(t,{bL:()=>b,zi:()=>N});var r=a(2115),s=a(5185),i=a(6101),l=a(6081),n=a(5845),d=a(1275),c=a(3655),o=a(5155),u="Switch",[h,x]=(0,l.A)(u),[m,p]=h(u),v=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:l,checked:d,defaultChecked:h,required:x,disabled:p,value:v="on",onCheckedChange:g,form:f,...b}=e,[N,k]=r.useState(null),w=(0,i.s)(t,e=>k(e)),A=r.useRef(!1),C=!N||f||!!N.closest("form"),[S,F]=(0,n.i)({prop:d,defaultProp:null!=h&&h,onChange:g,caller:u});return(0,o.jsxs)(m,{scope:a,checked:S,disabled:p,children:[(0,o.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":x,"data-state":y(S),"data-disabled":p?"":void 0,disabled:p,value:v,...b,ref:w,onClick:(0,s.m)(e.onClick,e=>{F(e=>!e),C&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),C&&(0,o.jsx)(j,{control:N,bubbles:!A.current,name:l,value:v,checked:S,required:x,disabled:p,form:f,style:{transform:"translateX(-100%)"}})]})});v.displayName=u;var g="SwitchThumb",f=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,s=p(g,a);return(0,o.jsx)(c.sG.span,{"data-state":y(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t})});f.displayName=g;var j=r.forwardRef((e,t)=>{let{__scopeSwitch:a,control:s,checked:l,bubbles:n=!0,...c}=e,u=r.useRef(null),h=(0,i.s)(u,t),x=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l),m=(0,d.X)(s);return r.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==l&&t){let a=new Event("click",{bubbles:n});t.call(e,l),e.dispatchEvent(a)}},[x,l,n]),(0,o.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:h,style:{...c.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var b=v,N=f},333:(e,t,a)=>{"use strict";a.d(t,{d:()=>l});var r=a(5155);a(2115);var s=a(239),i=a(9434);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},968:(e,t,a)=>{"use strict";a.d(t,{b:()=>n});var r=a(2115),s=a(3655),i=a(5155),l=r.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},1007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2053:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(5155),s=a(2115),i=a(285),l=a(2523),n=a(5057),d=a(6695),c=a(4416),o=a(1154),u=a(7213);let h=(0,a(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var x=a(8164),m=a(6766);function p(e){let{value:t,onChange:a,label:p="Image",folder:v="portfolio-cms",className:g="",accept:f="image/*",maxSize:j=5}=e,[y,b]=(0,s.useState)(!1),[N,k]=(0,s.useState)(""),[w,A]=(0,s.useState)(""),[C,S]=(0,s.useState)(!1),F=(0,s.useRef)(null),P=async e=>{if(e){if(e.size>1024*j*1024)return void k("File size must be less than ".concat(j,"MB"));if(!e.type.startsWith("image/"))return void k("Please select a valid image file");b(!0),k("");try{let t=new FormData;t.append("file",e),t.append("folder",v);let r=await fetch("/api/upload",{method:"POST",body:t}),s=await r.json();r.ok?a(s.url):k(s.error||"Upload failed")}catch(e){k("Network error occurred")}finally{b(!1)}}},T=()=>{w.trim()&&(a(w.trim()),A(""),S(!1))};return(0,r.jsxs)("div",{className:"space-y-4 ".concat(g),children:[(0,r.jsx)(n.J,{children:p}),t?(0,r.jsx)(d.Zp,{children:(0,r.jsxs)(d.Wu,{className:"p-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.default,{src:t,alt:"Uploaded image",width:300,height:200,className:"w-full h-48 object-cover rounded-lg"}),(0,r.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{a(""),F.current&&(F.current.value="")},children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2 truncate",children:t})]})}):(0,r.jsx)(d.Zp,{children:(0,r.jsxs)(d.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&P(t)},onDragOver:e=>{e.preventDefault()},children:y?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-blue-500 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Drag and drop an image here, or click to select"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=F.current)?void 0:e.click()},children:[(0,r.jsx)(h,{className:"mr-2 h-4 w-4"}),"Upload File"]}),(0,r.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>S(!C),children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Add URL"]})]})]})}),C&&(0,r.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,r.jsx)(l.p,{placeholder:"Enter image URL...",value:w,onChange:e=>A(e.target.value),onKeyPress:e=>"Enter"===e.key&&T()}),(0,r.jsx)(i.$,{type:"button",onClick:T,children:"Add"}),(0,r.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>S(!1),children:"Cancel"})]}),N&&(0,r.jsx)("p",{className:"text-sm text-red-600 mt-2",children:N}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Supports: JPG, PNG, GIF, WebP (max ",j,"MB)"]})]})}),(0,r.jsx)("input",{ref:F,type:"file",accept:f,onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];a&&P(a)},className:"hidden"})]})}},3050:(e,t,a)=>{Promise.resolve().then(a.bind(a,3926))},3926:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var r=a(5155),s=a(2115),i=a(5695),l=a(6913),n=a(2053),d=a(285),c=a(2523),o=a(8539),u=a(5057),h=a(6695),x=a(333),m=a(6126),p=a(5365),v=a(8564),g=a(1154),f=a(7550),j=a(4229),y=a(2657),b=a(1007),N=a(6874),k=a.n(N);function w(){let e,t=(0,i.useRouter)(),a=(0,i.useParams)(),[N,w]=(0,s.useState)(!1),[A,C]=(0,s.useState)(!0),[S,F]=(0,s.useState)(""),[P,T]=(0,s.useState)(""),[E,R]=(0,s.useState)({name:"",avatar:"",role:"",company:"",content:"",rating:5,email:"",linkedinUrl:"",featured:!1,status:"draft",order:0});(0,s.useEffect)(()=>{a.id&&M()},[a.id]);let M=async()=>{try{let e=await fetch("/api/testimonials/".concat(a.id),{credentials:"include"});if(e.ok){let t=(await e.json()).data;R({name:t.name||"",avatar:t.avatar||"",role:t.role||"",company:t.company||"",content:t.content||"",rating:t.rating||5,email:t.email||"",linkedinUrl:t.linkedinUrl||"",featured:t.featured||!1,status:t.status||"draft",order:t.order||0})}else F("Failed to fetch testimonial")}catch(e){F("Network error occurred")}finally{C(!1)}},U=(e,t)=>{R(a=>({...a,[e]:t}))},z=async e=>{w(!0),F(""),T("");try{let r=await fetch("/api/testimonials/".concat(a.id),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({...E,status:e})}),s=await r.json();r.ok?(T("Testimonial ".concat("published"===e?"published":"updated"," successfully!")),setTimeout(()=>{t.push("/dashboard/testimonials")},1500)):F(s.error||"Failed to update testimonial")}catch(e){F("Network error occurred")}finally{w(!1)}};return A?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{children:"Loading testimonial..."})]})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(k(),{href:"/dashboard/testimonials",children:(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Back to Testimonials"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Testimonial"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Update testimonial information"})]})]})}),S&&(0,r.jsx)(p.Fc,{variant:"destructive",children:(0,r.jsx)(p.TN,{children:S})}),P&&(0,r.jsx)(p.Fc,{children:(0,r.jsx)(p.TN,{children:P})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"xl:col-span-3 space-y-8",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:"Person Information"})}),(0,r.jsxs)(h.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.J,{htmlFor:"name",children:"Name *"}),(0,r.jsx)(c.p,{id:"name",value:E.name,onChange:e=>U("name",e.target.value),placeholder:"Enter person's name...",required:!0})]}),(0,r.jsx)("div",{children:(0,r.jsx)(n.A,{label:"Photo (Optional)",value:E.avatar,onChange:e=>U("avatar",e),folder:"testimonials"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.J,{htmlFor:"role",children:"Role/Position *"}),(0,r.jsx)(c.p,{id:"role",value:E.role,onChange:e=>U("role",e.target.value),placeholder:"e.g., CEO, Marketing Director",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(u.J,{htmlFor:"company",children:"Company"}),(0,r.jsx)(c.p,{id:"company",value:E.company,onChange:e=>U("company",e.target.value),placeholder:"e.g., Tech Corp, Creative Agency"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(c.p,{id:"email",type:"email",value:E.email,onChange:e=>U("email",e.target.value),placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(u.J,{htmlFor:"linkedinUrl",children:"LinkedIn URL"}),(0,r.jsx)(c.p,{id:"linkedinUrl",value:E.linkedinUrl,onChange:e=>U("linkedinUrl",e.target.value),placeholder:"https://linkedin.com/in/username"})]})]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:"Testimonial Content"})}),(0,r.jsxs)(h.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.J,{htmlFor:"content",children:"Testimonial Content *"}),(0,r.jsx)(o.T,{id:"content",value:E.content,onChange:e=>U("content",e.target.value),placeholder:"Enter the testimonial content...",rows:6,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(u.J,{htmlFor:"rating",children:"Rating *"}),(0,r.jsxs)("select",{id:"rating",value:E.rating,onChange:e=>U("rating",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",required:!0,children:[(0,r.jsx)("option",{value:5,children:"5 Stars - Excellent"}),(0,r.jsx)("option",{value:4,children:"4 Stars - Very Good"}),(0,r.jsx)("option",{value:3,children:"3 Stars - Good"}),(0,r.jsx)("option",{value:2,children:"2 Stars - Fair"}),(0,r.jsx)("option",{value:1,children:"1 Star - Poor"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6 xl:sticky xl:top-6 xl:h-fit",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:"Settings"})}),(0,r.jsxs)(h.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.d,{id:"featured",checked:E.featured,onCheckedChange:e=>U("featured",e)}),(0,r.jsx)(u.J,{htmlFor:"featured",children:"Featured Testimonial"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(d.$,{onClick:()=>z("draft"),variant:"outline",className:"w-full",disabled:N||!E.name||!E.role||!E.content,children:[N?(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Save as Draft"]}),(0,r.jsxs)(d.$,{onClick:()=>z("published"),className:"w-full",disabled:N||!E.name||!E.role||!E.content,children:[N?(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Update & Publish"]})]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:"Preview"})}),(0,r.jsx)(h.Wu,{children:(0,r.jsx)("div",{className:"bg-gray-50 p-6 rounded-lg border",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-1",children:(e=E.rating,Array.from({length:5},(t,a)=>(0,r.jsx)(v.A,{className:"h-4 w-4 ".concat(a<e?"text-yellow-400 fill-current":"text-gray-300")},a)))}),(0,r.jsxs)("blockquote",{className:"text-gray-700 italic",children:['"',E.content||"Testimonial content will appear here...",'"']}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 pt-4 border-t",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden",children:E.avatar?(0,r.jsx)("img",{src:E.avatar,alt:E.name||"Person",className:"w-full h-full object-cover"}):(0,r.jsx)(b.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:E.name||"Person Name"}),(E.role||E.company)&&(0,r.jsx)("div",{className:"text-xs text-gray-500",children:[E.role,E.company].filter(Boolean).join(" at ")})]})]}),E.featured&&(0,r.jsx)("div",{className:"pt-2",children:(0,r.jsx)(m.E,{className:"text-xs bg-yellow-100 text-yellow-800",children:"Featured"})})]})})})]})]})]})]})})}},4229:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(5155);a(2115);var s=a(968),i=a(9434);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>n,TN:()=>d});var r=a(5155);a(2115);var s=a(2085),i=a(9434);let l=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:a,...s}=e;return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(l({variant:a}),t),...s})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>d});var r=a(5155);a(2115);var s=a(9708),i=a(2085),l=a(9434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,asChild:i=!1,...d}=e,c=i?s.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(n({variant:a}),t),...d})}},7213:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7550:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8164:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,5812,6766,6823,8441,1684,7358],()=>t(3050)),_N_E=e.O()}]);