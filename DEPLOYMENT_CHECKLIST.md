# CORS Fix Deployment Checklist

## Current Issue Analysis

The test results show that the CMS is still returning `localhost:3000` as the allowed origin instead of the requested `https://uttamrimal.com.np`. This indicates:

1. **Environment variables are not set correctly in production**
2. **The deployed CMS is using default/fallback values**
3. **There might be caching issues with the deployment**

## Immediate Action Required

### Step 1: Update CMS Environment Variables

In your CMS deployment platform (Vercel), set these environment variables:

```env
FRONTEND_URL=https://uttamrimal.com.np
NEXT_PUBLIC_FRONTEND_URL=https://uttamrimal.com.np
NODE_ENV=production
```

### Step 2: Add Additional Domains (Optional)

If you have multiple domains or want to support Vercel preview deployments:

```env
ADDITIONAL_CORS_ORIGINS=https://www.uttamrimal.com.np,https://uttam-portfolio.vercel.app,https://uttam-portfolio-git-main.vercel.app
```

### Step 3: Redeploy CMS

After setting environment variables:
1. Go to your CMS deployment dashboard
2. Trigger a new deployment or redeploy
3. Wait for deployment to complete

### Step 4: Verify Environment Variables

Test the environment variables are loaded correctly:

```bash
curl https://uttam-backend.vercel.app/api/cors-test
```

Expected response should show your production domains in `allowedOrigins`.

## Verification Steps

### 1. Test CORS Configuration

```bash
# Test with your production domain
curl -i -X OPTIONS \
  -H "Origin: https://uttamrimal.com.np" \
  -H "Access-Control-Request-Method: GET" \
  https://uttam-backend.vercel.app/api/public/blog

# Expected: Access-Control-Allow-Origin: https://uttamrimal.com.np
```

### 2. Test from Browser

Open your portfolio website and run in console:

```javascript
fetch('https://uttam-backend.vercel.app/api/public/blog')
  .then(response => {
    console.log('CORS Origin:', response.headers.get('access-control-allow-origin'));
    return response.json();
  })
  .then(data => console.log('Success:', data))
  .catch(error => console.error('Error:', error));
```

### 3. Check Client Components

Verify these components load data without CORS errors:
- [ ] Featured Work section (videos)
- [ ] Testimonials section
- [ ] Clients section
- [ ] Blog posts (if any)

## Common Issues & Solutions

### Issue 1: Environment Variables Not Loading

**Symptoms:** Still getting `localhost:3000` in CORS headers

**Solution:**
1. Check deployment platform environment variables
2. Ensure variables are set for production environment
3. Redeploy after setting variables
4. Check deployment logs for any errors

### Issue 2: Caching Issues

**Symptoms:** Changes not reflecting immediately

**Solution:**
1. Clear browser cache
2. Try incognito/private browsing
3. Wait a few minutes for CDN cache to clear
4. Force refresh (Ctrl+F5 or Cmd+Shift+R)

### Issue 3: Multiple Domains

**Symptoms:** Works for one domain but not others

**Solution:**
1. Add all domains to `ADDITIONAL_CORS_ORIGINS`
2. Include both www and non-www versions
3. Include Vercel preview URLs if needed

## Platform-Specific Instructions

### Vercel Deployment

1. Go to your CMS project in Vercel dashboard
2. Navigate to Settings → Environment Variables
3. Add the required variables:
   - `FRONTEND_URL` → `https://uttamrimal.com.np`
   - `NODE_ENV` → `production`
4. Click "Save"
5. Go to Deployments tab
6. Click "..." on latest deployment → "Redeploy"

### Netlify Deployment

1. Go to Site Settings → Environment Variables
2. Add the variables
3. Trigger a new deploy

### Other Platforms

Check your platform's documentation for setting environment variables and redeploying.

## Success Criteria

- [ ] CORS test returns correct origin
- [ ] No CORS errors in browser console
- [ ] All API endpoints return proper CORS headers
- [ ] Client components load data successfully
- [ ] Portfolio website functions normally

## Rollback Plan

If issues persist:

1. **Temporary Fix:** Set `FRONTEND_URL=*` to allow all origins
2. **Debug:** Check deployment logs for errors
3. **Contact Support:** If platform-specific issues

## Next Steps After Fix

1. Monitor for any remaining CORS issues
2. Test on different browsers
3. Test on mobile devices
4. Consider implementing error boundaries for API failures
5. Add monitoring/alerting for API health

## Contact Information

If you need help with deployment platform specifics:
- Vercel: Check Vercel documentation or support
- Netlify: Check Netlify documentation or support
- Custom hosting: Check your hosting provider's docs
