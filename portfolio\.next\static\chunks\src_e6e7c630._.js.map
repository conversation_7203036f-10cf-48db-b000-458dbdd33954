{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/layout/ScrollToTop.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <Button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n      aria-label=\"Scroll to top\"\n    >\n      <ArrowUp size={20} />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,OAAO,WAAW,GAAG,KAAK;wBAC5B,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,6LAAC,+MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;;;;;;AAGrB;GApCwB;KAAA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientsPageHeader.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { <PERSON><PERSON><PERSON><PERSON>, Users, Handshake } from 'lucide-react';\n\nexport default function ClientsPageHeader() {\n  return (\n    <div className=\"relative bg-muted text-foreground py-20 overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-20 h-20 border border-foreground/20 rounded-full\"></div>\n        <div className=\"absolute top-32 right-20 w-16 h-16 border border-foreground/20 rounded-full\"></div>\n        <div className=\"absolute bottom-20 left-1/4 w-12 h-12 border border-foreground/20 rounded-full\"></div>\n        <div className=\"absolute bottom-32 right-1/3 w-24 h-24 border border-foreground/20 rounded-full\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <Link\n          href=\"/#clients\"\n          className=\"inline-flex items-center gap-2 text-accent hover:text-foreground transition-colors mb-8 group\"\n        >\n          <ArrowLeft size={20} className=\"group-hover:-translate-x-1 transition-transform duration-300\" />\n          Back to Portfolio\n        </Link>\n        \n        <div className=\"max-w-4xl\">\n          <div className=\"flex items-center gap-4 mb-6\">\n            <div className=\"p-4 bg-foreground/10 backdrop-blur-sm rounded-2xl\">\n              <Users size={32} className=\"text-foreground\" />\n            </div>\n            <div className=\"p-4 bg-foreground/10 backdrop-blur-sm rounded-2xl\">\n              <Handshake size={32} className=\"text-foreground\" />\n            </div>\n          </div>\n\n          <h1 className=\"text-secondary text-4xl md:text-5xl font-heading font-bold mb-4\">\n            Our Clients\n          </h1>\n          <p className=\"text-xl text-muted-foreground max-w-2xl mb-6\">\n            Building Success Together\n          </p>\n\n          <p className=\"text-xl text-muted-foreground max-w-2xl\">\n            Proud to have collaborated with diverse clients across various industries, building lasting partnerships through exceptional video editing services and creative storytelling.\n          </p>\n\n          <div className=\"flex flex-wrap gap-4 mt-8\">\n            <div className=\"bg-foreground/10 backdrop-blur-sm px-4 py-2 rounded-full\">\n              <span className=\"text-sm font-medium\">🎬 Video Editing</span>\n            </div>\n            <div className=\"bg-foreground/10 backdrop-blur-sm px-4 py-2 rounded-full\">\n              <span className=\"text-sm font-medium\">📱 Social Media Content</span>\n            </div>\n            <div className=\"bg-foreground/10 backdrop-blur-sm px-4 py-2 rounded-full\">\n              <span className=\"text-sm font-medium\">🎯 Brand Storytelling</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAAiE;;;;;;;kCAIlG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAE7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAInC,6LAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAGhF,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;0CAIvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;KAxDwB", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientsFilter.tsx"], "sourcesContent": ["'use client';\n\nimport { Filter, Grid, List } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\n\ninterface ClientsFilterProps {\n  industries: string[];\n  selectedIndustry: string;\n  onIndustryChange: (industry: string) => void;\n  viewMode: 'grid' | 'list';\n  onViewModeChange: (mode: 'grid' | 'list') => void;\n  totalClients: number;\n}\n\nexport default function ClientsFilter({\n  industries,\n  selectedIndustry,\n  onIndustryChange,\n  viewMode,\n  onViewModeChange,\n  totalClients,\n}: ClientsFilterProps) {\n  return (\n    <div className=\"bg-card rounded-xl shadow-sm border p-6 mb-8\">\n      <div className=\"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6\">\n        {/* Filter Section */}\n        <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-4\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-primary/10 rounded-lg\">\n              <Filter size={20} className=\"text-primary\" />\n            </div>\n            <span className=\"font-semibold text-primary text-lg\">\n              Filter Clients\n            </span>\n          </div>\n          \n          <div className=\"flex items-center gap-3\">\n            <span className=\"text-sm font-medium text-gray-600 whitespace-nowrap\">\n              Industry:\n            </span>\n            <Select value={selectedIndustry} onValueChange={onIndustryChange}>\n              <SelectTrigger className=\"w-48 border-gray-200 focus:border-primary\">\n                <SelectValue placeholder=\"Select industry\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Industries</SelectItem>\n                {industries.map((industry) => (\n                  <SelectItem key={industry} value={industry}>\n                    {industry}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n\n        {/* Results Count & View Mode */}\n        <div className=\"flex items-center gap-6\">\n          <div className=\"text-sm text-gray-600\">\n            <span className=\"font-semibold text-primary\">{totalClients}</span>\n            {' '}client{totalClients !== 1 ? 's' : ''} found\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-sm font-medium text-gray-600 mr-2\">View:</span>\n            <Button\n              variant={viewMode === 'grid' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => onViewModeChange('grid')}\n              className=\"h-9 w-9 p-0\"\n            >\n              <Grid size={16} />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => onViewModeChange('list')}\n              className=\"h-9 w-9 p-0\"\n            >\n              <List size={16} />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAee,SAAS,cAAc,EACpC,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACO;IACnB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAE9B,6LAAC;oCAAK,WAAU;8CAAqC;;;;;;;;;;;;sCAKvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAsD;;;;;;8CAGtE,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAkB,eAAe;;sDAC9C,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;gDACvB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,aAAU;wDAAgB,OAAO;kEAC/B;uDADc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA8B;;;;;;gCAC7C;gCAAI;gCAAO,iBAAiB,IAAI,MAAM;gCAAG;;;;;;;sCAG5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAyC;;;;;;8CACzD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,SAAS,YAAY;oCAC3C,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CAEV,cAAA,6LAAC,4MAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,SAAS,YAAY;oCAC3C,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;KAxEwB", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n        success:\n          \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40\",\n        info:\n          \"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientCard.tsx"], "sourcesContent": ["import Image from 'next/image';\nimport { ExternalLink } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { type Client } from '@/lib/api';\nimport clsx from 'clsx';\n\ninterface ClientCardProps {\n  client: Client;\n  variant?: 'logo' | 'detailed' | 'compact';\n  onClick?: () => void;\n  showWebsite?: boolean;\n  className?: string;\n}\n\nconst cardStyles = {\n  logo: 'group relative bg-white p-4 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-full h-full flex items-center justify-center',\n  detailed: 'bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',\n  compact: 'bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'\n};\n\nconst imageSizes = {\n  logo: { className: 'relative h-16 w-full max-w-[120px] mx-auto', size: '16vw' },\n  detailed: { className: 'relative h-24 w-full max-w-[200px] mx-auto mb-6', size: '33vw' },\n  compact: { className: 'relative h-12 w-full max-w-[100px] mx-auto mb-3', size: '33vw' }\n};\n\nconst contentPadding = {\n  detailed: 'p-6 text-center',\n  compact: 'p-4'\n};\n\nfunction ClientImage({ client, variant }: { client: Client; variant: keyof typeof imageSizes }) {\n  return (\n    <div className={imageSizes[variant].className}>\n      <Image\n        src={client.logo}\n        alt={client.name}\n        fill\n        className={clsx('object-contain', variant === 'detailed' && 'rounded-md')}\n        sizes={imageSizes[variant].size}\n      />\n    </div>\n  );\n}\n\nfunction ClientBadges({ client }: { client: Client }) {\n  return (\n    <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n      {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n      {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n    </div>\n  );\n}\n\nexport default function ClientCard({\n  client,\n  variant = 'logo',\n  onClick,\n  showWebsite = false,\n  className = ''\n}: ClientCardProps) {\n  if (variant === 'logo') {\n    return (\n      <button\n        onClick={onClick}\n        className={clsx(\n          'group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-40 h-40 flex flex-col items-center justify-center text-center',\n          className\n        )}\n        title={client.name}\n        aria-label={`View ${client.name}`}\n      >\n        <div className=\"relative w-24 h-24 mb-4\">\n          <Image\n            src={client.logo}\n            alt={client.name}\n            fill\n            className=\"object-cover rounded-full border border-gray-200 shadow-sm\"\n            sizes=\"96px\"\n          />\n        </div>\n        <p className=\"text-sm font-medium text-gray-700\">{client.name}</p>\n      </button>\n    );\n  }\n  if (variant === 'detailed') {\n    return (\n      <article\n        className={clsx(\n          ' transition-all duration-300 w-full max-w-md overflow-hidden',\n          className\n        )}\n      >\n        <div className=\"p-6 flex flex-col items-center text-center\">\n          <div className=\"relative w-28 h-28 mb-4\">\n            <Image\n              src={client.logo}\n              alt={client.name}\n              fill\n              className=\"object-cover rounded-xl border border-gray-200 shadow-sm\"\n              sizes=\"112px\"\n            />\n          </div>\n\n          <h3 className=\"text-2xl font-heading font-bold text-portfolio-primary mb-2\">\n            {client.name}\n          </h3>\n\n          <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n            {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n            {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n          </div>\n\n          <p className=\"text-muted-foreground leading-relaxed mb-6\">\n            {client.description}\n          </p>\n\n          {showWebsite && client.website && (\n            <Button variant=\"outline\" asChild>\n              <a\n                href={client.website}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center gap-2\"\n              >\n                Visit Website <ExternalLink size={16} />\n              </a>\n            </Button>\n          )}\n        </div>\n      </article>\n    );\n  }\n\n\n  // Compact\n  return (\n    <article className={clsx(cardStyles.compact, className)}>\n      <div className={contentPadding.compact}>\n        <ClientImage client={client} variant=\"compact\" />\n        <h4 className=\"text-lg font-heading font-semibold text-foreground mb-2\">{client.name}</h4>\n        <ClientBadges client={client} />\n        <p className=\"text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3\">\n          {client.description}\n        </p>\n        <div className=\"flex items-center justify-between\">\n          {onClick && (\n            <button\n              onClick={onClick}\n              className=\"text-foreground font-semibold text-sm hover:text-accent transition-colors duration-300\"\n              aria-label={`Learn more about ${client.name}`}\n            >\n              Learn More →\n            </button>\n          )}\n          {showWebsite && client.website && (\n            <a\n              href={client.website}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-portfolio-secondary hover:text-portfolio-primary transition-colors duration-300\"\n              aria-label={`Visit ${client.name} website`}\n            >\n              <ExternalLink size={16} />\n            </a>\n          )}\n        </div>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAUA,MAAM,aAAa;IACjB,MAAM;IACN,UAAU;IACV,SAAS;AACX;AAEA,MAAM,aAAa;IACjB,MAAM;QAAE,WAAW;QAA8C,MAAM;IAAO;IAC9E,UAAU;QAAE,WAAW;QAAmD,MAAM;IAAO;IACvF,SAAS;QAAE,WAAW;QAAmD,MAAM;IAAO;AACxF;AAEA,MAAM,iBAAiB;IACrB,UAAU;IACV,SAAS;AACX;AAEA,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAwD;IAC5F,qBACE,6LAAC;QAAI,WAAW,UAAU,CAAC,QAAQ,CAAC,SAAS;kBAC3C,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAK,OAAO,IAAI;YAChB,KAAK,OAAO,IAAI;YAChB,IAAI;YACJ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,kBAAkB,YAAY,cAAc;YAC5D,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI;;;;;;;;;;;AAIvC;KAZS;AAcT,SAAS,aAAa,EAAE,MAAM,EAAsB;IAClD,qBACE,6LAAC;QAAI,WAAU;;YACZ,OAAO,QAAQ,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAW,OAAO,QAAQ;;;;;;YAC5D,OAAO,WAAW,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAa,OAAO,WAAW;;;;;;;;;;;;AAG3E;MAPS;AASM,SAAS,WAAW,EACjC,MAAM,EACN,UAAU,MAAM,EAChB,OAAO,EACP,cAAc,KAAK,EACnB,YAAY,EAAE,EACE;IAChB,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YACC,SAAS;YACT,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,kLACA;YAEF,OAAO,OAAO,IAAI;YAClB,cAAY,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE;;8BAEjC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,OAAO,IAAI;wBAChB,KAAK,OAAO,IAAI;wBAChB,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;;;;;;8BAGV,6LAAC;oBAAE,WAAU;8BAAqC,OAAO,IAAI;;;;;;;;;;;;IAGnE;IACA,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,gEACA;sBAGF,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,OAAO,IAAI;4BAChB,KAAK,OAAO,IAAI;4BAChB,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;;;;;;;kCAIV,6LAAC;wBAAG,WAAU;kCACX,OAAO,IAAI;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,QAAQ,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW,OAAO,QAAQ;;;;;;4BAC5D,OAAO,WAAW,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAa,OAAO,WAAW;;;;;;;;;;;;kCAGvE,6LAAC;wBAAE,WAAU;kCACV,OAAO,WAAW;;;;;;oBAGpB,eAAe,OAAO,OAAO,kBAC5B,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,OAAO;kCAC/B,cAAA,6LAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;;gCACX;8CACe,6LAAC,yNAAA,CAAA,eAAY;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhD;IAGA,UAAU;IACV,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,OAAO,EAAE;kBAC3C,cAAA,6LAAC;YAAI,WAAW,eAAe,OAAO;;8BACpC,6LAAC;oBAAY,QAAQ;oBAAQ,SAAQ;;;;;;8BACrC,6LAAC;oBAAG,WAAU;8BAA2D,OAAO,IAAI;;;;;;8BACpF,6LAAC;oBAAa,QAAQ;;;;;;8BACtB,6LAAC;oBAAE,WAAU;8BACV,OAAO,WAAW;;;;;;8BAErB,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAY,CAAC,iBAAiB,EAAE,OAAO,IAAI,EAAE;sCAC9C;;;;;;wBAIF,eAAe,OAAO,OAAO,kBAC5B,6LAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;4BACV,cAAY,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC;sCAE1C,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;MApHwB", "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/FeaturedClientsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Star } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport ClientCard from './ClientCard';\nimport { type Client } from '@/lib/api';\n\ninterface FeaturedClientsSectionProps {\n  featuredClients: Client[];\n  allClients: Client[];\n}\n\nexport default function FeaturedClientsSection({\n  featuredClients,\n  allClients\n}: FeaturedClientsSectionProps) {\n  const [selectedClient, setSelectedClient] = useState<string | null>(null);\n\n  const openClientModal = (clientId: string) => {\n    setSelectedClient(clientId);\n  };\n\n  const selectedClientData = allClients.find(client => client._id === selectedClient);\n\n  if (featuredClients.length === 0) {\n    return null;\n  }\n\n  return (\n    <section className=\"mb-16\">\n      <div className=\"flex items-center gap-3 mb-8\">\n        <div className=\"p-3 bg-gradient-to-r from-warning to-warning/80 rounded-xl\">\n          <Star size={24} className=\"text-warning-foreground\" />\n        </div>\n        <div>\n          <h2 className=\"text-3xl font-heading font-bold text-primary\">\n            Featured Partnerships\n          </h2>\n          <p className=\"text-muted-foreground mt-1\">\n            Our most valued collaborations and success stories\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {featuredClients.map((client) => (\n          <Dialog key={client._id}>\n            <DialogTrigger asChild>\n              <div className=\"group relative\">\n                <ClientCard\n                  client={client}\n                  variant=\"compact\"\n                  onClick={() => openClientModal(client._id)}\n                  showWebsite={false}\n                  className=\"hover:shadow-xl transition-all duration-500 hover:-translate-y-2\"\n                />\n                {/* Featured Badge */}\n                <div className=\"absolute top-4 right-4 gradient-featured text-accent-foreground px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-lg\">\n                  <Star size={12} />\n                  Featured\n                </div>\n              </div>\n            </DialogTrigger>\n            <DialogContent className=\"max-w-2xl\">\n              {selectedClient === client._id && selectedClientData && (\n                <ClientCard\n                  client={selectedClientData}\n                  variant=\"detailed\"\n                  showWebsite={true}\n                />\n              )}\n            </DialogContent>\n          </Dialog>\n        ))}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,uBAAuB,EAC7C,eAAe,EACf,UAAU,EACkB;;IAC5B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,WAAW,IAAI,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;IAEpE,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;kCAE5B,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC,qIAAA,CAAA,SAAM;;0CACL,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,0JAAA,CAAA,UAAU;4CACT,QAAQ;4CACR,SAAQ;4CACR,SAAS,IAAM,gBAAgB,OAAO,GAAG;4CACzC,aAAa;4CACb,WAAU;;;;;;sDAGZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;0CAKxB,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACtB,mBAAmB,OAAO,GAAG,IAAI,oCAChC,6LAAC,0JAAA,CAAA,UAAU;oCACT,QAAQ;oCACR,SAAQ;oCACR,aAAa;;;;;;;;;;;;uBAtBR,OAAO,GAAG;;;;;;;;;;;;;;;;AA+BjC;GAjEwB;KAAA", "debugId": null}}, {"offset": {"line": 1540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientsGrid.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Building } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport ClientCard from './ClientCard';\nimport { type Client } from '@/lib/api';\n\ninterface ClientsGridProps {\n  clients: Client[];\n  allClients: Client[];\n  viewMode: 'grid' | 'list';\n  industryFilter: string;\n}\n\nexport default function ClientsGrid({ \n  clients, \n  allClients, \n  viewMode, \n  industryFilter \n}: ClientsGridProps) {\n  const [selectedClient, setSelectedClient] = useState<string | null>(null);\n\n  const openClientModal = (clientId: string) => {\n    setSelectedClient(clientId);\n  };\n\n  const selectedClientData = allClients.find(client => client._id === selectedClient);\n\n  if (clients.length === 0) {\n    return (\n      <div className=\"text-center py-16\">\n        <div className=\"bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6\">\n          <Building size={48} className=\"text-gray-400\" />\n        </div>\n        <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n          No clients found\n        </h3>\n        <p className=\"text-muted-foreground\">\n          {industryFilter === 'all' \n            ? 'No clients available at the moment.' \n            : `No clients found in the ${industryFilter} industry.`\n          }\n        </p>\n      </div>\n    );\n  }\n\n  const gridClasses = viewMode === 'grid' \n    ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\" \n    : \"space-y-6\";\n\n  return (\n    <div className={gridClasses}>\n      {clients.map((client, index) => (\n        <Dialog key={client._id}>\n          <DialogTrigger asChild>\n            <div \n              className=\"group\"\n              style={{\n                animationDelay: `${index * 100}ms`,\n              }}\n            >\n              <ClientCard\n                client={client}\n                variant={viewMode === 'grid' ? 'compact' : 'compact'}\n                onClick={() => openClientModal(client._id)}\n                showWebsite={viewMode === 'list'}\n                className={`\n                  hover:shadow-xl transition-all duration-500 hover:-translate-y-1\n                  ${viewMode === 'list' ? 'flex items-center gap-6 p-6' : ''}\n                  animate-fade-in-up\n                `}\n              />\n            </div>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl\">\n            {selectedClient === client._id && selectedClientData && (\n              <ClientCard\n                client={selectedClientData}\n                variant=\"detailed\"\n                showWebsite={true}\n              />\n            )}\n          </DialogContent>\n        </Dialog>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAee,SAAS,YAAY,EAClC,OAAO,EACP,UAAU,EACV,QAAQ,EACR,cAAc,EACG;;IACjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,WAAW,IAAI,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;IAEpE,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAI,WAAU;;;;;;;;;;;8BAEhC,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAGzD,6LAAC;oBAAE,WAAU;8BACV,mBAAmB,QAChB,wCACA,CAAC,wBAAwB,EAAE,eAAe,UAAU,CAAC;;;;;;;;;;;;IAKjE;IAEA,MAAM,cAAc,aAAa,SAC7B,yDACA;IAEJ,qBACE,6LAAC;QAAI,WAAW;kBACb,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,qIAAA,CAAA,SAAM;;kCACL,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,OAAO;kCACpB,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;4BACpC;sCAEA,cAAA,6LAAC,0JAAA,CAAA,UAAU;gCACT,QAAQ;gCACR,SAAS,aAAa,SAAS,YAAY;gCAC3C,SAAS,IAAM,gBAAgB,OAAO,GAAG;gCACzC,aAAa,aAAa;gCAC1B,WAAW,CAAC;;kBAEV,EAAE,aAAa,SAAS,gCAAgC,GAAG;;gBAE7D,CAAC;;;;;;;;;;;;;;;;kCAIP,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACtB,mBAAmB,OAAO,GAAG,IAAI,oCAChC,6LAAC,0JAAA,CAAA,UAAU;4BACT,QAAQ;4BACR,SAAQ;4BACR,aAAa;;;;;;;;;;;;eA1BR,OAAO,GAAG;;;;;;;;;;AAkC/B;GA1EwB;KAAA", "debugId": null}}, {"offset": {"line": 1682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/features/clients/ClientsStats.tsx"], "sourcesContent": ["import { Building, Globe, Eye, Heart } from 'lucide-react';\n\ninterface StatItem {\n  number: string;\n  label: string;\n  icon: React.ReactNode;\n  color: string;\n}\n\ninterface ClientsStatsProps {\n  className?: string;\n}\n\nexport default function ClientsStats({ className = '' }: ClientsStatsProps) {\n  const stats: StatItem[] = [\n    {\n      number: '50+',\n      label: 'Projects Completed',\n      icon: <Building size={24} />,\n      color: 'text-info'\n    },\n    {\n      number: '25+',\n      label: 'Happy Clients',\n      icon: <Heart size={24} />,\n      color: 'text-destructive'\n    },\n    {\n      number: '1M+',\n      label: 'Views Generated',\n      icon: <Eye size={24} />,\n      color: 'text-success'\n    },\n    {\n      number: '98%',\n      label: 'Client Satisfaction',\n      icon: <Globe size={24} />,\n      color: 'text-accent'\n    },\n  ];\n\n  return (\n    <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 ${className}`}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 text-center group hover:scale-105\"\n        >\n          <div className={`flex justify-center mb-4 ${stat.color} group-hover:scale-110 transition-transform duration-300`}>\n            {stat.icon}\n          </div>\n          <div className=\"text-3xl md:text-4xl font-heading font-bold text-accent mb-2 group-hover:text-primary transition-colors duration-300\">\n            {stat.number}\n          </div>\n          <div className=\"text-muted-foreground font-medium text-sm md:text-base\">\n            {stat.label}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAae,SAAS,aAAa,EAAE,YAAY,EAAE,EAAqB;IACxE,MAAM,QAAoB;QACxB;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YACtB,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;YACjB,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;kBACjE,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAEC,WAAU;;kCAEV,6LAAC;wBAAI,WAAW,CAAC,yBAAyB,EAAE,KAAK,KAAK,CAAC,wDAAwD,CAAC;kCAC7G,KAAK,IAAI;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;kCACZ,KAAK,MAAM;;;;;;kCAEd,6LAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK;;;;;;;eAVR;;;;;;;;;;AAgBf;KAhDwB", "debugId": null}}, {"offset": {"line": 1796, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/app/clients/ClientsPageClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport ClientsPageHeader from '@/components/features/clients/ClientsPageHeader';\nimport ClientsFilter from '@/components/features/clients/ClientsFilter';\nimport FeaturedClientsSection from '@/components/features/clients/FeaturedClientsSection';\nimport ClientsGrid from '@/components/features/clients/ClientsGrid';\nimport ClientsStats from '@/components/features/clients/ClientsStats';\nimport { type Client } from '@/lib/api';\n\ninterface ClientsPageClientProps {\n  allClients: Client[];\n  featuredClients: Client[];\n}\n\nexport default function ClientsPageClient({ allClients, featuredClients }: ClientsPageClientProps) {\n  const [filteredClients, setFilteredClients] = useState<Client[]>(allClients);\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [industryFilter, setIndustryFilter] = useState<string>('all');\n\n  // Get unique industries\n  const industries = Array.from(new Set(allClients.map(client => client.industry).filter(Boolean))) as string[];\n\n  const handleIndustryFilter = (industry: string) => {\n    setIndustryFilter(industry);\n    if (industry === 'all') {\n      setFilteredClients(allClients);\n    } else {\n      setFilteredClients(allClients.filter(client => client.industry === industry));\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <ClientsPageHeader />\n\n      <div className=\"container mx-auto px-4 py-16\">\n        {/* Featured Clients */}\n        <FeaturedClientsSection\n          featuredClients={featuredClients}\n          allClients={allClients}\n        />\n\n        {/* Filters and View Controls */}\n        <ClientsFilter\n          industries={industries}\n          selectedIndustry={industryFilter}\n          onIndustryChange={handleIndustryFilter}\n          viewMode={viewMode}\n          onViewModeChange={setViewMode}\n          totalClients={filteredClients.length}\n        />\n\n        {/* All Clients */}\n        <section>\n          <h2 className=\"text-3xl font-heading font-bold text-primary mb-8\">\n            {industryFilter === 'all' ? 'All Clients' : `${industryFilter} Clients`}\n            <span className=\"text-lg font-normal text-muted-foreground ml-2\">\n              ({filteredClients.length} clients)\n            </span>\n          </h2>\n\n          <ClientsGrid\n            clients={filteredClients}\n            allClients={allClients}\n            viewMode={viewMode}\n            industryFilter={industryFilter}\n          />\n        </section>\n\n        {/* Stats Section */}\n        <ClientsStats className=\"mt-20\" />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAee,SAAS,kBAAkB,EAAE,UAAU,EAAE,eAAe,EAA0B;;IAC/F,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,wBAAwB;IACxB,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,SAAU,OAAO,QAAQ,EAAE,MAAM,CAAC;IAEvF,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAClB,IAAI,aAAa,OAAO;YACtB,mBAAmB;QACrB,OAAO;YACL,mBAAmB,WAAW,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;QACrE;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,iKAAA,CAAA,UAAiB;;;;;0BAElB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,sKAAA,CAAA,UAAsB;wBACrB,iBAAiB;wBACjB,YAAY;;;;;;kCAId,6LAAC,6JAAA,CAAA,UAAa;wBACZ,YAAY;wBACZ,kBAAkB;wBAClB,kBAAkB;wBAClB,UAAU;wBACV,kBAAkB;wBAClB,cAAc,gBAAgB,MAAM;;;;;;kCAItC,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCACX,mBAAmB,QAAQ,gBAAgB,GAAG,eAAe,QAAQ,CAAC;kDACvE,6LAAC;wCAAK,WAAU;;4CAAiD;4CAC7D,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;;0CAI7B,6LAAC,2JAAA,CAAA,UAAW;gCACV,SAAS;gCACT,YAAY;gCACZ,UAAU;gCACV,gBAAgB;;;;;;;;;;;;kCAKpB,6LAAC,4JAAA,CAAA,UAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIhC;GA7DwB;KAAA", "debugId": null}}]}