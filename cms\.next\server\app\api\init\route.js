(()=>{var e={};e.id=1288,e.ids=[1288],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>o,HU:()=>l,b9:()=>u,oC:()=>m,rL:()=>d});var s=t(43205),n=t.n(s),i=t(85663);let a=process.env.JWT_SECRET;if(!a)throw Error("Please define the JWT_SECRET environment variable");let o=async e=>await i.Ay.hash(e,12),u=async(e,r)=>await i.Ay.compare(e,r),l=e=>n().sign(e,a,{expiresIn:"7d"}),c=e=>{try{return n().verify(e,a)}catch(e){return null}},p=e=>{let r=e.headers.get("authorization");return r&&r.startsWith("Bearer ")?r.substring(7):e.cookies.get("auth-token")?.value||null},d=async e=>{let r=p(e);return r?c(r):null},m=e=>async(r,t)=>{let s=await d(r);return s?(r.user=s,e(r,t)):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(56037),n=t.n(s);let i=new s.Schema({email:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},password:{type:String,required:!0,minlength:6},name:{type:String,required:!0,trim:!0},role:{type:String,enum:["admin","editor"],default:"editor"},isActive:{type:Boolean,default:!0},lastLogin:{type:Date}},{timestamps:!0});i.index({email:1}),i.index({role:1});let a=n().models.User||n().model("User",i)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),n=t.n(s);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose||{conn:null,promise:null};global.mongoose||(global.mongoose=a);let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(i,{bufferCommands:!1}).then(e=>(console.log("✅ Connected to MongoDB"),e)));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},89832:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>p});var n=t(96559),i=t(48088),a=t(37719),o=t(32190),u=t(75745),l=t(17063),c=t(12909);async function p(){try{await (0,u.A)();let e=process.env.ADMIN_EMAIL||"<EMAIL>",r=process.env.ADMIN_PASSWORD||"admin123";if(await l.A.findOne({email:e}))return o.NextResponse.json({success:!0,message:"Admin user already exists",credentials:{email:e,password:r}});let t=await (0,c.Er)(r),s=new l.A({email:e,password:t,name:"Admin User",role:"admin",isActive:!0});return await s.save(),o.NextResponse.json({success:!0,message:"Admin user created successfully",credentials:{email:e,password:r}})}catch(e){return console.error("Error initializing admin user:",e),o.NextResponse.json({error:"Failed to initialize admin user"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/init/route",pathname:"/api/init",filename:"route",bundlePath:"app/api/init/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\cms\\src\\app\\api\\init\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:v}=d;function x(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315],()=>t(89832));module.exports=s})();