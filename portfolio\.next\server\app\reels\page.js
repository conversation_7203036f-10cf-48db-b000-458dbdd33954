(()=>{var e={};e.id=972,e.ids=[972],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},28370:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["reels",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,97574)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reels/page",pathname:"/reels",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31646:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(43210),o=r(2975),i=r(29523);function n(){let[e,t]=(0,a.useState)(!1);return e?(0,s.jsx)(i.$,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-8 z-50 w-12 h-12 bg-secondary hover:bg-secondary/90 text-secondary-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300","aria-label":"Scroll to top",children:(0,s.jsx)(o.A,{size:20})}):null}},33484:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\components\\\\layout\\\\ScrollToTop.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\components\\layout\\ScrollToTop.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},45475:(e,t,r)=>{Promise.resolve().then(r.bind(r,84206)),Promise.resolve().then(r.bind(r,92830)),Promise.resolve().then(r.bind(r,94743)),Promise.resolve().then(r.bind(r,31646))},52343:(e,t,r)=>{"use strict";r.d(t,{GB:()=>u,HI:()=>p,Lf:()=>h,OA:()=>c,Sk:()=>i,fY:()=>l,g6:()=>o,rT:()=>d,t1:()=>n});let s="https://uttam-backend.vercel.app";async function a(e){try{let t=await fetch(`${s}/api${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return console.error(`Failed to fetch ${e}:`,t.status,t.statusText),[];let r=await t.json();if(!r.success)return console.error(`API error for ${e}:`,r.message),[];return r.data||[]}catch(t){return console.error(`Network error fetching ${e}:`,t),[]}}async function o(){return(await a("/blog")).filter(e=>"published"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}async function i(){return(await o()).filter(e=>e.featured)}async function n(e){try{let t=await fetch(`${s}/api/blog/slug/${e}`,{method:"GET",headers:{"Content-Type":"application/json"},next:{revalidate:300}});if(!t.ok)return null;let r=await t.json();return r.success?r.data:null}catch(e){return console.error("Error fetching blog post by slug:",e),null}}async function l(){return(await a("/videos")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function d(){return(await l()).filter(e=>e.featured)}async function c(){return(await a("/reels")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function u(){return(await c()).filter(e=>e.featured)}async function h(){return(await a("/clients")).filter(e=>"published"===e.status).sort((e,t)=>e.order-t.order)}async function p(){return(await h()).filter(e=>e.featured)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>h,HM:()=>c,lG:()=>n,zM:()=>l});var s=r(60687);r(43210);var a=r(37908),o=r(11860),i=r(4780);function n({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({...e}){return(0,s.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function h({className:e,children:t,...r}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(u,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(o.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}},69451:(e,t,r)=>{Promise.resolve().then(r.bind(r,97868)),Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926)),Promise.resolve().then(r.bind(r,33484))},79551:e=>{"use strict";e.exports=require("url")},84206:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var s=r(60687),a=r(43210),o=r(85814),i=r.n(o),n=r(30474),l=r(97840),d=r(28559),c=r(11860),u=r(6943),h=r(25366),p=r(63503),m=r(96834),f=r(29523);function x({allReels:e,featuredReels:t}){let[r,o]=(0,a.useState)(null),[x]=(0,a.useState)(e),[g,v]=(0,a.useState)("grid"),b=()=>(0,s.jsx)(l.A,{size:16}),j=()=>"bg-accent";return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("div",{className:"gradient-secondary text-foreground py-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)(i(),{href:"/#shorts",className:"inline-flex items-center gap-2 text-accent hover:text-foreground transition-colors mb-8 font-accent",children:[(0,s.jsx)(d.A,{size:20}),"Back to Portfolio"]}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl text-foreground font-heading font-bold mb-4",children:"YouTube Shorts Collection"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl font-body",children:"Discover my creative short-form content on YouTube. Engaging shorts that showcase dynamic editing and storytelling in under 60 seconds."})]})}),(0,s.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[t.length>0&&(0,s.jsxs)("section",{className:"mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl font-heading font-bold text-foreground mb-8",children:"Featured YouTube Shorts"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6",children:t.map(e=>(0,s.jsxs)(p.lG,{open:r===e.id,onOpenChange:e=>{e||o(null)},children:[(0,s.jsx)(p.zM,{asChild:!0,children:(0,s.jsxs)("div",{onClick:()=>o(e.id),className:"group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer",children:[(0,s.jsxs)("div",{className:"relative aspect-[9/16] overflow-hidden",children:[(0,s.jsx)(n.default,{src:e.thumbnail||"",alt:e.title,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center",children:(0,s.jsx)("div",{className:"bg-accent/90 backdrop-blur-sm rounded-full p-3 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-accent-foreground ml-0.5",fill:"currentColor"})})}),(0,s.jsx)("div",{className:"absolute top-3 left-3",children:(0,s.jsx)(m.E,{variant:"default",className:"bg-accent text-accent-foreground text-xs font-accent",children:"Featured"})}),(0,s.jsx)("div",{className:"absolute top-3 right-3",children:(0,s.jsx)("div",{className:`${j()} text-primary-foreground p-1.5 rounded-full`,children:b()})})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"text-sm font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 line-clamp-2",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-muted-foreground text-xs leading-relaxed line-clamp-2",children:e.description})]})]})}),(0,s.jsx)(p.Cf,{className:"max-w-lg w-[95vw] p-0 bg-black border-0",children:(0,s.jsxs)("div",{className:"relative aspect-[9/16] max-h-[85vh] w-full",children:[(0,s.jsx)(p.HM,{asChild:!0,children:(0,s.jsx)(f.$,{variant:"ghost",size:"icon",className:"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2",children:(0,s.jsx)(c.A,{size:24})})}),r===e.id&&e.embedUrl&&(0,s.jsx)("iframe",{src:e.embedUrl,title:e.title,className:"w-full h-full rounded-lg",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})]})})]},e._id))})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.A,{size:20,className:"text-red-600"}),(0,s.jsx)("span",{className:"font-medium text-primary",children:"YouTube Shorts"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.$,{variant:"grid"===g?"default":"outline",size:"sm",onClick:()=>v("grid"),children:(0,s.jsx)(u.A,{size:16})}),(0,s.jsx)(f.$,{variant:"list"===g?"default":"outline",size:"sm",onClick:()=>v("list"),children:(0,s.jsx)(h.A,{size:16})})]})]}),(0,s.jsxs)("section",{children:[(0,s.jsxs)("h2",{className:"text-3xl font-heading font-bold text-primary mb-8",children:["All YouTube Shorts",(0,s.jsxs)("span",{className:"text-lg font-normal text-muted-foreground ml-2",children:["(",x.length," shorts)"]})]}),0===x.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-lg text-muted-foreground",children:"No YouTube Shorts available at the moment."})}):(0,s.jsx)("div",{className:"grid"===g?"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:x.map(e=>(0,s.jsxs)(p.lG,{open:r===e.id,onOpenChange:e=>{e||o(null)},children:[(0,s.jsx)(p.zM,{asChild:!0,children:(0,s.jsxs)("div",{onClick:()=>o(e.id),className:`group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer ${"list"===g?"flex":""}`,children:[(0,s.jsxs)("div",{className:`relative overflow-hidden ${"list"===g?"w-32 aspect-[9/16]":"aspect-[9/16]"}`,children:[(0,s.jsx)(n.default,{src:e.thumbnail||"",alt:e.title,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center",children:(0,s.jsx)("div",{className:"bg-card/90 backdrop-blur-sm rounded-full p-3 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-primary ml-0.5",fill:"currentColor"})})}),e.featured&&(0,s.jsx)("div",{className:"absolute top-3 left-3",children:(0,s.jsx)(m.E,{variant:"secondary",className:"text-xs",children:"Featured"})}),(0,s.jsx)("div",{className:"absolute top-3 right-3",children:(0,s.jsx)("div",{className:`${j()} text-primary-foreground p-1.5 rounded-full`,children:b()})})]}),(0,s.jsxs)("div",{className:"p-4 flex-1",children:[(0,s.jsx)("h3",{className:`font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 ${"list"===g?"text-base line-clamp-2":"text-sm line-clamp-2"}`,children:e.title}),e.description&&(0,s.jsx)("p",{className:`text-muted-foreground leading-relaxed ${"list"===g?"text-sm line-clamp-3":"text-xs line-clamp-2"}`,children:e.description}),"list"===g&&(0,s.jsx)("div",{className:"mt-3",children:(0,s.jsx)(m.E,{variant:"outline",className:`text-xs ${j()} text-white border-none`,children:(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[b(),"YouTube Shorts"]})})})]})]})}),(0,s.jsx)(p.Cf,{className:"max-w-lg w-[95vw] p-0 bg-black border-0",children:(0,s.jsxs)("div",{className:"relative aspect-[9/16] max-h-[85vh] w-full",children:[(0,s.jsx)(p.HM,{asChild:!0,children:(0,s.jsx)(f.$,{variant:"ghost",size:"icon",className:"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2",children:(0,s.jsx)(c.A,{size:24})})}),r===e.id&&e.embedUrl&&(0,s.jsx)("iframe",{src:e.embedUrl,title:e.title,className:"w-full h-full rounded-lg",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})]})})]},e._id))})]})]})]})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),o=r(24224),i=r(4780);let n=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",success:"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 focus-visible:ring-success/20 dark:focus-visible:ring-success/40",warning:"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 focus-visible:ring-warning/20 dark:focus-visible:ring-warning/40",info:"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 focus-visible:ring-info/20 dark:focus-visible:ring-info/40"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...o}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(n({variant:t}),e),...o})}},97574:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),a=r(52343),o=r(68926),i=r(84712),n=r(33484),l=r(97868);let d={title:"YouTube Shorts - Creative Short-Form Content",description:"Watch all my YouTube Shorts showcasing dynamic editing, storytelling, and creative content. Professional short-form video portfolio by Uttam Rimal.",openGraph:{title:"YouTube Shorts - Uttam Rimal",description:"Watch all my YouTube Shorts showcasing dynamic editing, storytelling, and creative content. Professional short-form video portfolio by Uttam Rimal."}};async function c(){let[e,t]=await Promise.all([(0,a.OA)(),(0,a.GB)()]),r=e.map(e=>({...e,thumbnail:e.thumbnail||`https://img.youtube.com/vi/${e.id}/maxresdefault.jpg`})),d=t.map(e=>({...e,thumbnail:e.thumbnail||`https://img.youtube.com/vi/${e.id}/maxresdefault.jpg`}));return(0,s.jsxs)("main",{children:[(0,s.jsx)(o.default,{}),(0,s.jsx)(l.default,{allReels:r,featuredReels:d}),(0,s.jsx)(i.default,{}),(0,s.jsx)(n.default,{})]})}},97840:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},97868:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\uttam-portfolio\\\\portfolio\\\\src\\\\app\\\\reels\\\\ReelsPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\uttam-portfolio\\portfolio\\src\\app\\reels\\ReelsPageClient.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,176,474,908,849],()=>r(28370));module.exports=s})();